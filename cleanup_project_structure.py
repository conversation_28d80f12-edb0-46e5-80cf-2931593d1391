#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để cleanup và tổ chức lại cấu trúc dự án.
PHASE 1: Cleanup root level mess
"""

import os
import shutil
import json
from datetime import datetime

def create_new_structure():
    """Tạo cấu trúc thư mục mới."""
    print("📁 Creating new directory structure...")
    
    new_dirs = [
        "docs_consolidated",
        "tests_consolidated", 
        "config_consolidated",
        "scripts_consolidated",
        "archive"
    ]
    
    for dir_name in new_dirs:
        os.makedirs(dir_name, exist_ok=True)
        print(f"  ✅ Created: {dir_name}/")

def move_documentation_files():
    """Di chuyển tất cả documentation files vào docs_consolidated/."""
    print("\n📝 Moving documentation files...")
    
    doc_files = [
        "user_agent_manager_design.md", "MERGE_PLAN.md", "WebSearchAgentLocal_Implementation_Plan.md",
        "ENHANCED_SEARXNG_IMPLEMENTATION_SUMMARY.md", "HUONG_DAN_TIM_KIEM_TIENG_VIET.md",
        "pagination_handler_design.md", "PLANNING.md", "WebSearchAgentLocal_Tasks.md",
        "INSTALLATION.md", "adaptive_crawler_upgrade_status.md", "playwright_handler_design.md",
        "ADAPTIVE_CRAWLER_ANALYSIS_REPORT.md", "CONSOLIDATION_PLAN.md", "CONSOLIDATION_REPORT.md",
        "TASK_3_ADVANCED_FEATURES.md", "PROGRESS_SUMMARY.md", "WEBSITE_CRAWLER_README.md",
        "TASK_4_FINAL_SUMMARY.md", "SEARXNG_LOCAL_PRIORITY_IMPLEMENTATION.md",
        "README_ADAPTIVE_CRAWLER.md", "TASK_2_UTILS_COMPLETION.md", "ADAPTIVE_CRAWLER_UPGRADE_PLAN.md",
        "TASK_REPORT.md", "integration_summary.md", "WebSearchAgentLocal_Improvements.md",
        "example_crawl_results.md", "WebSearchAgentLocal_Advanced_Features_Guide.md",
        "TASK_1_COMPLETION_SUMMARY.md", "file_processor_design.md", "TASK_1_FINAL_VERIFICATION.md",
        "MISSING_FEATURES_ASSESSMENT.md", "ADAPTIVE_CRAWLER_IMPLEMENTATION_PLAN.md",
        "IMPLEMENTATION_REPORT.md", "DETAILED_COMPARISON_REPORT.md", "progress_summary.md",
        "ADAPTIVE_CRAWLER_MERGE_TASKS.md", "WebSearchAgentLocal_Additional_Features.md",
        "WebSearchAgentLocal_Improvements_Part2.md", "VIETNAMESE_SEARCH_README.md",
        "TASKS.md", "README_FEEDBACK.md", "ADAPTIVE_CRAWLER_MERGE_REPORT.md",
        "language_handler_design.md", "API_DOCUMENTATION.md", "WEBSITE_CRAWLER_WITH_DOWNLOAD_README.md",
        "deep_crawl_results.md", "IMPLEMENTATION_SUMMARY.md", "USAGE_EXAMPLES.md",
        "README_LLM_ANALYZER.md", "TASK_1_FEATURE_MAPPING_ASSESSMENT.md", "TASK_3_FINAL_SUMMARY.md",
        "README_WEBSEARCHAGENT_COMPREHENSIVE.md", "TASK_1_VERIFICATION_REPORT.md",
        "README_FAKE_NEWS_DETECTOR.md", "adaptive_crawler_upgrade_plan.md", "test_results.md",
        "config_manager_design.md", "task.md", "ADAPTIVE_CRAWLER_MERGE_PLAN.md",
        "TASK_4_TESTING_DOCUMENTATION.md", "README_MULTILINGUAL.md",
        "ADAPTIVE_CRAWLER_COMPREHENSIVE_ANALYSIS.md", "PERFORMANCE_IMPROVEMENTS.md",
        "README_UI.md", "captcha_handler_design.md", "WebSearchAgentLocal_Improvements_Part3.md",
        "TASK_3_COMPLETION_SUMMARY.md", "site_structure_handler_design.md", "TASK.md",
        "README_EVALUATION_FEATURES.md", "README_FIXES.md"
    ]
    
    moved_count = 0
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            try:
                shutil.move(doc_file, f"docs_consolidated/{doc_file}")
                moved_count += 1
                print(f"  📄 Moved: {doc_file}")
            except Exception as e:
                print(f"  ❌ Error moving {doc_file}: {e}")
    
    print(f"  ✅ Moved {moved_count} documentation files")

def move_test_files():
    """Di chuyển test files vào tests_consolidated/."""
    print("\n🧪 Moving test files...")
    
    test_files = [f for f in os.listdir(".") if f.startswith("test_") and f.endswith(".py")]
    
    moved_count = 0
    for test_file in test_files:
        try:
            shutil.move(test_file, f"tests_consolidated/{test_file}")
            moved_count += 1
            print(f"  🧪 Moved: {test_file}")
        except Exception as e:
            print(f"  ❌ Error moving {test_file}: {e}")
    
    print(f"  ✅ Moved {moved_count} test files")

def move_config_files():
    """Di chuyển config files vào config_consolidated/."""
    print("\n⚙️ Moving config files...")
    
    config_files = [
        "requirements_ui.txt", "requirements-credibility.txt", "requirements-core.txt",
        "requirements-llm.txt", "requirements.txt"
    ]
    
    moved_count = 0
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                shutil.move(config_file, f"config_consolidated/{config_file}")
                moved_count += 1
                print(f"  ⚙️ Moved: {config_file}")
            except Exception as e:
                print(f"  ❌ Error moving {config_file}: {e}")
    
    print(f"  ✅ Moved {moved_count} config files")

def move_utility_scripts():
    """Di chuyển utility scripts vào scripts_consolidated/."""
    print("\n🔧 Moving utility scripts...")
    
    # Scripts that are not part of main codebase
    utility_scripts = [
        "performance_optimization.py", "performance_measurement.py", "website_crawler.py",
        "website_crawler_with_download.py", "fix_web_search_agent_local_merged.py",
        "comprehensive_test_web_search_agent.py", "quick_searxng_check.py", "detailed_test.py",
        "quick_search_test.py", "simple_test.py", "minimal_test.py", "simple_search_test.py",
        "direct_test.py", "check_circular_imports.py", "typestubs.py", "demo_evaluation_features.py",
        "final_production_readiness_check.py", "analyze_project_structure.py", "restructure_phase1.py"
    ]
    
    moved_count = 0
    for script in utility_scripts:
        if os.path.exists(script):
            try:
                shutil.move(script, f"scripts_consolidated/{script}")
                moved_count += 1
                print(f"  🔧 Moved: {script}")
            except Exception as e:
                print(f"  ❌ Error moving {script}: {e}")
    
    print(f"  ✅ Moved {moved_count} utility scripts")

def clean_json_and_temp_files():
    """Dọn dẹp các file JSON và temp files."""
    print("\n🧹 Cleaning JSON and temp files...")
    
    json_files = [f for f in os.listdir(".") if f.endswith(".json") and f != "project_analysis_report.json"]
    txt_files = [f for f in os.listdir(".") if f.endswith(".txt") and f not in ["requirements.txt"]]
    
    cleaned_count = 0
    for file in json_files + txt_files:
        try:
            os.remove(file)
            cleaned_count += 1
            print(f"  🗑️ Removed: {file}")
        except Exception as e:
            print(f"  ❌ Error removing {file}: {e}")
    
    print(f"  ✅ Cleaned {cleaned_count} temp files")

def create_main_readme():
    """Tạo README.md chính mới."""
    print("\n📄 Creating new main README.md...")
    
    readme_content = """# Deep Research Core

## 🎯 Giới thiệu

Deep Research Core là thư viện Python mạnh mẽ cho việc tìm kiếm, thu thập và phân tích thông tin từ web với khả năng đánh giá độ tin cậy.

## 🏗️ Cấu trúc dự án

```
deep_research_core_1/
├── 📁 src/deep_research_core/     # MAIN CODEBASE
│   ├── agents/                    # Web search agents
│   ├── utils/                     # Utilities & helpers  
│   ├── credibility/               # Credibility evaluation
│   └── ...
├── 📁 docs_consolidated/          # All documentation
├── 📁 tests_consolidated/         # All test files
├── 📁 config_consolidated/        # Configuration files
├── 📁 scripts_consolidated/       # Utility scripts
└── 📁 archive/                    # Archived legacy code
```

## 🚀 Tính năng chính

- **WebSearchAgentLocalMerged**: Agent tìm kiếm web với đánh giá độ tin cậy
- **AdaptiveCrawlerConsolidatedMerged**: Crawler thích ứng cho nhiều loại website
- **Vietnamese Search Integration**: Hỗ trợ tìm kiếm tiếng Việt chuyên sâu
- **Credibility Evaluation**: Đánh giá độ tin cậy của nguồn thông tin
- **Content Extraction**: Trích xuất nội dung thông minh

## 📖 Documentation

Xem thêm tài liệu chi tiết trong thư mục `docs_consolidated/`:
- [Installation Guide](docs_consolidated/INSTALLATION.md)
- [Usage Examples](docs_consolidated/USAGE_EXAMPLES.md)
- [API Documentation](docs_consolidated/API_DOCUMENTATION.md)

## 🧪 Testing

Chạy tests:
```bash
cd tests_consolidated/
python -m pytest
```

## 📝 License

MIT License - xem file LICENSE để biết thêm chi tiết.
"""
    
    # Backup old README if exists
    if os.path.exists("README.md"):
        shutil.move("README.md", "docs_consolidated/README_OLD.md")
        print("  📄 Backed up old README.md")
    
    with open("README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("  ✅ Created new main README.md")

def generate_cleanup_report():
    """Tạo báo cáo cleanup."""
    print("\n📊 Generating cleanup report...")
    
    # Count files in new structure
    new_structure = {}
    for dir_name in ["docs_consolidated", "tests_consolidated", "config_consolidated", "scripts_consolidated"]:
        if os.path.exists(dir_name):
            files = os.listdir(dir_name)
            new_structure[dir_name] = len(files)
    
    # Count remaining root files
    root_files = [f for f in os.listdir(".") if os.path.isfile(f)]
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "cleanup_summary": {
            "docs_moved": new_structure.get("docs_consolidated", 0),
            "tests_moved": new_structure.get("tests_consolidated", 0), 
            "configs_moved": new_structure.get("config_consolidated", 0),
            "scripts_moved": new_structure.get("scripts_consolidated", 0),
            "remaining_root_files": len(root_files)
        },
        "remaining_files": root_files,
        "new_structure": new_structure
    }
    
    with open("cleanup_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("  ✅ Cleanup report saved: cleanup_report.json")
    return report

def main():
    """Main cleanup function."""
    print("🧹 PROJECT STRUCTURE CLEANUP")
    print("=" * 50)
    
    # Step 1: Create new structure
    create_new_structure()
    
    # Step 2: Move documentation
    move_documentation_files()
    
    # Step 3: Move test files
    move_test_files()
    
    # Step 4: Move config files
    move_config_files()
    
    # Step 5: Move utility scripts
    move_utility_scripts()
    
    # Step 6: Clean temp files
    clean_json_and_temp_files()
    
    # Step 7: Create new README
    create_main_readme()
    
    # Step 8: Generate report
    report = generate_cleanup_report()
    
    print("\n" + "=" * 50)
    print("✅ CLEANUP COMPLETED!")
    print(f"  📝 Documentation files moved: {report['cleanup_summary']['docs_moved']}")
    print(f"  🧪 Test files moved: {report['cleanup_summary']['tests_moved']}")
    print(f"  ⚙️ Config files moved: {report['cleanup_summary']['configs_moved']}")
    print(f"  🔧 Script files moved: {report['cleanup_summary']['scripts_moved']}")
    print(f"  📁 Remaining root files: {report['cleanup_summary']['remaining_root_files']}")
    print("\n🎯 Next steps:")
    print("  1. Review the new structure")
    print("  2. Test main functionality")
    print("  3. Archive deepresearch/ directory")

if __name__ == "__main__":
    main()
