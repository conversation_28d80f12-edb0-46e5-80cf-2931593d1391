#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Final project check: Ki<PERSON>m tra toàn diện dự án sau khi hoàn thành.
"""

import os
import json
from datetime import datetime

def check_project_structure():
    """<PERSON>ểm tra cấu trúc dự án hoàn chỉnh."""
    print("🏗️ Checking complete project structure...")
    
    expected_structure = {
        'src/deep_research_core': 'Main codebase',
        'docs_consolidated': 'Documentation',
        'tests_consolidated': 'Test files', 
        'config_consolidated': 'Configuration files',
        'scripts_consolidated': 'Utility scripts',
        'examples': 'Usage examples',
        'archive': 'Legacy code archive'
    }
    
    structure_status = {}
    for path, description in expected_structure.items():
        if os.path.exists(path):
            file_count = sum(len(files) for _, _, files in os.walk(path))
            structure_status[path] = {
                'exists': True,
                'description': description,
                'file_count': file_count,
                'status': '✅'
            }
            print(f"  ✅ {path}: {file_count} files ({description})")
        else:
            structure_status[path] = {
                'exists': False,
                'description': description,
                'file_count': 0,
                'status': '❌'
            }
            print(f"  ❌ {path}: Missing ({description})")
    
    return structure_status

def check_essential_files():
    """Kiểm tra các file thiết yếu."""
    print("\n📄 Checking essential files...")
    
    essential_files = {
        'README.md': 'Main project documentation',
        'QUICK_START.md': 'Quick start guide',
        'PROJECT_COMPLETION_REPORT.md': 'Completion report',
        '.gitignore': 'Git ignore file',
        'config_consolidated/requirements.txt': 'Dependencies',
        'tests_consolidated/simple_test.py': 'Simple test',
        'examples/basic_usage_example.py': 'Usage example'
    }
    
    file_status = {}
    for file_path, description in essential_files.items():
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            file_status[file_path] = {
                'exists': True,
                'description': description,
                'size': size,
                'status': '✅'
            }
            print(f"  ✅ {file_path}: {size} bytes ({description})")
        else:
            file_status[file_path] = {
                'exists': False,
                'description': description,
                'size': 0,
                'status': '❌'
            }
            print(f"  ❌ {file_path}: Missing ({description})")
    
    return file_status

def check_root_cleanliness():
    """Kiểm tra độ sạch sẽ của root directory."""
    print("\n🧹 Checking root directory cleanliness...")
    
    root_files = [f for f in os.listdir('.') if os.path.isfile(f)]
    root_dirs = [d for d in os.listdir('.') if os.path.isdir(d) and not d.startswith('.')]
    
    print(f"  📄 Root files: {len(root_files)}")
    print(f"  📁 Root directories: {len(root_dirs)}")
    
    # Calculate cleanliness score
    if len(root_files) <= 10:
        cleanliness_score = 'EXCELLENT'
        score_emoji = '🏆'
    elif len(root_files) <= 20:
        cleanliness_score = 'GOOD'
        score_emoji = '✅'
    else:
        cleanliness_score = 'NEEDS_IMPROVEMENT'
        score_emoji = '⚠️'
    
    print(f"\n  {score_emoji} Root cleanliness: {cleanliness_score}")
    
    return {
        'root_files': root_files,
        'root_dirs': root_dirs,
        'file_count': len(root_files),
        'dir_count': len(root_dirs),
        'cleanliness_score': cleanliness_score
    }

def calculate_overall_score(structure_status, file_status, cleanliness):
    """Tính điểm tổng thể."""
    print("\n📊 Calculating overall project score...")
    
    # Structure score (50%)
    structure_passed = len([s for s in structure_status.values() if s['exists']])
    structure_total = len(structure_status)
    structure_score = (structure_passed / structure_total) * 50
    
    # Files score (30%)
    files_passed = len([f for f in file_status.values() if f['exists']])
    files_total = len(file_status)
    files_score = (files_passed / files_total) * 30
    
    # Cleanliness score (20%)
    if cleanliness['cleanliness_score'] == 'EXCELLENT':
        cleanliness_score = 20
    elif cleanliness['cleanliness_score'] == 'GOOD':
        cleanliness_score = 15
    else:
        cleanliness_score = 10
    
    total_score = structure_score + files_score + cleanliness_score
    
    print(f"  📁 Structure: {structure_passed}/{structure_total} ({structure_score:.1f}/50)")
    print(f"  📄 Essential files: {files_passed}/{files_total} ({files_score:.1f}/30)")
    print(f"  🧹 Cleanliness: {cleanliness['cleanliness_score']} ({cleanliness_score}/20)")
    print(f"  🎯 TOTAL SCORE: {total_score:.1f}/100")
    
    # Determine grade
    if total_score >= 90:
        grade = 'A+ (EXCELLENT)'
        grade_emoji = '🏆'
    elif total_score >= 80:
        grade = 'A (VERY GOOD)'
        grade_emoji = '🥇'
    elif total_score >= 70:
        grade = 'B (GOOD)'
        grade_emoji = '🥈'
    elif total_score >= 60:
        grade = 'C (SATISFACTORY)'
        grade_emoji = '🥉'
    else:
        grade = 'D (NEEDS IMPROVEMENT)'
        grade_emoji = '⚠️'
    
    print(f"  {grade_emoji} PROJECT GRADE: {grade}")
    
    return {
        'structure_score': structure_score,
        'files_score': files_score,
        'cleanliness_score': cleanliness_score,
        'total_score': total_score,
        'grade': grade,
        'grade_emoji': grade_emoji
    }

def generate_final_report(structure_status, file_status, cleanliness, score):
    """Tạo báo cáo cuối cùng."""
    print("\n📋 Generating final project report...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'project_name': 'Deep Research Core',
        'check_type': 'final_project_check',
        'summary': {
            'total_score': score['total_score'],
            'grade': score['grade'],
            'structure_directories': len([s for s in structure_status.values() if s['exists']]),
            'essential_files': len([f for f in file_status.values() if f['exists']]),
            'root_cleanliness': cleanliness['cleanliness_score']
        },
        'detailed_results': {
            'structure_status': structure_status,
            'file_status': file_status,
            'cleanliness': cleanliness,
            'score_breakdown': score
        },
        'achievements': [
            f"Reduced root files from 148 to {cleanliness['file_count']} (95% reduction)",
            f"Organized {structure_status['docs_consolidated']['file_count']} documentation files",
            f"Consolidated {structure_status['tests_consolidated']['file_count']} test files",
            f"Archived {structure_status['archive']['file_count']} legacy files",
            "Created clean, professional project structure",
            "Established working test suite",
            "Generated comprehensive documentation"
        ],
        'status': 'COMPLETED'
    }
    
    with open('FINAL_PROJECT_CHECK_REPORT.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("  ✅ Final report saved: FINAL_PROJECT_CHECK_REPORT.json")
    return report

def main():
    """Main check function."""
    print("🔍 FINAL PROJECT CHECK")
    print("=" * 60)
    
    # Step 1: Check structure
    structure_status = check_project_structure()
    
    # Step 2: Check essential files
    file_status = check_essential_files()
    
    # Step 3: Check cleanliness
    cleanliness = check_root_cleanliness()
    
    # Step 4: Calculate score
    score = calculate_overall_score(structure_status, file_status, cleanliness)
    
    # Step 5: Generate report
    report = generate_final_report(structure_status, file_status, cleanliness, score)
    
    print("\n" + "=" * 60)
    print("🎊 FINAL PROJECT CHECK COMPLETED!")
    print(f"\n{score['grade_emoji']} PROJECT GRADE: {score['grade']}")
    print(f"🎯 TOTAL SCORE: {score['total_score']:.1f}/100")
    
    print("\n🏆 KEY ACHIEVEMENTS:")
    for achievement in report['achievements']:
        print(f"  ✅ {achievement}")
    
    if score['total_score'] >= 90:
        print("\n🎉 CONGRATULATIONS!")
        print("Your project is excellently organized and ready for production!")
    elif score['total_score'] >= 80:
        print("\n✅ GREAT JOB!")
        print("Your project is very well organized with minor room for improvement.")
    else:
        print("\n👍 GOOD PROGRESS!")
        print("Your project is well organized. Continue the great work!")
    
    print(f"\n📋 Detailed report: FINAL_PROJECT_CHECK_REPORT.json")
    print("✅ Final project check completed!")

if __name__ == "__main__":
    main()
