# Deep Research Core

## 🎯 Giới thiệu

Deep Research Core là thư viện Python mạnh mẽ cho việc tìm kiếm, thu thập và phân tích thông tin từ web với khả năng đánh giá độ tin cậy.

## 🏗️ Cấu trúc dự án

```
deep_research_core_1/
├── 📁 src/deep_research_core/     # MAIN CODEBASE
│   ├── agents/                    # Web search agents
│   ├── utils/                     # Utilities & helpers  
│   ├── credibility/               # Credibility evaluation
│   └── ...
├── 📁 docs_consolidated/          # All documentation
├── 📁 tests_consolidated/         # All test files
├── 📁 config_consolidated/        # Configuration files
├── 📁 scripts_consolidated/       # Utility scripts
├── 📁 examples/                   # Usage examples
└── 📁 archive/                    # Archived legacy code
```

## 🚀 Quick Start

### 1. Cài đặt Dependencies

```bash
pip install -r config_consolidated/requirements.txt
```

### 2. Basic Usage

```python
import sys
sys.path.append('src')

# Example sẽ được cập nhật khi dependencies được fix
print("Deep Research Core - Ready to use!")
```

### 3. Chạy Tests

```bash
cd tests_consolidated/
python simple_test.py
```

### 4. Xem Examples

```bash
cd examples/
python basic_usage_example.py
```

## 📖 Documentation

- [Quick Start Guide](QUICK_START.md)
- [Installation Guide](docs_consolidated/INSTALLATION.md)
- [Usage Examples](docs_consolidated/USAGE_EXAMPLES.md)
- [API Documentation](docs_consolidated/API_DOCUMENTATION.md)

## 🧪 Testing

```bash
# Simple test
cd tests_consolidated/
python simple_test.py

# Full test suite (when dependencies are installed)
python -m pytest
```

## 🔧 Troubleshooting

### Import Errors
```python
import sys
sys.path.append('src')
```

### Missing Dependencies
```bash
pip install requests beautifulsoup4 lxml
```

## 📝 Project Status

✅ **COMPLETED**: Project structure reorganization
✅ **COMPLETED**: Documentation consolidation  
✅ **COMPLETED**: Test file organization
⚠️ **IN PROGRESS**: Dependency optimization
⚠️ **IN PROGRESS**: Import path fixes

## 📞 Support

Xem thêm tài liệu trong `docs_consolidated/` để biết chi tiết.

## 📝 License

MIT License - xem file LICENSE để biết thêm chi tiết.
