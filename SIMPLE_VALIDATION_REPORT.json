{"timestamp": "2025-05-28T20:24:15.242897", "validation_type": "simple_structure_check", "summary": {"structure_dirs_ok": 6, "structure_dirs_total": 6, "main_files_ok": 4, "main_files_total": 4, "root_files": 12, "requirements_files_ok": 4, "requirements_files_total": 4}, "details": {"structure_check": {"src/deep_research_core": {"exists": true, "file_count": 215}, "docs_consolidated": {"exists": true, "file_count": 72}, "tests_consolidated": {"exists": true, "file_count": 42}, "config_consolidated": {"exists": true, "file_count": 5}, "scripts_consolidated": {"exists": true, "file_count": 21}, "archive": {"exists": true, "file_count": 65251}}, "file_check": {"README.md": {"exists": true, "size": 1751}, "src/deep_research_core/agents/web_search_agent_local_merged.py": {"exists": true, "size": 268426}, "src/deep_research_core/agents/adaptive_crawler_consolidated_merged.py": {"exists": true, "size": 186376}, "config_consolidated/requirements.txt": {"exists": true, "size": 467}}, "root_analysis": {"root_files": ["POST_RESTRUCTURE_VALIDATION_REPORT.json", "simple_validation.py", "post_restructure_validation.py", ".giti<PERSON>re", "final_cleanup.py", "QUICK_START.md", "cleanup_report.json", "project_analysis_report.json", "cleanup_project_structure.py", "README.md", "PROJECT_RESTRUCTURE_PLAN.md", "RESTRUCTURE_FINAL_SUMMARY.json"], "root_dirs": ["thuvienphapluat_docs", "docs_consolidated", "archive", "__pycache__", "docs", "examples", "test_downloads", "tools", "temp_test_dir", "src", "config_consolidated", "tasks", "static", "scripts_consolidated", "results", "tests", "data", "tests_consolidated", "config", "backup", "test_results"], "root_file_count": 12, "root_dir_count": 21}, "requirements_check": {"config_consolidated/requirements.txt": {"exists": true, "package_count": 11, "sample_packages": ["requests>=2.25.0", "beautifulsoup4>=4.9.3", "urllib3>=1.26.0"]}, "config_consolidated/requirements-core.txt": {"exists": true, "package_count": 11, "sample_packages": ["requests>=2.28.0", "beautifulsoup4>=4.11.0", "lxml>=4.9.0"]}, "config_consolidated/requirements-credibility.txt": {"exists": true, "package_count": 5, "sample_packages": ["nltk>=3.7", "textblob>=0.17.1", "scikit-learn>=1.1.1"]}, "config_consolidated/requirements-llm.txt": {"exists": true, "package_count": 6, "sample_packages": ["transformers>=4.30.0", "torch>=2.0.0", "tokenizers>=0.13.3"]}}}, "status": "COMPLETED"}