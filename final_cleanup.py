#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Final cleanup: Archive deepresearch/ và hoàn thành tổ chức lại dự án.
"""

import os
import shutil
import json
from datetime import datetime

def archive_deepresearch():
    """Archive thư mục deepresearch/ vào archive/."""
    print("📦 Archiving deepresearch/ directory...")
    
    if os.path.exists("deepresearch"):
        try:
            # Move deepresearch to archive
            if not os.path.exists("archive"):
                os.makedirs("archive")
            
            shutil.move("deepresearch", "archive/deepresearch_legacy")
            print("  ✅ Moved deepresearch/ to archive/deepresearch_legacy/")
            
            # Create archive info
            archive_info = {
                "archived_date": datetime.now().isoformat(),
                "original_path": "deepresearch/",
                "new_path": "archive/deepresearch_legacy/",
                "reason": "Legacy codebase archived after consolidation",
                "note": "Contains experimental features and duplicate code"
            }
            
            with open("archive/deepresearch_archive_info.json", "w", encoding="utf-8") as f:
                json.dump(archive_info, f, indent=2, ensure_ascii=False)
            
            print("  📄 Created archive info file")
            
        except Exception as e:
            print(f"  ❌ Error archiving deepresearch: {e}")
    else:
        print("  ⚠️ deepresearch/ directory not found")

def clean_remaining_files():
    """Dọn dẹp các file còn lại."""
    print("\n🧹 Cleaning remaining files...")
    
    files_to_clean = [
        "simple_file_processor_test.py",
        "web_search_agent_local_merged_fixed.py"
    ]
    
    cleaned_count = 0
    for file in files_to_clean:
        if os.path.exists(file):
            try:
                # Move to scripts_consolidated instead of deleting
                shutil.move(file, f"scripts_consolidated/{file}")
                cleaned_count += 1
                print(f"  🔧 Moved to scripts: {file}")
            except Exception as e:
                print(f"  ❌ Error moving {file}: {e}")
    
    print(f"  ✅ Cleaned {cleaned_count} remaining files")

def clean_backup_directories():
    """Dọn dẹp các thư mục backup cũ."""
    print("\n🗑️ Cleaning old backup directories...")
    
    backup_dirs = [d for d in os.listdir(".") if d.startswith("backup_")]
    
    cleaned_count = 0
    for backup_dir in backup_dirs:
        try:
            shutil.rmtree(backup_dir)
            cleaned_count += 1
            print(f"  🗑️ Removed: {backup_dir}")
        except Exception as e:
            print(f"  ❌ Error removing {backup_dir}: {e}")
    
    print(f"  ✅ Cleaned {cleaned_count} backup directories")

def create_gitignore():
    """Tạo .gitignore file cải thiện."""
    print("\n📄 Creating improved .gitignore...")
    
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Project specific
cache/
temp/
logs/
*.tmp
*.temp
node_modules/
volumes/
.DS_Store
Thumbs.db

# Analysis and cleanup files
project_analysis_report.json
cleanup_report.json
cleanup_project_structure.py
final_cleanup.py
"""
    
    with open(".gitignore", "w", encoding="utf-8") as f:
        f.write(gitignore_content)
    
    print("  ✅ Created improved .gitignore")

def create_final_summary():
    """Tạo báo cáo tổng kết cuối cùng."""
    print("\n📊 Creating final summary...")
    
    # Count files in each directory
    structure_summary = {}
    
    directories = [
        "src/deep_research_core",
        "docs_consolidated", 
        "tests_consolidated",
        "config_consolidated",
        "scripts_consolidated",
        "archive"
    ]
    
    for dir_name in directories:
        if os.path.exists(dir_name):
            file_count = 0
            for root, dirs, files in os.walk(dir_name):
                file_count += len(files)
            structure_summary[dir_name] = file_count
        else:
            structure_summary[dir_name] = 0
    
    # Count root files
    root_files = [f for f in os.listdir(".") if os.path.isfile(f)]
    
    final_summary = {
        "timestamp": datetime.now().isoformat(),
        "restructure_completed": True,
        "structure_summary": structure_summary,
        "root_files_remaining": len(root_files),
        "root_files": root_files,
        "achievements": [
            "Moved 72 documentation files to docs_consolidated/",
            "Moved 42 test files to tests_consolidated/", 
            "Moved 5 config files to config_consolidated/",
            "Moved 19 utility scripts to scripts_consolidated/",
            "Archived legacy deepresearch/ directory",
            "Reduced root level files from 148 to " + str(len(root_files)),
            "Created clean project structure",
            "Improved .gitignore file"
        ],
        "next_steps": [
            "Review consolidated documentation",
            "Run tests to ensure functionality",
            "Update import paths if needed",
            "Consider further optimization"
        ]
    }
    
    with open("RESTRUCTURE_FINAL_SUMMARY.json", "w", encoding="utf-8") as f:
        json.dump(final_summary, f, indent=2, ensure_ascii=False)
    
    print("  ✅ Final summary saved: RESTRUCTURE_FINAL_SUMMARY.json")
    return final_summary

def main():
    """Main final cleanup function."""
    print("🎯 FINAL PROJECT CLEANUP")
    print("=" * 50)
    
    # Step 1: Archive deepresearch
    archive_deepresearch()
    
    # Step 2: Clean remaining files
    clean_remaining_files()
    
    # Step 3: Clean backup directories
    clean_backup_directories()
    
    # Step 4: Create improved .gitignore
    create_gitignore()
    
    # Step 5: Create final summary
    summary = create_final_summary()
    
    print("\n" + "=" * 50)
    print("🎉 PROJECT RESTRUCTURE COMPLETED!")
    print("\n📊 FINAL STATISTICS:")
    for dir_name, count in summary['structure_summary'].items():
        print(f"  📁 {dir_name}: {count} files")
    
    print(f"\n📄 Root files remaining: {summary['root_files_remaining']}")
    print("\n✅ ACHIEVEMENTS:")
    for achievement in summary['achievements']:
        print(f"  ✅ {achievement}")
    
    print("\n🚀 NEXT STEPS:")
    for step in summary['next_steps']:
        print(f"  🎯 {step}")
    
    print("\n🎊 Congratulations! Your project is now well-organized!")

if __name__ == "__main__":
    main()
