"""
Script tối ưu hóa hiệu suất của WebSearchAgentLocal và AdaptiveCrawler sau khi tích hợp tất cả các module dùng chung.
"""

import os
import sys
import json
import logging
import argparse
from typing import Dict, List, Any, Optional, Tuple

# Thêm thư mục gốc vào sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Import các module cần thiết
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
from src.deep_research_core.crawlers.adaptive_crawler import AdaptiveCrawler
from src.deep_research_core.utils.shared.all_modules_integration import integrate_all_modules

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("performance_optimization.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_performance_results(filename: str) -> Dict[str, Any]:
    """
    Tải kết quả đo hiệu suất từ file.
    
    Args:
        filename: Tên file để tải kết quả
        
    Returns:
        Dict[str, Any]: Kết quả đo hiệu suất
    """
    with open(filename, "r", encoding="utf-8") as f:
        results = json.load(f)
    
    logger.info(f"Đã tải kết quả đo hiệu suất từ file {filename}")
    
    return results

def optimize_web_search_agent(
    comparison_results: Dict[str, Any],
    optimization_threshold: float = -5.0
) -> Dict[str, Any]:
    """
    Tối ưu hóa hiệu suất của WebSearchAgentLocal.
    
    Args:
        comparison_results: Kết quả so sánh hiệu suất
        optimization_threshold: Ngưỡng tối ưu hóa (phần trăm)
        
    Returns:
        Dict[str, Any]: Cấu hình tối ưu
    """
    logger.info("Bắt đầu tối ưu hóa hiệu suất WebSearchAgentLocal")
    
    # Kiểm tra xem có cần tối ưu hóa không
    if comparison_results["difference"]["average_time_percent"] <= optimization_threshold:
        logger.info(f"Hiệu suất giảm {comparison_results['difference']['average_time_percent']:.2f}%, cần tối ưu hóa")
        
        # Cấu hình tối ưu
        optimized_config = {
            "captcha_handler": {
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
                "use_playwright": False,  # Tắt Playwright nếu không cần thiết
                "browser_emulation": False,  # Tắt giả lập trình duyệt nếu không cần thiết
            },
            "user_agent_manager": {
                "cache_enabled": True,
                "rotation_strategy": "round_robin",  # Sử dụng round_robin thay vì random
                "rotation_interval": 10,  # Tăng khoảng thời gian xoay vòng
            },
            "file_processor": {
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
                "extract_text_from_images": False,  # Tắt trích xuất văn bản từ hình ảnh
                "extract_metadata": False,  # Tắt trích xuất metadata
            },
            "playwright_handler": {
                "headless": True,
                "stealth_mode": False,  # Tắt stealth mode nếu không cần thiết
                "handle_infinite_scroll": False,  # Tắt xử lý infinite scroll nếu không cần thiết
                "handle_forms": False,  # Tắt xử lý form nếu không cần thiết
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
            },
            "site_structure_handler": {
                "respect_robots": False,  # Tắt respect robots.txt nếu không cần thiết
                "use_sitemap": False,  # Tắt sử dụng sitemap nếu không cần thiết
                "extract_metadata": False,  # Tắt trích xuất metadata
                "extract_images": False,  # Tắt trích xuất hình ảnh
                "extract_files": False,  # Tắt trích xuất file
                "extract_structured_data": False,  # Tắt trích xuất dữ liệu có cấu trúc
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
            },
            "pagination_handler": {
                "detect_infinite_scroll": False,  # Tắt phát hiện infinite scroll
                "detect_load_more": False,  # Tắt phát hiện nút "Tải thêm"
                "detect_ajax_pagination": False,  # Tắt phát hiện phân trang AJAX
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
            },
            "language_handler": {
                "enable_dialect_detection": False,  # Tắt phát hiện phương ngữ
                "enable_translation": False,  # Tắt dịch thuật
                "enable_text_normalization": False,  # Tắt chuẩn hóa văn bản
                "enable_keyword_extraction": False,  # Tắt trích xuất từ khóa
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
            },
        }
        
        logger.info("Đã tạo cấu hình tối ưu cho WebSearchAgentLocal")
    else:
        logger.info(f"Hiệu suất tăng {-comparison_results['difference']['average_time_percent']:.2f}%, không cần tối ưu hóa")
        
        # Không cần tối ưu hóa
        optimized_config = {}
    
    return optimized_config

def optimize_adaptive_crawler(
    comparison_results: Dict[str, Any],
    optimization_threshold: float = -5.0
) -> Dict[str, Any]:
    """
    Tối ưu hóa hiệu suất của AdaptiveCrawler.
    
    Args:
        comparison_results: Kết quả so sánh hiệu suất
        optimization_threshold: Ngưỡng tối ưu hóa (phần trăm)
        
    Returns:
        Dict[str, Any]: Cấu hình tối ưu
    """
    logger.info("Bắt đầu tối ưu hóa hiệu suất AdaptiveCrawler")
    
    # Kiểm tra xem có cần tối ưu hóa không
    if comparison_results["difference"]["average_time_percent"] <= optimization_threshold:
        logger.info(f"Hiệu suất giảm {comparison_results['difference']['average_time_percent']:.2f}%, cần tối ưu hóa")
        
        # Cấu hình tối ưu
        optimized_config = {
            "captcha_handler": {
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
                "use_playwright": False,  # Tắt Playwright nếu không cần thiết
                "browser_emulation": False,  # Tắt giả lập trình duyệt nếu không cần thiết
            },
            "user_agent_manager": {
                "cache_enabled": True,
                "rotation_strategy": "round_robin",  # Sử dụng round_robin thay vì random
                "rotation_interval": 10,  # Tăng khoảng thời gian xoay vòng
            },
            "file_processor": {
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
                "extract_text_from_images": False,  # Tắt trích xuất văn bản từ hình ảnh
                "extract_metadata": False,  # Tắt trích xuất metadata
            },
            "playwright_handler": {
                "headless": True,
                "stealth_mode": False,  # Tắt stealth mode nếu không cần thiết
                "handle_infinite_scroll": False,  # Tắt xử lý infinite scroll nếu không cần thiết
                "handle_forms": False,  # Tắt xử lý form nếu không cần thiết
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
            },
            "site_structure_handler": {
                "respect_robots": False,  # Tắt respect robots.txt nếu không cần thiết
                "use_sitemap": False,  # Tắt sử dụng sitemap nếu không cần thiết
                "extract_metadata": False,  # Tắt trích xuất metadata
                "extract_images": False,  # Tắt trích xuất hình ảnh
                "extract_files": False,  # Tắt trích xuất file
                "extract_structured_data": False,  # Tắt trích xuất dữ liệu có cấu trúc
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
            },
            "pagination_handler": {
                "detect_infinite_scroll": False,  # Tắt phát hiện infinite scroll
                "detect_load_more": False,  # Tắt phát hiện nút "Tải thêm"
                "detect_ajax_pagination": False,  # Tắt phát hiện phân trang AJAX
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
            },
            "language_handler": {
                "enable_dialect_detection": False,  # Tắt phát hiện phương ngữ
                "enable_translation": False,  # Tắt dịch thuật
                "enable_text_normalization": False,  # Tắt chuẩn hóa văn bản
                "enable_keyword_extraction": False,  # Tắt trích xuất từ khóa
                "cache_enabled": True,
                "cache_ttl": 3600 * 24,  # 1 day
            },
        }
        
        logger.info("Đã tạo cấu hình tối ưu cho AdaptiveCrawler")
    else:
        logger.info(f"Hiệu suất tăng {-comparison_results['difference']['average_time_percent']:.2f}%, không cần tối ưu hóa")
        
        # Không cần tối ưu hóa
        optimized_config = {}
    
    return optimized_config

def save_optimized_config(config: Dict[str, Any], filename: str) -> None:
    """
    Lưu cấu hình tối ưu vào file.
    
    Args:
        config: Cấu hình tối ưu
        filename: Tên file để lưu cấu hình
    """
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Đã lưu cấu hình tối ưu vào file {filename}")

def main():
    """
    Hàm chính để tối ưu hóa hiệu suất.
    """
    # Phân tích tham số dòng lệnh
    parser = argparse.ArgumentParser(description="Tối ưu hóa hiệu suất của WebSearchAgentLocal và AdaptiveCrawler")
    parser.add_argument("--web-search-comparison", help="File kết quả so sánh hiệu suất WebSearchAgentLocal")
    parser.add_argument("--crawler-comparison", help="File kết quả so sánh hiệu suất AdaptiveCrawler")
    parser.add_argument("--optimization-threshold", type=float, default=-5.0, help="Ngưỡng tối ưu hóa (phần trăm)")
    parser.add_argument("--output-dir", default="optimized_configs", help="Thư mục để lưu cấu hình tối ưu")
    args = parser.parse_args()
    
    # Tạo thư mục để lưu cấu hình tối ưu nếu chưa tồn tại
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Tối ưu hóa hiệu suất WebSearchAgentLocal
    if args.web_search_comparison:
        # Tải kết quả so sánh hiệu suất
        web_search_comparison = load_performance_results(args.web_search_comparison)
        
        # Tối ưu hóa hiệu suất
        web_search_optimized_config = optimize_web_search_agent(
            web_search_comparison,
            args.optimization_threshold
        )
        
        # Lưu cấu hình tối ưu
        if web_search_optimized_config:
            save_optimized_config(
                web_search_optimized_config,
                os.path.join(args.output_dir, "web_search_optimized_config.json")
            )
    
    # Tối ưu hóa hiệu suất AdaptiveCrawler
    if args.crawler_comparison:
        # Tải kết quả so sánh hiệu suất
        crawler_comparison = load_performance_results(args.crawler_comparison)
        
        # Tối ưu hóa hiệu suất
        crawler_optimized_config = optimize_adaptive_crawler(
            crawler_comparison,
            args.optimization_threshold
        )
        
        # Lưu cấu hình tối ưu
        if crawler_optimized_config:
            save_optimized_config(
                crawler_optimized_config,
                os.path.join(args.output_dir, "crawler_optimized_config.json")
            )

if __name__ == "__main__":
    main()
