#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Website Crawler - Công cụ để crawl toàn bộ một website.
Sử dụng AdaptiveCrawler từ module deepresearch.
"""

import os
import sys
import time
import json
import argparse
import logging
from urllib.parse import urlparse
from datetime import datetime

# Thêm thư mục hiện tại vào đường dẫn Python
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# Import AdaptiveCrawler
from deepresearch.adaptive_crawler import AdaptiveCrawler

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_domain(url):
    """
    Lấy tên miền từ URL.
    
    Args:
        url (str): URL cần lấy tên miền.
    
    Returns:
        str: <PERSON><PERSON><PERSON> miền.
    """
    parsed_url = urlparse(url)
    return parsed_url.netloc

def is_same_domain(url, domain):
    """
    Kiểm tra xem URL có cùng tên miền hay không.
    
    Args:
        url (str): URL cần kiểm tra.
        domain (str): Tên miền cần so sánh.
    
    Returns:
        bool: True nếu cùng tên miền, False nếu không.
    """
    parsed_url = urlparse(url)
    return parsed_url.netloc == domain

def crawl_website(url, output_file, max_pages=100, max_depth=3, timeout=30, respect_robots_txt=False, use_playwright=True):
    """
    Crawl toàn bộ một website.
    
    Args:
        url (str): URL bắt đầu.
        output_file (str): Đường dẫn file để lưu kết quả.
        max_pages (int): Số trang tối đa để crawl.
        max_depth (int): Độ sâu tối đa để crawl.
        timeout (int): Thời gian chờ tối đa cho mỗi request (giây).
        respect_robots_txt (bool): Có tôn trọng robots.txt hay không.
        use_playwright (bool): Có sử dụng Playwright hay không.
    
    Returns:
        dict: Thông tin về quá trình crawl.
    """
    # Khởi tạo AdaptiveCrawler
    crawler = AdaptiveCrawler(
        max_depth=max_depth,
        max_pages=max_pages,
        timeout=timeout,
        user_agent_rotation=True,
        respect_robots_txt=respect_robots_txt,
        verbose=True
    )
    
    # Lấy tên miền
    domain = get_domain(url)
    logger.info(f"Bắt đầu crawl website: {url} (tên miền: {domain})")
    logger.info(f"Số trang tối đa: {max_pages}, Độ sâu tối đa: {max_depth}")
    
    # Thời gian bắt đầu
    start_time = time.time()
    
    # Crawl website
    pages = crawler.crawl(url)
    
    # Lọc các trang cùng tên miền
    same_domain_pages = [page for page in pages if is_same_domain(page['url'], domain)]
    
    # Thời gian kết thúc
    end_time = time.time()
    
    # Thông tin về quá trình crawl
    crawl_info = {
        'url': url,
        'domain': domain,
        'start_time': datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S'),
        'end_time': datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S'),
        'duration': end_time - start_time,
        'total_pages': len(pages),
        'same_domain_pages': len(same_domain_pages),
        'max_pages': max_pages,
        'max_depth': max_depth,
        'respect_robots_txt': respect_robots_txt,
        'use_playwright': use_playwright
    }
    
    # Lưu kết quả vào file
    with open(output_file, 'w', encoding='utf-8') as f:
        # Lưu thông tin về quá trình crawl
        f.write(f"# Kết quả crawl website: {url}\n")
        f.write(f"Thời gian: {crawl_info['start_time']} - {crawl_info['end_time']}\n")
        f.write(f"Thời gian crawl: {crawl_info['duration']:.2f} giây\n")
        f.write(f"Tổng số trang: {crawl_info['total_pages']}\n")
        f.write(f"Số trang cùng tên miền: {crawl_info['same_domain_pages']}\n")
        f.write(f"Độ sâu tối đa: {crawl_info['max_depth']}\n")
        f.write(f"Tôn trọng robots.txt: {crawl_info['respect_robots_txt']}\n")
        f.write(f"Sử dụng Playwright: {crawl_info['use_playwright']}\n\n")
        
        # Lưu thông tin về các trang đã crawl
        f.write("## Danh sách các trang đã crawl\n\n")
        for i, page in enumerate(same_domain_pages):
            f.write(f"### {i+1}. {page['title']}\n")
            f.write(f"URL: {page['url']}\n")
            f.write(f"Độ sâu: {page['depth']}\n")
            f.write(f"Số ký tự nội dung: {len(page['content'])}\n")
            f.write(f"Số liên kết: {len(page['links'])}\n")
            f.write(f"Nguồn: {page.get('source', 'unknown')}\n\n")
            
            # Lưu nội dung (trích đoạn)
            content_preview = page['content'][:500] + "..." if len(page['content']) > 500 else page['content']
            f.write("#### Nội dung (trích đoạn)\n")
            f.write("```\n")
            f.write(content_preview)
            f.write("\n```\n\n")
    
    # Lưu dữ liệu thô dưới dạng JSON
    json_output_file = output_file.replace('.md', '.json')
    with open(json_output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'crawl_info': crawl_info,
            'pages': same_domain_pages
        }, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Đã lưu kết quả vào file: {output_file}")
    logger.info(f"Đã lưu dữ liệu thô vào file: {json_output_file}")
    
    return crawl_info

def main():
    """
    Hàm chính.
    """
    # Phân tích tham số dòng lệnh
    parser = argparse.ArgumentParser(description='Crawl toàn bộ một website.')
    parser.add_argument('url', help='URL bắt đầu')
    parser.add_argument('--output', '-o', default='crawl_results.md', help='Đường dẫn file để lưu kết quả')
    parser.add_argument('--max-pages', '-p', type=int, default=100, help='Số trang tối đa để crawl')
    parser.add_argument('--max-depth', '-d', type=int, default=3, help='Độ sâu tối đa để crawl')
    parser.add_argument('--timeout', '-t', type=int, default=30, help='Thời gian chờ tối đa cho mỗi request (giây)')
    parser.add_argument('--respect-robots', '-r', action='store_true', help='Tôn trọng robots.txt')
    parser.add_argument('--no-playwright', '-n', action='store_true', help='Không sử dụng Playwright')
    
    args = parser.parse_args()
    
    # Crawl website
    crawl_website(
        url=args.url,
        output_file=args.output,
        max_pages=args.max_pages,
        max_depth=args.max_depth,
        timeout=args.timeout,
        respect_robots_txt=args.respect_robots,
        use_playwright=not args.no_playwright
    )

if __name__ == '__main__':
    main()
