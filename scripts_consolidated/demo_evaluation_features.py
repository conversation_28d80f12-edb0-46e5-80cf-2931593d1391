#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script demo minh họa cách sử dụng các tính năng đánh giá mới
của WebSearchAgentLocalMerged.
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any, List, Optional, cast

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(str(Path(__file__).parent))

try:
    from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
    # Import typestubs để giúp linter hiểu các phương thức mới
    from typestubs import WebSearchAgentLocalMergedStub
except ImportError as e:
    print(f"Lỗi khi import: {e}")
    sys.exit(1)

def print_separator():
    """In dòng phân cách."""
    print("\n" + "=" * 50 + "\n")

def demo_answer_quality():
    """Demo tính năng đánh giá chất lượng câu trả lời."""
    print("DEMO ĐÁNH GIÁ CHẤT LƯỢNG CÂU TRẢ LỜI")
    print_separator()
    
    # Khởi tạo agent
    agent = WebSearchAgentLocalMerged()
    # Cast để linter hiểu các phương thức
    agent_typed = cast(WebSearchAgentLocalMergedStub, agent)
    
    # Ví dụ 1: Câu trả lời tốt
    good_answer = """
    Hà Nội là thủ đô của Việt Nam, với lịch sử hơn 1000 năm văn hiến. 
    Thành phố này được thành lập từ năm 1010 dưới thời vua Lý Thái Tổ với tên gọi Thăng Long.
    Hà Nội có diện tích khoảng 3.359 km² và dân số khoảng 8 triệu người (2019).
    Thành phố nổi tiếng với nhiều di tích lịch sử như Văn Miếu - Quốc Tử Giám, Hoàng thành Thăng Long, và Hồ Gươm.
    Nền văn hóa Hà Nội cũng phong phú với các món ẩm thực truyền thống như phở, bún chả, và chả cá Lã Vọng.
    """
    
    query = "Giới thiệu về Hà Nội"
    
    print(f"Câu hỏi: {query}")
    print(f"Câu trả lời tốt:")
    print(good_answer)
    print("\nĐánh giá:")
    
    try:
        # Sử dụng phiên bản typed của agent
        result = agent_typed.evaluate_answer_quality(good_answer, query)
        
        print(f"- Điểm chất lượng: {result['quality_score']:.2f}")
        print(f"- Điểm chính xác: {result['accuracy_score']:.2f}")
        print(f"- Điểm đầy đủ: {result['completeness_score']:.2f}")
        print(f"- Điểm liên quan: {result['relevance_score']:.2f}")
        print(f"- Điểm rõ ràng: {result['clarity_score']:.2f}")
        print(f"- Điểm súc tích: {result['conciseness_score']:.2f}")
        
        print("\n- Điểm mạnh:")
        for strength in result['strengths']:
            print(f"  * {strength}")
            
        print("\n- Điểm yếu:")
        for weakness in result['weaknesses']:
            print(f"  * {weakness}")
            
        print("\n- Gợi ý cải thiện:")
        for suggestion in result['suggestions']:
            print(f"  * {suggestion}")
        
        print(f"\n- Giải thích: {result['explanation']}")
    except Exception as e:
        print(f"Lỗi khi đánh giá câu trả lời tốt: {e}")
    
    print_separator()
    
    # Ví dụ 2: Câu trả lời kém
    poor_answer = "Hà Nội là một thành phố ở Việt Nam."
    
    print(f"Câu hỏi: {query}")
    print(f"Câu trả lời kém:")
    print(poor_answer)
    print("\nĐánh giá:")
    
    try:
        # Sử dụng phiên bản typed của agent
        result = agent_typed.evaluate_answer_quality(poor_answer, query)
        
        print(f"- Điểm chất lượng: {result['quality_score']:.2f}")
        print(f"- Điểm chính xác: {result['accuracy_score']:.2f}")
        print(f"- Điểm đầy đủ: {result['completeness_score']:.2f}")
        print(f"- Điểm liên quan: {result['relevance_score']:.2f}")
        
        print("\n- Điểm yếu:")
        for weakness in result['weaknesses']:
            print(f"  * {weakness}")
            
        print("\n- Gợi ý cải thiện:")
        for suggestion in result['suggestions']:
            print(f"  * {suggestion}")
    except Exception as e:
        print(f"Lỗi khi đánh giá câu trả lời kém: {e}")

def demo_question_complexity():
    """Demo tính năng đánh giá độ phức tạp của câu hỏi."""
    print("DEMO ĐÁNH GIÁ ĐỘ PHỨC TẠP CỦA CÂU HỎI")
    print_separator()
    
    # Khởi tạo agent
    agent = WebSearchAgentLocalMerged()
    # Cast để linter hiểu các phương thức
    agent_typed = cast(WebSearchAgentLocalMergedStub, agent)
    
    # Ví dụ 1: Câu hỏi đơn giản
    simple_question = "Thủ đô của Việt Nam là gì?"
    
    print(f"Câu hỏi đơn giản: {simple_question}")
    print("\nĐánh giá:")
    
    try:
        # Sử dụng phiên bản typed của agent
        result = agent_typed.evaluate_question_complexity(simple_question)
        
        print(f"- Mức độ phức tạp: {result['complexity_level']}")
        print(f"- Điểm phức tạp: {result['complexity_score']:.2f}")
        print(f"- Loại câu hỏi: {result['question_type']}")
        
        print(f"\n- Đặc điểm câu hỏi:")
        for characteristic in result['question_characteristics']:
            print(f"  * {characteristic}")
            
        print(f"\n- Thực thể: {', '.join(result['entities'])}" if result['entities'] else "\n- Thực thể: Không có")
        print(f"- Từ khóa: {', '.join(result['keywords'])}" if result['keywords'] else "- Từ khóa: Không có")
        
        print("\n- Chiến lược đề xuất:")
        for key, value in result['recommended_strategy'].items():
            print(f"  * {key}: {value}")
    except Exception as e:
        print(f"Lỗi khi đánh giá câu hỏi đơn giản: {e}")
    
    print_separator()
    
    # Ví dụ 2: Câu hỏi phức tạp
    complex_question = "Phân tích so sánh các yếu tố kinh tế xã hội ảnh hưởng đến sự phát triển giáo dục ở các nước Đông Nam Á và đưa ra giải pháp cải thiện chất lượng giáo dục cho Việt Nam trong bối cảnh hội nhập quốc tế?"
    
    print(f"Câu hỏi phức tạp: {complex_question}")
    print("\nĐánh giá:")
    
    try:
        # Sử dụng phiên bản typed của agent
        result = agent_typed.evaluate_question_complexity(complex_question)
        
        print(f"- Mức độ phức tạp: {result['complexity_level']}")
        print(f"- Điểm phức tạp: {result['complexity_score']:.2f}")
        print(f"- Loại câu hỏi: {result['question_type']}")
        
        print(f"\n- Đặc điểm câu hỏi:")
        for characteristic in result['question_characteristics']:
            print(f"  * {characteristic}")
            
        print(f"\n- Thực thể: {', '.join(result['entities'])}" if result['entities'] else "\n- Thực thể: Không có")
        
        print("\n- Chiến lược đề xuất:")
        for key, value in result['recommended_strategy'].items():
            print(f"  * {key}: {value}")
    except Exception as e:
        print(f"Lỗi khi đánh giá câu hỏi phức tạp: {e}")
    
    print_separator()
    
    # Ví dụ 3: Các loại câu hỏi khác nhau
    question_types = {
        "comparative": "So sánh giữa kinh tế Việt Nam và Thái Lan?",
        "causal": "Tại sao biến đổi khí hậu lại ảnh hưởng đến nông nghiệp?",
        "procedural": "Làm thế nào để chế biến món phở bò truyền thống?",
        "definitional": "Khái niệm trí tuệ nhân tạo là gì?"
    }
    
    print("Đánh giá các loại câu hỏi khác nhau:")
    
    for q_type, question in question_types.items():
        try:
            # Sử dụng phiên bản typed của agent
            result = agent_typed.evaluate_question_complexity(question)
            print(f"\n{question}")
            print(f"- Loại câu hỏi: {result['question_type']}")
            print(f"- Mức độ phức tạp: {result['complexity_level']} ({result['complexity_score']:.2f})")
        except Exception as e:
            print(f"Lỗi khi đánh giá câu hỏi {q_type}: {e}")

def main():
    """Hàm main thực thi demo."""
    print("DEMO CÁC TÍNH NĂNG ĐÁNH GIÁ MỚI")
    print("===============================\n")
    
    try:
        # Demo đánh giá chất lượng câu trả lời
        demo_answer_quality()
        
        # Demo đánh giá độ phức tạp câu hỏi
        demo_question_complexity()
        
        print("Demo hoàn thành!")
    except Exception as e:
        print(f"Lỗi trong quá trình demo: {e}")

if __name__ == "__main__":
    main() 