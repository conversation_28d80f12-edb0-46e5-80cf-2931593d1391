#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Website Crawler with File Download - Công cụ để crawl toàn bộ một website và tải xuống các file.
Sử dụng AdaptiveCrawler từ module deepresearch.
"""

import os
import sys
import time
import json
import argparse
import logging
import requests
import mimetypes
from urllib.parse import urlparse, unquote
from datetime import datetime
import re

# Thêm thư mục hiện tại vào đường dẫn Python
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# Import AdaptiveCrawler
from deepresearch.adaptive_crawler import AdaptiveCrawler

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_domain(url):
    """
    <PERSON><PERSON>y tên miền từ URL.

    Args:
        url (str): U<PERSON> cần l<PERSON>y tên miền.

    Returns:
        str: Tên miền.
    """
    parsed_url = urlparse(url)
    return parsed_url.netloc

def is_same_domain(url, domain):
    """
    Kiểm tra xem URL có cùng tên miền hay không.

    Args:
        url (str): URL cần kiểm tra.
        domain (str): Tên miền cần so sánh.

    Returns:
        bool: True nếu cùng tên miền, False nếu không.
    """
    parsed_url = urlparse(url)
    return parsed_url.netloc == domain

def is_file_url(url):
    """
    Kiểm tra xem URL có phải là liên kết đến file hay không.

    Args:
        url (str): URL cần kiểm tra.

    Returns:
        bool: True nếu là file, False nếu không.
    """
    # Kiểm tra phần mở rộng của URL
    file_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.zip', '.rar', '.7z', '.txt', '.csv']
    parsed_url = urlparse(url)
    path = unquote(parsed_url.path)

    # Kiểm tra phần mở rộng
    for ext in file_extensions:
        if path.lower().endswith(ext):
            return True

    # Kiểm tra tham số query có chứa file hay không
    if 'download' in parsed_url.query.lower() or 'file' in parsed_url.query.lower():
        return True

    # Kiểm tra các mẫu URL đặc biệt của thuvienphapluat.vn
    if 'thuvienphapluat.vn' in url:
        if '/download/' in url.lower() or 'tab=7' in url.lower() or 'tab=1' in url.lower():
            return True
        if 'vbpq-download.aspx' in url.lower():
            return True

    return False

def get_file_name_from_url(url):
    """
    Lấy tên file từ URL.

    Args:
        url (str): URL cần lấy tên file.

    Returns:
        str: Tên file.
    """
    parsed_url = urlparse(url)
    path = unquote(parsed_url.path)

    # Lấy tên file từ path
    file_name = os.path.basename(path)

    # Nếu không có tên file, tạo tên file từ URL
    if not file_name or '.' not in file_name:
        file_name = f"file_{hash(url)}"

    # Loại bỏ các ký tự không hợp lệ
    file_name = re.sub(r'[\\/*?:"<>|]', '_', file_name)

    return file_name

def download_file(url, output_dir, timeout=30):
    """
    Tải xuống file từ URL.

    Args:
        url (str): URL của file.
        output_dir (str): Thư mục để lưu file.
        timeout (int): Thời gian chờ tối đa cho mỗi request (giây).

    Returns:
        dict: Thông tin về file đã tải xuống.
    """
    try:
        # Tạo thư mục nếu chưa tồn tại
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Lấy tên file từ URL
        file_name = get_file_name_from_url(url)
        file_path = os.path.join(output_dir, file_name)

        # Tải xuống file
        response = requests.get(url, stream=True, timeout=timeout)

        # Kiểm tra xem response có phải là file hay không
        content_type = response.headers.get('Content-Type', '')
        if 'text/html' in content_type and not file_name.endswith(('.html', '.htm')):
            # Thêm phần mở rộng dựa trên Content-Type
            ext = mimetypes.guess_extension(content_type)
            if ext:
                file_name += ext
                file_path = os.path.join(output_dir, file_name)

        # Lưu file
        with open(file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        # Thông tin về file
        file_info = {
            'url': url,
            'file_name': file_name,
            'file_path': file_path,
            'content_type': content_type,
            'size': os.path.getsize(file_path),
            'download_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        logger.info(f"Đã tải xuống file: {file_name} ({file_info['size']} bytes)")

        return file_info

    except Exception as e:
        logger.warning(f"Lỗi khi tải xuống file từ {url}: {e}")
        return None

def crawl_website(url, output_file, download_files=False, download_dir='downloads', max_pages=100, max_depth=3, timeout=30, respect_robots_txt=False, use_playwright=True):
    """
    Crawl toàn bộ một website và tải xuống các file.

    Args:
        url (str): URL bắt đầu.
        output_file (str): Đường dẫn file để lưu kết quả.
        download_files (bool): Có tải xuống các file hay không.
        download_dir (str): Thư mục để lưu các file tải xuống.
        max_pages (int): Số trang tối đa để crawl.
        max_depth (int): Độ sâu tối đa để crawl.
        timeout (int): Thời gian chờ tối đa cho mỗi request (giây).
        respect_robots_txt (bool): Có tôn trọng robots.txt hay không.
        use_playwright (bool): Có sử dụng Playwright hay không.

    Returns:
        dict: Thông tin về quá trình crawl.
    """
    # Khởi tạo AdaptiveCrawler
    crawler = AdaptiveCrawler(
        max_depth=max_depth,
        max_pages=max_pages,
        timeout=timeout,
        user_agent_rotation=True,
        respect_robots_txt=respect_robots_txt,
        verbose=True
    )

    # Lấy tên miền
    domain = get_domain(url)
    logger.info(f"Bắt đầu crawl website: {url} (tên miền: {domain})")
    logger.info(f"Số trang tối đa: {max_pages}, Độ sâu tối đa: {max_depth}")

    # Thời gian bắt đầu
    start_time = time.time()

    # Crawl website
    pages = crawler.crawl(url)

    # Lọc các trang cùng tên miền
    same_domain_pages = [page for page in pages if is_same_domain(page['url'], domain)]

    # Danh sách các file đã tải xuống
    downloaded_files = []

    # Tải xuống các file
    if download_files:
        logger.info(f"Bắt đầu tải xuống các file từ website: {url}")

        # Tạo thư mục để lưu các file
        if not os.path.exists(download_dir):
            os.makedirs(download_dir)

        # Tìm các liên kết đến file
        file_urls = set()

        # Tìm trong các trang đã crawl
        for page in same_domain_pages:
            for link in page['links']:
                if is_file_url(link):
                    file_urls.add(link)

        # Tải xuống các file
        for file_url in file_urls:
            file_info = download_file(file_url, download_dir, timeout)
            if file_info:
                downloaded_files.append(file_info)

    # Thời gian kết thúc
    end_time = time.time()

    # Thông tin về quá trình crawl
    crawl_info = {
        'url': url,
        'domain': domain,
        'start_time': datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S'),
        'end_time': datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S'),
        'duration': end_time - start_time,
        'total_pages': len(pages),
        'same_domain_pages': len(same_domain_pages),
        'max_pages': max_pages,
        'max_depth': max_depth,
        'respect_robots_txt': respect_robots_txt,
        'use_playwright': use_playwright,
        'download_files': download_files,
        'total_downloaded_files': len(downloaded_files)
    }

    # Lưu kết quả vào file
    with open(output_file, 'w', encoding='utf-8') as f:
        # Lưu thông tin về quá trình crawl
        f.write(f"# Kết quả crawl website: {url}\n")
        f.write(f"Thời gian: {crawl_info['start_time']} - {crawl_info['end_time']}\n")
        f.write(f"Thời gian crawl: {crawl_info['duration']:.2f} giây\n")
        f.write(f"Tổng số trang: {crawl_info['total_pages']}\n")
        f.write(f"Số trang cùng tên miền: {crawl_info['same_domain_pages']}\n")
        f.write(f"Độ sâu tối đa: {crawl_info['max_depth']}\n")
        f.write(f"Tôn trọng robots.txt: {crawl_info['respect_robots_txt']}\n")
        f.write(f"Sử dụng Playwright: {crawl_info['use_playwright']}\n")

        # Thông tin về các file đã tải xuống
        if download_files:
            f.write(f"\n## Các file đã tải xuống\n")
            f.write(f"Tổng số file đã tải xuống: {len(downloaded_files)}\n\n")

            for i, file_info in enumerate(downloaded_files):
                f.write(f"### {i+1}. {file_info['file_name']}\n")
                f.write(f"URL: {file_info['url']}\n")
                f.write(f"Đường dẫn: {file_info['file_path']}\n")
                f.write(f"Loại nội dung: {file_info['content_type']}\n")
                f.write(f"Kích thước: {file_info['size']} bytes\n")
                f.write(f"Thời gian tải xuống: {file_info['download_time']}\n\n")

        # Lưu thông tin về các trang đã crawl
        f.write("\n## Danh sách các trang đã crawl\n\n")
        for i, page in enumerate(same_domain_pages):
            f.write(f"### {i+1}. {page['title']}\n")
            f.write(f"URL: {page['url']}\n")
            f.write(f"Độ sâu: {page['depth']}\n")
            f.write(f"Số ký tự nội dung: {len(page['content'])}\n")
            f.write(f"Số liên kết: {len(page['links'])}\n")
            f.write(f"Nguồn: {page.get('source', 'unknown')}\n\n")

            # Lưu nội dung (trích đoạn)
            content_preview = page['content'][:500] + "..." if len(page['content']) > 500 else page['content']
            f.write("#### Nội dung (trích đoạn)\n")
            f.write("```\n")
            f.write(content_preview)
            f.write("\n```\n\n")

    # Lưu dữ liệu thô dưới dạng JSON
    json_output_file = output_file.replace('.md', '.json')
    with open(json_output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'crawl_info': crawl_info,
            'pages': same_domain_pages,
            'downloaded_files': downloaded_files
        }, f, ensure_ascii=False, indent=2)

    logger.info(f"Đã lưu kết quả vào file: {output_file}")
    logger.info(f"Đã lưu dữ liệu thô vào file: {json_output_file}")

    return crawl_info

def main():
    """
    Hàm chính.
    """
    # Phân tích tham số dòng lệnh
    parser = argparse.ArgumentParser(description='Crawl toàn bộ một website và tải xuống các file.')
    parser.add_argument('url', help='URL bắt đầu')
    parser.add_argument('--output', '-o', default='crawl_results.md', help='Đường dẫn file để lưu kết quả')
    parser.add_argument('--download-files', '-df', action='store_true', help='Tải xuống các file')
    parser.add_argument('--download-dir', '-dd', default='downloads', help='Thư mục để lưu các file tải xuống')
    parser.add_argument('--max-pages', '-p', type=int, default=100, help='Số trang tối đa để crawl')
    parser.add_argument('--max-depth', '-d', type=int, default=3, help='Độ sâu tối đa để crawl')
    parser.add_argument('--timeout', '-t', type=int, default=30, help='Thời gian chờ tối đa cho mỗi request (giây)')
    parser.add_argument('--respect-robots', '-r', action='store_true', help='Tôn trọng robots.txt')
    parser.add_argument('--no-playwright', '-n', action='store_true', help='Không sử dụng Playwright')

    args = parser.parse_args()

    # Crawl website
    crawl_website(
        url=args.url,
        output_file=args.output,
        download_files=args.download_files,
        download_dir=args.download_dir,
        max_pages=args.max_pages,
        max_depth=args.max_depth,
        timeout=args.timeout,
        respect_robots_txt=args.respect_robots,
        use_playwright=not args.no_playwright
    )

if __name__ == '__main__':
    main()
