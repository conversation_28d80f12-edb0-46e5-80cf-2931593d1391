"""
Script đo hiệu suất của WebSearchAgentLocal và AdaptiveCrawler trước và sau khi tích hợp tất cả các module dùng chung.
"""

import os
import sys
import time
import json
import logging
import argparse
import traceback
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

# Thêm thư mục gốc vào sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Import các module cần thiết
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal
from src.deep_research_core.crawlers.adaptive_crawler import AdaptiveCrawler
from src.deep_research_core.utils.shared.all_modules_integration import integrate_all_modules

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("performance_measurement.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Danh sách các truy vấn tìm kiếm để đo hiệu suất
SEARCH_QUERIES = [
    "Thủ tướng Việt Nam hiện nay là ai?",
    "Thủ đô của Việt Nam là gì?",
    "Diện tích Việt Nam là bao nhiêu?",
    "Dân số Việt Nam năm 2023 là bao nhiêu?",
    "GDP Việt Nam năm 2023 là bao nhiêu?",
    "Các tỉnh thành của Việt Nam",
    "Các món ăn đặc sản của Việt Nam",
    "Các di sản văn hóa thế giới tại Việt Nam",
    "Các danh lam thắng cảnh nổi tiếng của Việt Nam",
    "Các sự kiện lịch sử quan trọng của Việt Nam"
]

# Danh sách các URL để đo hiệu suất
CRAWL_URLS = [
    "https://vi.wikipedia.org/wiki/Vi%E1%BB%87t_Nam",
    "https://chinhphu.vn",
    "https://vnexpress.net",
    "https://dantri.com.vn",
    "https://thanhnien.vn",
    "https://tuoitre.vn",
    "https://vietnamnet.vn",
    "https://baomoi.com",
    "https://cafef.vn",
    "https://kenh14.vn"
]

def measure_web_search_agent_performance(
    use_integrated_modules: bool = False,
    num_queries: int = 5,
    timeout: int = 60,
    max_results: int = 5,
    verbose: bool = False
) -> Dict[str, Any]:
    """
    Đo hiệu suất của WebSearchAgentLocal.
    
    Args:
        use_integrated_modules: Sử dụng các module dùng chung đã tích hợp hay không
        num_queries: Số lượng truy vấn để đo hiệu suất
        timeout: Thời gian chờ tối đa cho mỗi truy vấn (giây)
        max_results: Số lượng kết quả tối đa cho mỗi truy vấn
        verbose: Ghi log chi tiết hay không
        
    Returns:
        Dict[str, Any]: Kết quả đo hiệu suất
    """
    logger.info(f"Bắt đầu đo hiệu suất WebSearchAgentLocal (use_integrated_modules={use_integrated_modules})")
    
    # Khởi tạo WebSearchAgentLocal
    agent = WebSearchAgentLocal(
        timeout=timeout,
        max_results=max_results,
        verbose=verbose
    )
    
    # Tích hợp tất cả các module dùng chung nếu cần
    if use_integrated_modules:
        logger.info("Tích hợp tất cả các module dùng chung vào WebSearchAgentLocal")
        agent = integrate_all_modules(agent)
    
    # Giới hạn số lượng truy vấn
    queries = SEARCH_QUERIES[:num_queries]
    
    # Kết quả đo hiệu suất
    performance_results = {
        "agent_type": "WebSearchAgentLocal",
        "use_integrated_modules": use_integrated_modules,
        "num_queries": len(queries),
        "timeout": timeout,
        "max_results": max_results,
        "total_time": 0,
        "average_time_per_query": 0,
        "success_rate": 0,
        "queries": []
    }
    
    # Đo hiệu suất cho từng truy vấn
    success_count = 0
    for i, query in enumerate(queries):
        logger.info(f"Đo hiệu suất cho truy vấn {i+1}/{len(queries)}: {query}")
        
        query_result = {
            "query": query,
            "time": 0,
            "success": False,
            "num_results": 0,
            "error": None
        }
        
        try:
            # Đo thời gian thực hiện truy vấn
            start_time = time.time()
            results = agent.search(query)
            end_time = time.time()
            
            # Tính thời gian thực hiện
            query_time = end_time - start_time
            
            # Cập nhật kết quả
            query_result["time"] = query_time
            query_result["success"] = True
            query_result["num_results"] = len(results)
            
            # Cập nhật tổng thời gian
            performance_results["total_time"] += query_time
            
            # Tăng số lượng truy vấn thành công
            success_count += 1
            
            logger.info(f"Truy vấn thành công, thời gian: {query_time:.2f}s, số lượng kết quả: {len(results)}")
        except Exception as e:
            # Ghi log lỗi
            error_message = str(e)
            logger.error(f"Lỗi khi thực hiện truy vấn: {error_message}")
            logger.error(traceback.format_exc())
            
            # Cập nhật kết quả
            query_result["error"] = error_message
        
        # Thêm kết quả truy vấn vào danh sách
        performance_results["queries"].append(query_result)
    
    # Tính thời gian trung bình cho mỗi truy vấn
    if len(queries) > 0:
        performance_results["average_time_per_query"] = performance_results["total_time"] / len(queries)
    
    # Tính tỷ lệ thành công
    if len(queries) > 0:
        performance_results["success_rate"] = success_count / len(queries)
    
    logger.info(f"Kết thúc đo hiệu suất WebSearchAgentLocal")
    logger.info(f"Tổng thời gian: {performance_results['total_time']:.2f}s")
    logger.info(f"Thời gian trung bình cho mỗi truy vấn: {performance_results['average_time_per_query']:.2f}s")
    logger.info(f"Tỷ lệ thành công: {performance_results['success_rate']:.2f}")
    
    return performance_results

def measure_adaptive_crawler_performance(
    use_integrated_modules: bool = False,
    num_urls: int = 5,
    timeout: int = 60,
    max_depth: int = 2,
    max_pages: int = 10,
    verbose: bool = False
) -> Dict[str, Any]:
    """
    Đo hiệu suất của AdaptiveCrawler.
    
    Args:
        use_integrated_modules: Sử dụng các module dùng chung đã tích hợp hay không
        num_urls: Số lượng URL để đo hiệu suất
        timeout: Thời gian chờ tối đa cho mỗi URL (giây)
        max_depth: Độ sâu tối đa cho mỗi URL
        max_pages: Số lượng trang tối đa cho mỗi URL
        verbose: Ghi log chi tiết hay không
        
    Returns:
        Dict[str, Any]: Kết quả đo hiệu suất
    """
    logger.info(f"Bắt đầu đo hiệu suất AdaptiveCrawler (use_integrated_modules={use_integrated_modules})")
    
    # Khởi tạo AdaptiveCrawler
    crawler = AdaptiveCrawler(
        timeout=timeout,
        max_depth=max_depth,
        max_pages=max_pages,
        verbose=verbose
    )
    
    # Tích hợp tất cả các module dùng chung nếu cần
    if use_integrated_modules:
        logger.info("Tích hợp tất cả các module dùng chung vào AdaptiveCrawler")
        crawler = integrate_all_modules(crawler)
    
    # Giới hạn số lượng URL
    urls = CRAWL_URLS[:num_urls]
    
    # Kết quả đo hiệu suất
    performance_results = {
        "crawler_type": "AdaptiveCrawler",
        "use_integrated_modules": use_integrated_modules,
        "num_urls": len(urls),
        "timeout": timeout,
        "max_depth": max_depth,
        "max_pages": max_pages,
        "total_time": 0,
        "average_time_per_url": 0,
        "success_rate": 0,
        "urls": []
    }
    
    # Đo hiệu suất cho từng URL
    success_count = 0
    for i, url in enumerate(urls):
        logger.info(f"Đo hiệu suất cho URL {i+1}/{len(urls)}: {url}")
        
        url_result = {
            "url": url,
            "time": 0,
            "success": False,
            "num_pages_crawled": 0,
            "error": None
        }
        
        try:
            # Đo thời gian thực hiện crawl
            start_time = time.time()
            results = crawler.crawl(url)
            end_time = time.time()
            
            # Tính thời gian thực hiện
            url_time = end_time - start_time
            
            # Cập nhật kết quả
            url_result["time"] = url_time
            url_result["success"] = True
            url_result["num_pages_crawled"] = len(results)
            
            # Cập nhật tổng thời gian
            performance_results["total_time"] += url_time
            
            # Tăng số lượng URL thành công
            success_count += 1
            
            logger.info(f"Crawl thành công, thời gian: {url_time:.2f}s, số lượng trang: {len(results)}")
        except Exception as e:
            # Ghi log lỗi
            error_message = str(e)
            logger.error(f"Lỗi khi crawl URL: {error_message}")
            logger.error(traceback.format_exc())
            
            # Cập nhật kết quả
            url_result["error"] = error_message
        
        # Thêm kết quả URL vào danh sách
        performance_results["urls"].append(url_result)
    
    # Tính thời gian trung bình cho mỗi URL
    if len(urls) > 0:
        performance_results["average_time_per_url"] = performance_results["total_time"] / len(urls)
    
    # Tính tỷ lệ thành công
    if len(urls) > 0:
        performance_results["success_rate"] = success_count / len(urls)
    
    logger.info(f"Kết thúc đo hiệu suất AdaptiveCrawler")
    logger.info(f"Tổng thời gian: {performance_results['total_time']:.2f}s")
    logger.info(f"Thời gian trung bình cho mỗi URL: {performance_results['average_time_per_url']:.2f}s")
    logger.info(f"Tỷ lệ thành công: {performance_results['success_rate']:.2f}")
    
    return performance_results

def save_performance_results(results: Dict[str, Any], filename: str) -> None:
    """
    Lưu kết quả đo hiệu suất vào file.
    
    Args:
        results: Kết quả đo hiệu suất
        filename: Tên file để lưu kết quả
    """
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Đã lưu kết quả đo hiệu suất vào file {filename}")

def compare_performance_results(
    before_results: Dict[str, Any],
    after_results: Dict[str, Any]
) -> Dict[str, Any]:
    """
    So sánh kết quả đo hiệu suất trước và sau khi tích hợp.
    
    Args:
        before_results: Kết quả đo hiệu suất trước khi tích hợp
        after_results: Kết quả đo hiệu suất sau khi tích hợp
        
    Returns:
        Dict[str, Any]: Kết quả so sánh
    """
    # Kết quả so sánh
    comparison_results = {
        "agent_type": before_results.get("agent_type") or before_results.get("crawler_type"),
        "before": {
            "total_time": before_results["total_time"],
            "average_time": before_results.get("average_time_per_query") or before_results.get("average_time_per_url"),
            "success_rate": before_results["success_rate"]
        },
        "after": {
            "total_time": after_results["total_time"],
            "average_time": after_results.get("average_time_per_query") or after_results.get("average_time_per_url"),
            "success_rate": after_results["success_rate"]
        },
        "difference": {
            "total_time": after_results["total_time"] - before_results["total_time"],
            "total_time_percent": (after_results["total_time"] - before_results["total_time"]) / before_results["total_time"] * 100 if before_results["total_time"] > 0 else 0,
            "average_time": (after_results.get("average_time_per_query") or after_results.get("average_time_per_url")) - (before_results.get("average_time_per_query") or before_results.get("average_time_per_url")),
            "average_time_percent": ((after_results.get("average_time_per_query") or after_results.get("average_time_per_url")) - (before_results.get("average_time_per_query") or before_results.get("average_time_per_url"))) / (before_results.get("average_time_per_query") or before_results.get("average_time_per_url")) * 100 if (before_results.get("average_time_per_query") or before_results.get("average_time_per_url")) > 0 else 0,
            "success_rate": after_results["success_rate"] - before_results["success_rate"],
            "success_rate_percent": (after_results["success_rate"] - before_results["success_rate"]) / before_results["success_rate"] * 100 if before_results["success_rate"] > 0 else 0
        }
    }
    
    return comparison_results

def main():
    """
    Hàm chính để đo hiệu suất.
    """
    # Phân tích tham số dòng lệnh
    parser = argparse.ArgumentParser(description="Đo hiệu suất của WebSearchAgentLocal và AdaptiveCrawler")
    parser.add_argument("--agent", choices=["web_search", "crawler", "both"], default="both", help="Loại agent để đo hiệu suất")
    parser.add_argument("--num-queries", type=int, default=5, help="Số lượng truy vấn để đo hiệu suất")
    parser.add_argument("--num-urls", type=int, default=5, help="Số lượng URL để đo hiệu suất")
    parser.add_argument("--timeout", type=int, default=60, help="Thời gian chờ tối đa (giây)")
    parser.add_argument("--max-results", type=int, default=5, help="Số lượng kết quả tối đa cho mỗi truy vấn")
    parser.add_argument("--max-depth", type=int, default=2, help="Độ sâu tối đa cho mỗi URL")
    parser.add_argument("--max-pages", type=int, default=10, help="Số lượng trang tối đa cho mỗi URL")
    parser.add_argument("--verbose", action="store_true", help="Ghi log chi tiết")
    parser.add_argument("--output-dir", default="performance_results", help="Thư mục để lưu kết quả")
    args = parser.parse_args()
    
    # Tạo thư mục để lưu kết quả nếu chưa tồn tại
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Tạo tên file dựa trên thời gian hiện tại
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Đo hiệu suất WebSearchAgentLocal
    if args.agent in ["web_search", "both"]:
        # Đo hiệu suất trước khi tích hợp
        web_search_before = measure_web_search_agent_performance(
            use_integrated_modules=False,
            num_queries=args.num_queries,
            timeout=args.timeout,
            max_results=args.max_results,
            verbose=args.verbose
        )
        
        # Lưu kết quả
        save_performance_results(
            web_search_before,
            os.path.join(args.output_dir, f"web_search_before_{timestamp}.json")
        )
        
        # Đo hiệu suất sau khi tích hợp
        web_search_after = measure_web_search_agent_performance(
            use_integrated_modules=True,
            num_queries=args.num_queries,
            timeout=args.timeout,
            max_results=args.max_results,
            verbose=args.verbose
        )
        
        # Lưu kết quả
        save_performance_results(
            web_search_after,
            os.path.join(args.output_dir, f"web_search_after_{timestamp}.json")
        )
        
        # So sánh kết quả
        web_search_comparison = compare_performance_results(web_search_before, web_search_after)
        
        # Lưu kết quả so sánh
        save_performance_results(
            web_search_comparison,
            os.path.join(args.output_dir, f"web_search_comparison_{timestamp}.json")
        )
        
        # In kết quả so sánh
        logger.info("Kết quả so sánh hiệu suất WebSearchAgentLocal:")
        logger.info(f"Tổng thời gian: {web_search_comparison['before']['total_time']:.2f}s -> {web_search_comparison['after']['total_time']:.2f}s ({web_search_comparison['difference']['total_time_percent']:.2f}%)")
        logger.info(f"Thời gian trung bình: {web_search_comparison['before']['average_time']:.2f}s -> {web_search_comparison['after']['average_time']:.2f}s ({web_search_comparison['difference']['average_time_percent']:.2f}%)")
        logger.info(f"Tỷ lệ thành công: {web_search_comparison['before']['success_rate']:.2f} -> {web_search_comparison['after']['success_rate']:.2f} ({web_search_comparison['difference']['success_rate_percent']:.2f}%)")
    
    # Đo hiệu suất AdaptiveCrawler
    if args.agent in ["crawler", "both"]:
        # Đo hiệu suất trước khi tích hợp
        crawler_before = measure_adaptive_crawler_performance(
            use_integrated_modules=False,
            num_urls=args.num_urls,
            timeout=args.timeout,
            max_depth=args.max_depth,
            max_pages=args.max_pages,
            verbose=args.verbose
        )
        
        # Lưu kết quả
        save_performance_results(
            crawler_before,
            os.path.join(args.output_dir, f"crawler_before_{timestamp}.json")
        )
        
        # Đo hiệu suất sau khi tích hợp
        crawler_after = measure_adaptive_crawler_performance(
            use_integrated_modules=True,
            num_urls=args.num_urls,
            timeout=args.timeout,
            max_depth=args.max_depth,
            max_pages=args.max_pages,
            verbose=args.verbose
        )
        
        # Lưu kết quả
        save_performance_results(
            crawler_after,
            os.path.join(args.output_dir, f"crawler_after_{timestamp}.json")
        )
        
        # So sánh kết quả
        crawler_comparison = compare_performance_results(crawler_before, crawler_after)
        
        # Lưu kết quả so sánh
        save_performance_results(
            crawler_comparison,
            os.path.join(args.output_dir, f"crawler_comparison_{timestamp}.json")
        )
        
        # In kết quả so sánh
        logger.info("Kết quả so sánh hiệu suất AdaptiveCrawler:")
        logger.info(f"Tổng thời gian: {crawler_comparison['before']['total_time']:.2f}s -> {crawler_comparison['after']['total_time']:.2f}s ({crawler_comparison['difference']['total_time_percent']:.2f}%)")
        logger.info(f"Thời gian trung bình: {crawler_comparison['before']['average_time']:.2f}s -> {crawler_comparison['after']['average_time']:.2f}s ({crawler_comparison['difference']['average_time_percent']:.2f}%)")
        logger.info(f"Tỷ lệ thành công: {crawler_comparison['before']['success_rate']:.2f} -> {crawler_comparison['after']['success_rate']:.2f} ({crawler_comparison['difference']['success_rate_percent']:.2f}%)")

if __name__ == "__main__":
    main()
