#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test chi tiết cho các tính năng mới của WebSearchAgentLocalMerged
"""

import sys
import os
import json
from datetime import datetime

# Thêm đường dẫn để import
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_new_features():
    """Test các tính năng mới đã được thêm vào"""
    print("🚀 TESTING NEW FEATURES OF WEBSEARCHAGENTLOCALMERGED")
    print("=" * 60)
    
    try:
        from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
        print("✅ Import successful!")
        
        # Khởi tạo agent với verbose=False để giảm log
        agent = WebSearchAgentLocalMerged(verbose=False)
        print("✅ Agent initialization successful!")
        
        results = {}
        
        # Test 1: Question Complexity Evaluation
        print("\n🔍 Test 1: Question Complexity Evaluation")
        test_queries = [
            "Python là gì?",
            "So sánh Python và Java trong machine learning và web development",
            "What are the advantages and disadvantages of artificial intelligence?",
            "How to learn programming?"
        ]
        
        complexity_results = []
        for query in test_queries:
            try:
                result = agent.evaluate_question_complexity(query)
                complexity_results.append({
                    "query": query,
                    "complexity_level": result['complexity_level'],
                    "complexity_score": result['complexity_score'],
                    "question_type": result['question_type'],
                    "search_method": result['recommended_strategy']['search_method']
                })
                print(f"  ✓ '{query}' -> {result['complexity_level']} ({result['complexity_score']:.2f})")
            except Exception as e:
                print(f"  ✗ Error with '{query}': {e}")
                complexity_results.append({"query": query, "error": str(e)})
        
        results["question_complexity"] = complexity_results
        
        # Test 2: Answer Quality Evaluation
        print("\n🔍 Test 2: Answer Quality Evaluation")
        test_cases = [
            {
                "query": "Python là gì?",
                "answer": "Python là một ngôn ngữ lập trình bậc cao, được thiết kế với triết lý đơn giản và dễ đọc. Python được sử dụng rộng rãi trong phát triển web, khoa học dữ liệu, trí tuệ nhân tạo và nhiều lĩnh vực khác. Python có cú pháp rõ ràng và thư viện phong phú."
            },
            {
                "query": "Machine learning benefits",
                "answer": "Good for data analysis."
            },
            {
                "query": "Climate change effects",
                "answer": ""
            }
        ]
        
        quality_results = []
        for case in test_cases:
            try:
                result = agent.evaluate_answer_quality(case["answer"], case["query"])
                quality_results.append({
                    "query": case["query"],
                    "answer_length": len(case["answer"]),
                    "quality_score": result['quality_score'],
                    "accuracy_score": result['accuracy_score'],
                    "completeness_score": result['completeness_score'],
                    "relevance_score": result['relevance_score'],
                    "clarity_score": result['clarity_score'],
                    "explanation": result['explanation']
                })
                print(f"  ✓ Quality: {result['quality_score']:.2f}, Accuracy: {result['accuracy_score']:.2f}")
            except Exception as e:
                print(f"  ✗ Error: {e}")
                quality_results.append({"query": case["query"], "error": str(e)})
        
        results["answer_quality"] = quality_results
        
        # Test 3: Content Disinformation Check
        print("\n🔍 Test 3: Content Disinformation Check")
        test_contents = [
            "Nghiên cứu cho thấy Python là ngôn ngữ lập trình phổ biến. Theo Stack Overflow, Python được sử dụng bởi nhiều nhà phát triển.",
            "TIN ĐỘC QUYỀN!!! Chính phủ che giấu sự thật về Python! Họ không muốn bạn biết!!!",
            "Python programming language was created by Guido van Rossum in 1991."
        ]
        
        disinformation_results = []
        for content in test_contents:
            try:
                result = agent.check_content_disinformation(content)
                disinformation_results.append({
                    "content_preview": content[:50] + "...",
                    "is_disinformation": result['is_disinformation'],
                    "confidence_score": result['confidence_score'],
                    "warning_signs_count": len(result['warning_signs']),
                    "credibility_indicators_count": len(result['credibility_indicators']),
                    "explanation": result['explanation']
                })
                print(f"  ✓ Disinformation: {result['is_disinformation']}, Confidence: {result['confidence_score']:.2f}")
            except Exception as e:
                print(f"  ✗ Error: {e}")
                disinformation_results.append({"content": content[:50], "error": str(e)})
        
        results["disinformation_check"] = disinformation_results
        
        # Test 4: Vietnamese Text Processing
        print("\n🔍 Test 4: Vietnamese Text Processing")
        vietnamese_tests = [
            "Python là ngôn ngữ lập trình mạnh mẽ và dễ học",
            "Machine learning và AI đang phát triển nhanh chóng",
            "This is English text for comparison",
            "Trí tuệ nhân tạo sẽ thay đổi thế giới trong tương lai"
        ]
        
        vietnamese_results = []
        for text in vietnamese_tests:
            try:
                is_vietnamese = agent._is_vietnamese_text(text)
                vietnamese_results.append({
                    "text": text,
                    "is_vietnamese": is_vietnamese
                })
                print(f"  ✓ '{text[:30]}...' -> Vietnamese: {is_vietnamese}")
            except Exception as e:
                print(f"  ✗ Error: {e}")
                vietnamese_results.append({"text": text, "error": str(e)})
        
        results["vietnamese_processing"] = vietnamese_results
        
        # Test 5: Deep Crawl (Mock test)
        print("\n🔍 Test 5: Deep Crawl Functionality")
        try:
            crawl_result = agent._perform_deep_crawl("https://example.com", max_depth=1, max_pages=2)
            deep_crawl_result = {
                "status": crawl_result['status'],
                "pages_crawled": crawl_result['pages_crawled'],
                "errors_count": len(crawl_result['errors']),
                "crawl_time": crawl_result['crawl_time']
            }
            results["deep_crawl"] = deep_crawl_result
            print(f"  ✓ Status: {crawl_result['status']}, Pages: {crawl_result['pages_crawled']}")
        except Exception as e:
            print(f"  ✗ Deep crawl error: {e}")
            results["deep_crawl"] = {"error": str(e)}
        
        # Test 6: LLM Content Analysis (Mock test)
        print("\n🔍 Test 6: LLM Content Analysis")
        try:
            analysis_result = agent.analyze_content_with_llm(
                "Python is a powerful programming language used for web development, data science, and AI.",
                "Python programming"
            )
            llm_analysis_result = {
                "analysis_available": analysis_result['analysis_available'],
                "summary_length": len(analysis_result['summary']),
                "key_points_count": len(analysis_result['key_points']),
                "sentiment": analysis_result['sentiment'],
                "relevance_to_query": analysis_result['relevance_to_query'],
                "confidence": analysis_result['confidence']
            }
            results["llm_analysis"] = llm_analysis_result
            print(f"  ✓ Analysis available: {analysis_result['analysis_available']}, Sentiment: {analysis_result['sentiment']}")
        except Exception as e:
            print(f"  ✗ LLM analysis error: {e}")
            results["llm_analysis"] = {"error": str(e)}
        
        # Lưu kết quả chi tiết
        os.makedirs("test_results", exist_ok=True)
        with open("test_results/detailed_features_test.json", "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "test_results": results,
                "summary": {
                    "total_feature_tests": 6,
                    "successful_tests": sum(1 for r in results.values() if "error" not in str(r))
                }
            }, f, ensure_ascii=False, indent=2)
        
        print("\n🎉 ALL NEW FEATURES TESTED SUCCESSFULLY!")
        print("📁 Detailed results saved to test_results/detailed_features_test.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Major error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_new_features()
    print(f"\n📊 Final Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
