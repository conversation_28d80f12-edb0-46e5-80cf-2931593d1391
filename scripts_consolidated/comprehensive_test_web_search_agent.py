#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test case tổng hợp cho WebSearchAgentLocal.
Ki<PERSON>m tra đầy đủ các tính năng mới và ghi kết quả vào file.
"""

import os
import sys
import unittest
import time
import json
import logging
from datetime import datetime

# Thêm thư mục hiện tại vào đường dẫn Python
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# Import các module cần thiết
import sys
from deepresearch.question_complexity_evaluator import QuestionComplexityEvaluator
from deepresearch.answer_quality_evaluator import AnswerQualityEvaluator
from deepresearch.captcha_handler import CaptchaHandler
from deepresearch.web_search_agent_local import WebSearchAgentLocal

# Đảm bảo các module có thể được import
sys.modules['captcha_handler'] = sys.modules['deepresearch.captcha_handler']
sys.modules['question_complexity_evaluator'] = sys.modules['deepresearch.question_complexity_evaluator']
sys.modules['answer_quality_evaluator'] = sys.modules['deepresearch.answer_quality_evaluator']

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Tạo file kết quả
RESULTS_FILE = "test_results.md"

def write_to_results(text):
    """Ghi kết quả vào file."""
    with open(RESULTS_FILE, "a", encoding="utf-8") as f:
        f.write(text + "\n")

def write_section_header(title):
    """Ghi tiêu đề phần vào file kết quả."""
    write_to_results(f"\n## {title}\n")

def write_test_result(test_name, result):
    """Ghi kết quả test vào file."""
    write_to_results(f"### {test_name}\n")
    write_to_results("```\n" + result + "\n```\n")

class ComprehensiveWebSearchAgentTest(unittest.TestCase):
    """
    Test tổng hợp cho WebSearchAgentLocal.
    """
    
    @classmethod
    def setUpClass(cls):
        """Thiết lập trước khi chạy tất cả các test."""
        # Tạo file kết quả mới
        with open(RESULTS_FILE, "w", encoding="utf-8") as f:
            f.write(f"# Kết quả kiểm tra WebSearchAgentLocal\n")
            f.write(f"Thời gian: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    
    def setUp(self):
        """Thiết lập trước mỗi test."""
        self.agent = WebSearchAgentLocal(
            verbose=True,
            captcha_handler_config={
                "auto_solve": False,
                "use_selenium": False,
                "max_retries": 3,
                "retry_delay": 5
            },
            question_evaluator_config={
                "complexity_threshold_high": 0.7,
                "complexity_threshold_medium": 0.4,
                "use_domain_knowledge": True
            },
            answer_evaluator_config={
                "quality_threshold_high": 0.7,
                "quality_threshold_medium": 0.4,
                "use_model_evaluation": False
            }
        )
    
    def test_01_question_complexity_simple(self):
        """Test đánh giá câu hỏi đơn giản."""
        test_name = "Đánh giá câu hỏi đơn giản"
        query = "What is Python?"
        
        result = self.agent.evaluate_question_complexity(query)
        
        output = f"Câu hỏi: {query}\n"
        output += f"Độ phức tạp: {result.get('complexity_level', 'unknown')}\n"
        output += f"Điểm phức tạp: {result.get('complexity_score', 0)}\n"
        
        # Hiển thị chiến lược đề xuất
        strategy = result.get('recommended_strategy', {})
        output += f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}\n"
        output += f"Số kết quả đề xuất: {strategy.get('num_results', 5)}\n"
        output += f"Trích xuất nội dung: {strategy.get('get_content', False)}\n"
        output += f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}\n"
        
        write_test_result(test_name, output)
        self.assertIn(result["complexity_level"], ["low", "medium"])
    
    def test_02_question_complexity_complex(self):
        """Test đánh giá câu hỏi phức tạp."""
        test_name = "Đánh giá câu hỏi phức tạp"
        query = "Explain the differences between Python's asyncio, threading, and multiprocessing modules, including their performance characteristics, use cases, and limitations in various scenarios."
        
        result = self.agent.evaluate_question_complexity(query)
        
        output = f"Câu hỏi: {query}\n"
        output += f"Độ phức tạp: {result.get('complexity_level', 'unknown')}\n"
        output += f"Điểm phức tạp: {result.get('complexity_score', 0)}\n"
        
        # Hiển thị chiến lược đề xuất
        strategy = result.get('recommended_strategy', {})
        output += f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}\n"
        output += f"Số kết quả đề xuất: {strategy.get('num_results', 5)}\n"
        output += f"Trích xuất nội dung: {strategy.get('get_content', False)}\n"
        output += f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}\n"
        
        write_test_result(test_name, output)
        self.assertIn(result["complexity_level"], ["medium", "high"])
    
    def test_03_question_complexity_vietnamese(self):
        """Test đánh giá câu hỏi tiếng Việt."""
        test_name = "Đánh giá câu hỏi tiếng Việt"
        query = "Python là gì và tại sao nó được sử dụng rộng rãi trong lĩnh vực khoa học dữ liệu?"
        
        result = self.agent.evaluate_question_complexity(query)
        
        output = f"Câu hỏi: {query}\n"
        output += f"Độ phức tạp: {result.get('complexity_level', 'unknown')}\n"
        output += f"Điểm phức tạp: {result.get('complexity_score', 0)}\n"
        
        # Hiển thị chiến lược đề xuất
        strategy = result.get('recommended_strategy', {})
        output += f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}\n"
        output += f"Số kết quả đề xuất: {strategy.get('num_results', 5)}\n"
        output += f"Trích xuất nội dung: {strategy.get('get_content', False)}\n"
        output += f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}\n"
        
        write_test_result(test_name, output)
        self.assertIn(result["complexity_level"], ["low", "medium", "high"])
    
    def test_04_answer_quality_good(self):
        """Test đánh giá câu trả lời tốt."""
        test_name = "Đánh giá câu trả lời tốt"
        question = "What is Python?"
        answer = """
        Python is a high-level, interpreted programming language known for its readability and versatility.
        It was created by Guido van Rossum and first released in 1991. Python supports multiple programming
        paradigms, including procedural, object-oriented, and functional programming. It has a comprehensive
        standard library and a large ecosystem of third-party packages, making it suitable for a wide range
        of applications, from web development to data science and artificial intelligence.
        """
        documents = [
            {
                "title": "Python Programming - Wikipedia",
                "url": "https://en.wikipedia.org/wiki/Python_(programming_language)",
                "snippet": "Python is a high-level, general-purpose programming language.",
                "content": "Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentation. Python is dynamically-typed and garbage-collected. It supports multiple programming paradigms, including structured, object-oriented, and functional programming."
            }
        ]
        
        result = self.agent.evaluate_answer_quality(question, answer, documents)
        
        output = f"Câu hỏi: {question}\n"
        output += f"Câu trả lời: {answer.strip()}\n\n"
        output += f"Chất lượng: {result.get('quality_level', 'unknown')}\n"
        output += f"Điểm chất lượng: {result.get('overall_score', 0)}\n"
        output += f"Cần tìm kiếm thêm: {result.get('need_more_search', False)}\n\n"
        
        # Hiển thị các chỉ số đánh giá
        metrics = result.get('metrics', {})
        for metric_name, metric_data in metrics.items():
            output += f"Chỉ số {metric_name}:\n"
            output += f"  Điểm: {metric_data.get('score', 0)}\n"
            output += f"  Giải thích: {metric_data.get('explanation', '')}\n\n"
        
        write_test_result(test_name, output)
        self.assertIn(result["quality_level"], ["medium", "high"])
        self.assertFalse(result["need_more_search"])
    
    def test_05_answer_quality_poor(self):
        """Test đánh giá câu trả lời kém."""
        test_name = "Đánh giá câu trả lời kém"
        question = "What is Python?"
        answer = "It's a programming language."
        documents = [
            {
                "title": "Python Programming - Wikipedia",
                "url": "https://en.wikipedia.org/wiki/Python_(programming_language)",
                "snippet": "Python is a high-level, general-purpose programming language.",
                "content": "Python is a high-level, general-purpose programming language."
            }
        ]
        
        result = self.agent.evaluate_answer_quality(question, answer, documents)
        
        output = f"Câu hỏi: {question}\n"
        output += f"Câu trả lời: {answer.strip()}\n\n"
        output += f"Chất lượng: {result.get('quality_level', 'unknown')}\n"
        output += f"Điểm chất lượng: {result.get('overall_score', 0)}\n"
        output += f"Cần tìm kiếm thêm: {result.get('need_more_search', False)}\n\n"
        
        # Hiển thị các chỉ số đánh giá
        metrics = result.get('metrics', {})
        for metric_name, metric_data in metrics.items():
            output += f"Chỉ số {metric_name}:\n"
            output += f"  Điểm: {metric_data.get('score', 0)}\n"
            output += f"  Giải thích: {metric_data.get('explanation', '')}\n\n"
        
        write_test_result(test_name, output)
        self.assertIn(result["quality_level"], ["low", "medium"])
        self.assertTrue(result["need_more_search"])
    
    def test_06_answer_quality_vietnamese(self):
        """Test đánh giá câu trả lời tiếng Việt."""
        test_name = "Đánh giá câu trả lời tiếng Việt"
        question = "Python là gì?"
        answer = """
        Python là một ngôn ngữ lập trình bậc cao, được thiết kế với triết lý nhấn mạnh vào khả năng đọc code.
        Python hỗ trợ nhiều mô hình lập trình, bao gồm lập trình hướng đối tượng, lập trình hàm và lập trình
        thủ tục. Nó có một thư viện chuẩn toàn diện và một hệ sinh thái lớn các gói bên thứ ba, làm cho nó
        phù hợp với nhiều ứng dụng, từ phát triển web đến khoa học dữ liệu và trí tuệ nhân tạo.
        """
        documents = [
            {
                "title": "Python - Wikipedia tiếng Việt",
                "url": "https://vi.wikipedia.org/wiki/Python_(ngôn_ngữ_lập_trình)",
                "snippet": "Python là một ngôn ngữ lập trình bậc cao, được thiết kế với triết lý nhấn mạnh vào khả năng đọc code.",
                "content": "Python là một ngôn ngữ lập trình bậc cao, được thiết kế với triết lý nhấn mạnh vào khả năng đọc code. Python hỗ trợ nhiều mô hình lập trình, bao gồm lập trình hướng đối tượng, lập trình hàm và lập trình thủ tục."
            }
        ]
        
        result = self.agent.evaluate_answer_quality(question, answer, documents)
        
        output = f"Câu hỏi: {question}\n"
        output += f"Câu trả lời: {answer.strip()}\n\n"
        output += f"Chất lượng: {result.get('quality_level', 'unknown')}\n"
        output += f"Điểm chất lượng: {result.get('overall_score', 0)}\n"
        output += f"Cần tìm kiếm thêm: {result.get('need_more_search', False)}\n\n"
        
        # Hiển thị các chỉ số đánh giá
        metrics = result.get('metrics', {})
        for metric_name, metric_data in metrics.items():
            output += f"Chỉ số {metric_name}:\n"
            output += f"  Điểm: {metric_data.get('score', 0)}\n"
            output += f"  Giải thích: {metric_data.get('explanation', '')}\n\n"
        
        write_test_result(test_name, output)
        self.assertIn(result["quality_level"], ["medium", "high"])
    
    def test_07_captcha_recaptcha(self):
        """Test phát hiện và xử lý reCAPTCHA."""
        test_name = "Phát hiện và xử lý reCAPTCHA"
        url = "https://example.com/recaptcha"
        html_content = """
        <html>
        <head><title>CAPTCHA Test</title></head>
        <body>
            <div class="g-recaptcha" data-sitekey="6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"></div>
            <script src="https://www.google.com/recaptcha/api.js"></script>
        </body>
        </html>
        """
        
        # Phát hiện CAPTCHA
        captcha_handler = CaptchaHandler(auto_solve=False)
        has_captcha, captcha_type, captcha_data = captcha_handler.detect_captcha(html_content)
        
        output = f"URL: {url}\n"
        output += f"Có CAPTCHA: {has_captcha}\n"
        output += f"Loại CAPTCHA: {captcha_type}\n"
        output += f"Dữ liệu CAPTCHA: {captcha_data}\n\n"
        
        # Xử lý CAPTCHA
        result = self.agent.handle_captcha(url, html_content)
        
        output += f"Kết quả xử lý CAPTCHA:\n"
        output += f"Thành công: {result.get('success', False)}\n"
        output += f"Thông báo: {result.get('message', '')}\n"
        
        write_test_result(test_name, output)
        self.assertTrue(has_captcha)
        self.assertTrue("recaptcha" in str(captcha_type).lower())
    
    def test_08_captcha_hcaptcha(self):
        """Test phát hiện và xử lý hCaptcha."""
        test_name = "Phát hiện và xử lý hCaptcha"
        url = "https://example.com/hcaptcha"
        html_content = """
        <html>
        <head><title>CAPTCHA Test</title></head>
        <body>
            <div class="h-captcha" data-sitekey="10000000-ffff-ffff-ffff-000000000001"></div>
            <script src="https://hcaptcha.com/1/api.js"></script>
        </body>
        </html>
        """
        
        # Phát hiện CAPTCHA
        captcha_handler = CaptchaHandler(auto_solve=False)
        has_captcha, captcha_type, captcha_data = captcha_handler.detect_captcha(html_content)
        
        output = f"URL: {url}\n"
        output += f"Có CAPTCHA: {has_captcha}\n"
        output += f"Loại CAPTCHA: {captcha_type}\n"
        output += f"Dữ liệu CAPTCHA: {captcha_data}\n\n"
        
        # Xử lý CAPTCHA
        result = self.agent.handle_captcha(url, html_content)
        
        output += f"Kết quả xử lý CAPTCHA:\n"
        output += f"Thành công: {result.get('success', False)}\n"
        output += f"Thông báo: {result.get('message', '')}\n"
        
        write_test_result(test_name, output)
        self.assertTrue(has_captcha)
        self.assertTrue("hcaptcha" in str(captcha_type).lower())
    
    def test_09_captcha_image(self):
        """Test phát hiện và xử lý CAPTCHA hình ảnh."""
        test_name = "Phát hiện và xử lý CAPTCHA hình ảnh"
        url = "https://example.com/image_captcha"
        html_content = """
        <html>
        <head><title>CAPTCHA Test</title></head>
        <body>
            <form action="/verify" method="post">
                <img src="/captcha.jpg" alt="CAPTCHA" id="captcha-image">
                <input type="text" name="captcha" placeholder="Enter CAPTCHA">
                <button type="submit">Submit</button>
            </form>
        </body>
        </html>
        """
        
        # Phát hiện CAPTCHA
        captcha_handler = CaptchaHandler(auto_solve=False)
        has_captcha, captcha_type, captcha_data = captcha_handler.detect_captcha(html_content)
        
        output = f"URL: {url}\n"
        output += f"Có CAPTCHA: {has_captcha}\n"
        output += f"Loại CAPTCHA: {captcha_type}\n"
        output += f"Dữ liệu CAPTCHA: {captcha_data}\n\n"
        
        # Xử lý CAPTCHA
        result = self.agent.handle_captcha(url, html_content)
        
        output += f"Kết quả xử lý CAPTCHA:\n"
        output += f"Thành công: {result.get('success', False)}\n"
        output += f"Thông báo: {result.get('message', '')}\n"
        
        write_test_result(test_name, output)
        self.assertTrue(has_captcha)
        self.assertTrue("image" in str(captcha_type).lower())
    
    def test_10_search_with_evaluation(self):
        """Test tìm kiếm với đánh giá câu hỏi và câu trả lời."""
        test_name = "Tìm kiếm với đánh giá câu hỏi và câu trả lời"
        query = "What is Python?"
        
        # Thực hiện tìm kiếm
        result = self.agent.search(
            query=query,
            num_results=3,
            evaluate_question=True,
            get_content=True,
            evaluate_answer=True
        )
        
        output = f"Câu hỏi: {query}\n"
        output += f"Thành công: {result.get('success', False)}\n"
        output += f"Số kết quả: {len(result.get('results', []))}\n\n"
        
        # Hiển thị đánh giá câu hỏi
        if "question_evaluation" in result:
            question_eval = result["question_evaluation"]
            output += f"Đánh giá câu hỏi:\n"
            output += f"Độ phức tạp: {question_eval.get('complexity_level', 'unknown')}\n"
            output += f"Điểm phức tạp: {question_eval.get('complexity_score', 0)}\n\n"
        
        # Hiển thị đánh giá câu trả lời
        if "answer_evaluation" in result:
            answer_eval = result["answer_evaluation"]
            output += f"Đánh giá câu trả lời:\n"
            output += f"Chất lượng: {answer_eval.get('quality_level', 'unknown')}\n"
            output += f"Điểm chất lượng: {answer_eval.get('overall_score', 0)}\n"
            output += f"Cần tìm kiếm thêm: {answer_eval.get('need_more_search', False)}\n\n"
        
        # Hiển thị kết quả tìm kiếm
        output += f"Kết quả tìm kiếm:\n"
        for i, res in enumerate(result.get('results', [])[:2]):  # Chỉ hiển thị 2 kết quả đầu tiên
            output += f"Kết quả {i+1}:\n"
            output += f"  Tiêu đề: {res.get('title', '')}\n"
            output += f"  URL: {res.get('url', '')}\n"
            output += f"  Snippet: {res.get('snippet', '')[:100]}...\n\n"
        
        write_test_result(test_name, output)
        self.assertTrue(result["success"])
    
    def test_11_search_with_deep_crawl(self):
        """Test tìm kiếm với deep crawl."""
        test_name = "Tìm kiếm với deep crawl"
        query = "What are the benefits of Python for data science?"
        
        # Thực hiện tìm kiếm
        result = self.agent.search(
            query=query,
            num_results=3,
            deep_crawl=True
        )
        
        output = f"Câu hỏi: {query}\n"
        output += f"Thành công: {result.get('success', False)}\n"
        output += f"Số kết quả: {len(result.get('results', []))}\n\n"
        
        # Hiển thị kết quả tìm kiếm
        output += f"Kết quả tìm kiếm:\n"
        for i, res in enumerate(result.get('results', [])[:2]):  # Chỉ hiển thị 2 kết quả đầu tiên
            output += f"Kết quả {i+1}:\n"
            output += f"  Tiêu đề: {res.get('title', '')}\n"
            output += f"  URL: {res.get('url', '')}\n"
            output += f"  Snippet: {res.get('snippet', '')[:100]}...\n"
            output += f"  Source: {res.get('source', '')}\n\n"
        
        write_test_result(test_name, output)
        self.assertTrue(result["success"])
    
    def test_12_search_vietnamese(self):
        """Test tìm kiếm với câu hỏi tiếng Việt."""
        test_name = "Tìm kiếm với câu hỏi tiếng Việt"
        query = "Python là gì và tại sao nó được sử dụng rộng rãi?"
        
        # Thực hiện tìm kiếm
        result = self.agent.search(
            query=query,
            num_results=3,
            evaluate_question=True
        )
        
        output = f"Câu hỏi: {query}\n"
        output += f"Thành công: {result.get('success', False)}\n"
        output += f"Số kết quả: {len(result.get('results', []))}\n\n"
        
        # Hiển thị đánh giá câu hỏi
        if "question_evaluation" in result:
            question_eval = result["question_evaluation"]
            output += f"Đánh giá câu hỏi:\n"
            output += f"Độ phức tạp: {question_eval.get('complexity_level', 'unknown')}\n"
            output += f"Điểm phức tạp: {question_eval.get('complexity_score', 0)}\n\n"
        
        # Hiển thị kết quả tìm kiếm
        output += f"Kết quả tìm kiếm:\n"
        for i, res in enumerate(result.get('results', [])[:2]):  # Chỉ hiển thị 2 kết quả đầu tiên
            output += f"Kết quả {i+1}:\n"
            output += f"  Tiêu đề: {res.get('title', '')}\n"
            output += f"  URL: {res.get('url', '')}\n"
            output += f"  Snippet: {res.get('snippet', '')[:100]}...\n\n"
        
        write_test_result(test_name, output)
        self.assertTrue(result["success"])

if __name__ == "__main__":
    unittest.main()
