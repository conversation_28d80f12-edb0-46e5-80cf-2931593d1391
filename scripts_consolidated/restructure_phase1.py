#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PHASE 1: BACKUP & ANALYSIS
Phân tích và backup dự án trước khi tổ chức lại.
"""

import os
import sys
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Set, Any
import subprocess

def create_backup():
    """Tạo backup toàn bộ dự án."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_{timestamp}"

    print(f"🔄 Creating backup directory: {backup_dir}")

    # T<PERSON>o thư mục backup
    os.makedirs(backup_dir, exist_ok=True)

    # Backup các thư mục quan trọng
    important_dirs = [
        "src",
        "deepresearch",
        "docs",
        "tests",
        "examples",
        "config"
    ]

    def ignore_patterns(dir, files):
        """Ignore node_modules, __pycache__, .git, etc."""
        ignored = []
        for file in files:
            if file in ['node_modules', '__pycache__', '.git', '.pytest_cache', 'volumes']:
                ignored.append(file)
        return ignored

    for dir_name in important_dirs:
        if os.path.exists(dir_name):
            print(f"  📁 Backing up {dir_name}/")
            try:
                shutil.copytree(dir_name, f"{backup_dir}/{dir_name}", ignore=ignore_patterns)
            except Exception as e:
                print(f"  ⚠️ Warning backing up {dir_name}: {e}")
                # Try to backup what we can
                try:
                    shutil.copytree(dir_name, f"{backup_dir}/{dir_name}", ignore=ignore_patterns, ignore_errors=True)
                except:
                    print(f"  ❌ Failed to backup {dir_name}")
    
    # Backup các file quan trọng ở root
    important_files = [
        "README.md",
        "requirements.txt", 
        "pyproject.toml",
        ".gitignore"
    ]
    
    for file_name in important_files:
        if os.path.exists(file_name):
            print(f"  📄 Backing up {file_name}")
            shutil.copy2(file_name, f"{backup_dir}/{file_name}")
    
    print(f"✅ Backup completed: {backup_dir}")
    return backup_dir

def analyze_duplicate_files():
    """Phân tích các file duplicate giữa src/ và deepresearch/."""
    print("\n🔍 Analyzing duplicate files...")
    
    src_files = set()
    deepresearch_files = set()
    
    # Scan src/deep_research_core/
    if os.path.exists("src/deep_research_core"):
        for root, dirs, files in os.walk("src/deep_research_core"):
            for file in files:
                if file.endswith('.py'):
                    rel_path = os.path.relpath(os.path.join(root, file), "src/deep_research_core")
                    src_files.add(rel_path)
    
    # Scan deepresearch/
    if os.path.exists("deepresearch"):
        for root, dirs, files in os.walk("deepresearch"):
            for file in files:
                if file.endswith('.py'):
                    rel_path = os.path.relpath(os.path.join(root, file), "deepresearch")
                    deepresearch_files.add(rel_path)
    
    # Tìm duplicates
    duplicates = []
    for src_file in src_files:
        for dr_file in deepresearch_files:
            if os.path.basename(src_file) == os.path.basename(dr_file):
                duplicates.append({
                    'filename': os.path.basename(src_file),
                    'src_path': f"src/deep_research_core/{src_file}",
                    'deepresearch_path': f"deepresearch/{dr_file}"
                })
    
    print(f"  📊 Found {len(duplicates)} potential duplicates")
    
    return {
        'src_files': list(src_files),
        'deepresearch_files': list(deepresearch_files), 
        'duplicates': duplicates
    }

def identify_critical_files():
    """Xác định các file quan trọng cần giữ lại."""
    print("\n📋 Identifying critical files...")
    
    critical_files = {
        'agents': [
            'src/deep_research_core/agents/web_search_agent_local_merged.py',
            'src/deep_research_core/agents/adaptive_crawler_consolidated_merged.py',
            'src/deep_research_core/agents/adaptive_crawler_integration.py'
        ],
        'utils': [
            'src/deep_research_core/utils/shared/',
            'src/deep_research_core/utils/vietnamese_search_methods.py',
            'src/deep_research_core/utils/vietnamese_search_integration.py'
        ],
        'credibility': [
            'src/deep_research_core/credibility/',
        ],
        'tests': [
            'test_web_search_agent_local_merged.py',
            'test_adaptive_crawler_consolidated_merged.py'
        ]
    }
    
    existing_critical = {}
    for category, files in critical_files.items():
        existing_critical[category] = []
        for file_path in files:
            if os.path.exists(file_path):
                existing_critical[category].append(file_path)
                print(f"  ✅ {file_path}")
            else:
                print(f"  ❌ {file_path} (missing)")
    
    return existing_critical

def document_imports():
    """Tài liệu hóa các import dependencies."""
    print("\n📝 Documenting import dependencies...")
    
    import_map = {}
    
    # Scan Python files for imports
    for root, dirs, files in os.walk("."):
        # Skip backup directories and __pycache__
        dirs[:] = [d for d in dirs if not d.startswith('backup_') and d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    imports = []
                    for line in content.split('\n'):
                        line = line.strip()
                        if line.startswith('from ') or line.startswith('import '):
                            imports.append(line)
                    
                    if imports:
                        import_map[file_path] = imports
                        
                except Exception as e:
                    print(f"  ⚠️ Error reading {file_path}: {e}")
    
    print(f"  📊 Analyzed {len(import_map)} Python files")
    return import_map

def generate_analysis_report(backup_dir, duplicates, critical_files, import_map):
    """Tạo báo cáo phân tích."""
    print("\n📄 Generating analysis report...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'backup_directory': backup_dir,
        'file_analysis': duplicates,
        'critical_files': critical_files,
        'import_dependencies': import_map,
        'statistics': {
            'total_python_files': len(import_map),
            'duplicate_files': len(duplicates['duplicates']),
            'src_files': len(duplicates['src_files']),
            'deepresearch_files': len(duplicates['deepresearch_files'])
        }
    }
    
    report_file = f"{backup_dir}/analysis_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Analysis report saved: {report_file}")
    return report

def main():
    """Main function để chạy Phase 1."""
    print("🚀 PHASE 1: BACKUP & ANALYSIS")
    print("=" * 50)
    
    # Step 1: Create backup
    backup_dir = create_backup()
    
    # Step 2: Analyze duplicates
    duplicates = analyze_duplicate_files()
    
    # Step 3: Identify critical files
    critical_files = identify_critical_files()
    
    # Step 4: Document imports
    import_map = document_imports()
    
    # Step 5: Generate report
    report = generate_analysis_report(backup_dir, duplicates, critical_files, import_map)
    
    print("\n" + "=" * 50)
    print("📊 PHASE 1 SUMMARY:")
    print(f"  🗂️ Backup created: {backup_dir}")
    print(f"  📁 Python files analyzed: {report['statistics']['total_python_files']}")
    print(f"  🔄 Duplicate files found: {report['statistics']['duplicate_files']}")
    print(f"  📋 Critical files identified: {sum(len(files) for files in critical_files.values())}")
    print("\n✅ Phase 1 completed successfully!")
    print("📋 Next: Review analysis_report.json and proceed to Phase 2")

if __name__ == "__main__":
    main()
