#!/usr/bin/env python3
"""
Simple test để kiểm tra WebSearchAgentLocalMerged
"""

import sys
import os

# Thêm đường dẫn để import
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def main():
    print("🚀 SIMPLE CONSOLIDATION TEST")
    print("=" * 40)
    
    try:
        print("1. Testing import...")
        from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
        print("✅ Import successful!")
        
        print("\n2. Testing initialization...")
        agent = WebSearchAgentLocalMerged(
            verbose=False,
            use_cache=False,
            enable_credibility_evaluation=False,
            enable_feedback_system=False,
            deep_crawl=False,
            timeout=5
        )
        print("✅ Initialization successful!")
        
        print("\n3. Testing basic search...")
        result = agent.search("test query", num_results=1)
        if result and "results" in result:
            print("✅ Basic search successful!")
            print(f"   Found {len(result['results'])} results")
        else:
            print("⚠️ Basic search returned no results (expected for mock)")
            
        print("\n🎉 ALL TESTS PASSED!")
        print("WebSearchAgentLocalMerged is working correctly!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
