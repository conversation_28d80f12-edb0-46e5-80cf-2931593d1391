#!/usr/bin/env python3
"""
Simple test script để kiểm tra real search functionality.
"""

import sys
import os
import requests
from bs4 import BeautifulSoup
import urllib.parse
import json

def test_searxng_direct():
    """Test SearXNG trực tiếp."""
    print("🔍 Testing SearXNG directly...")
    
    searxng_instances = [
        "https://searx.be",
        "https://search.sapti.me", 
        "https://searx.tiekoetter.com"
    ]
    
    query = "Python programming tutorial"
    
    for instance in searxng_instances:
        try:
            params = {
                'q': query,
                'format': 'json',
                'engines': 'google,bing,duckduckgo',
                'safesearch': '1'
            }
            
            response = requests.get(
                f"{instance}/search",
                params=params,
                timeout=10,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                print(f"✅ {instance}: {len(results)} results")
                
                # Hiển thị 2 kết quả đầu
                for i, result in enumerate(results[:2], 1):
                    title = result.get('title', 'No title')[:60]
                    url = result.get('url', 'No URL')
                    print(f"  {i}. {title}")
                    print(f"     {url}")
                
                return True
                
            else:
                print(f"❌ {instance}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {instance}: {e}")
    
    return False

def test_duckduckgo_direct():
    """Test DuckDuckGo trực tiếp."""
    print("\n🦆 Testing DuckDuckGo directly...")
    
    try:
        query = "Python programming tutorial"
        encoded_query = urllib.parse.quote_plus(query)
        
        url = f"https://html.duckduckgo.com/html/?q={encoded_query}"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # Parse DuckDuckGo results
            for result_div in soup.find_all('div', class_='result')[:5]:
                try:
                    title_elem = result_div.find('a', class_='result__a')
                    snippet_elem = result_div.find('a', class_='result__snippet')
                    
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        url = title_elem.get('href', '')
                        snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''
                        
                        results.append({
                            'title': title,
                            'url': url,
                            'snippet': snippet
                        })
                except Exception as e:
                    continue
            
            print(f"✅ DuckDuckGo: {len(results)} results")
            
            # Hiển thị 2 kết quả đầu
            for i, result in enumerate(results[:2], 1):
                title = result['title'][:60]
                url = result['url']
                print(f"  {i}. {title}")
                print(f"     {url}")
            
            return len(results) > 0
        
        else:
            print(f"❌ DuckDuckGo: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ DuckDuckGo: {e}")
        return False

def test_agent_search_methods():
    """Test các search methods của agent trực tiếp."""
    print("\n🤖 Testing Agent Search Methods...")
    
    try:
        # Import chỉ những gì cần thiết
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        # Tạo class đơn giản để test
        class SimpleSearchAgent:
            def __init__(self):
                self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                self.verbose = True
                
            def _search_with_searxng(self, query: str, num_results: int):
                """Copy method từ agent."""
                try:
                    searxng_instances = [
                        "https://searx.be",
                        "https://search.sapti.me"
                    ]
                    
                    for instance in searxng_instances:
                        try:
                            params = {
                                'q': query,
                                'format': 'json',
                                'engines': 'google,bing,duckduckgo',
                                'safesearch': '1'
                            }
                            
                            response = requests.get(
                                f"{instance}/search",
                                params=params,
                                timeout=10,
                                headers={'User-Agent': self.user_agent}
                            )
                            
                            if response.status_code == 200:
                                data = response.json()
                                results = []
                                
                                for item in data.get('results', [])[:num_results]:
                                    result = {
                                        'title': item.get('title', ''),
                                        'url': item.get('url', ''),
                                        'snippet': item.get('content', ''),
                                        'source': item.get('engine', 'searxng'),
                                        'score': item.get('score', 0)
                                    }
                                    results.append(result)
                                
                                if results:
                                    print(f"✅ SearXNG method: {len(results)} results")
                                    return results
                                    
                        except Exception as e:
                            print(f"❌ SearXNG instance {instance}: {e}")
                            continue
                    
                    return []
                    
                except Exception as e:
                    print(f"❌ SearXNG method error: {e}")
                    return []
            
            def _search_with_duckduckgo(self, query: str, num_results: int):
                """Copy method từ agent."""
                try:
                    encoded_query = urllib.parse.quote_plus(query)
                    url = f"https://html.duckduckgo.com/html/?q={encoded_query}"
                    
                    headers = {
                        'User-Agent': self.user_agent,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                    }
                    
                    response = requests.get(url, headers=headers, timeout=10)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        results = []
                        
                        for result_div in soup.find_all('div', class_='result')[:num_results]:
                            try:
                                title_elem = result_div.find('a', class_='result__a')
                                snippet_elem = result_div.find('a', class_='result__snippet')
                                
                                if title_elem:
                                    title = title_elem.get_text(strip=True)
                                    url = title_elem.get('href', '')
                                    snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''
                                    
                                    result = {
                                        'title': title,
                                        'url': url,
                                        'snippet': snippet,
                                        'source': 'duckduckgo',
                                        'score': 1.0
                                    }
                                    results.append(result)
                            except Exception:
                                continue
                        
                        print(f"✅ DuckDuckGo method: {len(results)} results")
                        return results
                    
                    return []
                    
                except Exception as e:
                    print(f"❌ DuckDuckGo method error: {e}")
                    return []
        
        # Test agent methods
        agent = SimpleSearchAgent()
        query = "Python programming"
        
        searxng_results = agent._search_with_searxng(query, 3)
        ddg_results = agent._search_with_duckduckgo(query, 3)
        
        total_results = len(searxng_results) + len(ddg_results)
        print(f"\n📊 Total results from agent methods: {total_results}")
        
        return total_results > 0
        
    except Exception as e:
        print(f"❌ Agent methods test error: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 SIMPLE REAL SEARCH TEST")
    print("=" * 50)
    
    # Test 1: SearXNG direct
    searxng_ok = test_searxng_direct()
    
    # Test 2: DuckDuckGo direct  
    ddg_ok = test_duckduckgo_direct()
    
    # Test 3: Agent methods
    agent_ok = test_agent_search_methods()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    print(f"SearXNG direct: {'✅ PASS' if searxng_ok else '❌ FAIL'}")
    print(f"DuckDuckGo direct: {'✅ PASS' if ddg_ok else '❌ FAIL'}")
    print(f"Agent methods: {'✅ PASS' if agent_ok else '❌ FAIL'}")
    
    if searxng_ok or ddg_ok or agent_ok:
        print("\n🎉 SUCCESS: Real search functionality is working!")
        print("✅ The search() method will now return real data instead of mock data.")
        return True
    else:
        print("\n❌ FAILURE: All search methods failed.")
        print("❌ Check network connection and search engine availability.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
