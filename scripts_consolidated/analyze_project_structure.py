#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON> tích cấu trúc dự án để đưa ra kế hoạch tổ chức lại.
"""

import os
import json
from datetime import datetime
from collections import defaultdict

def count_files_by_type():
    """<PERSON><PERSON><PERSON> số lượng files theo loại."""
    print("📊 Analyzing file types...")
    
    file_counts = defaultdict(int)
    total_files = 0
    
    # Scan toàn bộ dự án
    for root, dirs, files in os.walk("."):
        # Skip backup directories
        dirs[:] = [d for d in dirs if not d.startswith('backup_') and d != '__pycache__']
        
        for file in files:
            total_files += 1
            if file.endswith('.py'):
                file_counts['Python files'] += 1
            elif file.endswith('.md'):
                file_counts['Markdown docs'] += 1
            elif file.endswith('.json'):
                file_counts['JSON files'] += 1
            elif file.endswith('.txt'):
                file_counts['Text files'] += 1
            elif file.endswith('.yml') or file.endswith('.yaml'):
                file_counts['YAML configs'] += 1
            elif file.startswith('test_'):
                file_counts['Test files'] += 1
            else:
                file_counts['Other files'] += 1
    
    print(f"  📁 Total files: {total_files}")
    for file_type, count in sorted(file_counts.items()):
        print(f"  📄 {file_type}: {count}")
    
    return file_counts, total_files

def analyze_root_level_mess():
    """Phân tích độ bừa bộn ở root level."""
    print("\n🚨 Analyzing root level mess...")
    
    root_files = []
    root_dirs = []
    
    for item in os.listdir("."):
        if os.path.isfile(item):
            root_files.append(item)
        elif os.path.isdir(item) and not item.startswith('.'):
            root_dirs.append(item)
    
    # Phân loại files ở root
    docs = [f for f in root_files if f.endswith('.md')]
    configs = [f for f in root_files if f.endswith(('.yml', '.yaml', '.json', '.txt', '.toml'))]
    python_files = [f for f in root_files if f.endswith('.py')]
    
    print(f"  📁 Root directories: {len(root_dirs)}")
    print(f"  📄 Root files: {len(root_files)}")
    print(f"    📝 Documentation files: {len(docs)}")
    print(f"    ⚙️ Config files: {len(configs)}")
    print(f"    🐍 Python files: {len(python_files)}")
    
    return {
        'root_dirs': root_dirs,
        'root_files': root_files,
        'docs': docs,
        'configs': configs,
        'python_files': python_files
    }

def compare_src_vs_deepresearch():
    """So sánh cấu trúc src/ vs deepresearch/."""
    print("\n🔍 Comparing src/ vs deepresearch/...")
    
    src_structure = {}
    deepresearch_structure = {}
    
    # Analyze src/
    if os.path.exists("src/deep_research_core"):
        src_structure = analyze_directory_structure("src/deep_research_core")
        print(f"  📁 src/deep_research_core: {src_structure['total_files']} files")
    
    # Analyze deepresearch/
    if os.path.exists("deepresearch"):
        deepresearch_structure = analyze_directory_structure("deepresearch")
        print(f"  📁 deepresearch: {deepresearch_structure['total_files']} files")
    
    # Find potential duplicates
    duplicates = find_duplicate_filenames(src_structure.get('files', []), 
                                        deepresearch_structure.get('files', []))
    
    print(f"  🔄 Potential duplicate files: {len(duplicates)}")
    
    return {
        'src': src_structure,
        'deepresearch': deepresearch_structure,
        'duplicates': duplicates
    }

def analyze_directory_structure(path):
    """Phân tích cấu trúc thư mục."""
    structure = {
        'path': path,
        'subdirs': [],
        'files': [],
        'total_files': 0,
        'python_files': 0,
        'test_files': 0
    }
    
    for root, dirs, files in os.walk(path):
        # Skip problematic directories
        dirs[:] = [d for d in dirs if d not in ['__pycache__', 'node_modules', '.git']]
        
        for file in files:
            file_path = os.path.relpath(os.path.join(root, file), path)
            structure['files'].append(file_path)
            structure['total_files'] += 1
            
            if file.endswith('.py'):
                structure['python_files'] += 1
            if file.startswith('test_') or 'test' in file.lower():
                structure['test_files'] += 1
    
    # Get immediate subdirectories
    if os.path.exists(path):
        structure['subdirs'] = [d for d in os.listdir(path) 
                              if os.path.isdir(os.path.join(path, d)) 
                              and not d.startswith('.')]
    
    return structure

def find_duplicate_filenames(files1, files2):
    """Tìm files có tên giống nhau."""
    names1 = {os.path.basename(f): f for f in files1}
    names2 = {os.path.basename(f): f for f in files2}
    
    duplicates = []
    for name in names1:
        if name in names2:
            duplicates.append({
                'filename': name,
                'path1': names1[name],
                'path2': names2[name]
            })
    
    return duplicates

def analyze_test_files():
    """Phân tích test files."""
    print("\n🧪 Analyzing test files...")
    
    test_files = []
    test_dirs = []
    
    for root, dirs, files in os.walk("."):
        dirs[:] = [d for d in dirs if not d.startswith('backup_')]
        
        # Find test directories
        for d in dirs:
            if 'test' in d.lower():
                test_dirs.append(os.path.join(root, d))
        
        # Find test files
        for file in files:
            if file.startswith('test_') or 'test' in file.lower():
                test_files.append(os.path.join(root, file))
    
    print(f"  📁 Test directories: {len(test_dirs)}")
    print(f"  📄 Test files: {len(test_files)}")
    
    return {
        'test_dirs': test_dirs,
        'test_files': test_files
    }

def generate_restructure_recommendations():
    """Tạo đề xuất tổ chức lại."""
    print("\n💡 Generating restructure recommendations...")
    
    recommendations = {
        'priority_actions': [
            "Consolidate 50+ README files into organized docs/ structure",
            "Move all test files to unified tests/ directory", 
            "Archive deepresearch/ after extracting useful features",
            "Clean up root level - move configs to config/",
            "Remove cache and temp files"
        ],
        'file_moves': {
            'docs/': "All .md files except main README.md",
            'tests/': "All test_*.py files and test directories",
            'config/': "All .yml, .yaml, .json config files",
            'scripts/': "Utility .py scripts not part of main codebase",
            'archive/': "deepresearch/ directory after feature extraction"
        },
        'estimated_reduction': "70% reduction in root level files"
    }
    
    return recommendations

def main():
    """Main analysis function."""
    print("🔍 PROJECT STRUCTURE ANALYSIS")
    print("=" * 50)
    
    # Step 1: Count files by type
    file_counts, total_files = count_files_by_type()
    
    # Step 2: Analyze root level mess
    root_analysis = analyze_root_level_mess()
    
    # Step 3: Compare src vs deepresearch
    comparison = compare_src_vs_deepresearch()
    
    # Step 4: Analyze test files
    test_analysis = analyze_test_files()
    
    # Step 5: Generate recommendations
    recommendations = generate_restructure_recommendations()
    
    # Generate summary report
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_files': total_files,
            'root_level_files': len(root_analysis['root_files']),
            'documentation_files': len(root_analysis['docs']),
            'test_files': len(test_analysis['test_files']),
            'duplicate_candidates': len(comparison['duplicates'])
        },
        'file_counts': dict(file_counts),
        'root_analysis': root_analysis,
        'comparison': comparison,
        'test_analysis': test_analysis,
        'recommendations': recommendations
    }
    
    # Save report
    with open('project_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("\n" + "=" * 50)
    print("📊 ANALYSIS SUMMARY:")
    print(f"  📁 Total files: {total_files}")
    print(f"  📄 Root level files: {len(root_analysis['root_files'])}")
    print(f"  📝 Documentation files: {len(root_analysis['docs'])}")
    print(f"  🧪 Test files: {len(test_analysis['test_files'])}")
    print(f"  🔄 Potential duplicates: {len(comparison['duplicates'])}")
    print(f"\n✅ Analysis completed! Report saved: project_analysis_report.json")

if __name__ == "__main__":
    main()
