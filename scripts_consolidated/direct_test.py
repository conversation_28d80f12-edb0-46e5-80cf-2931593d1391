#!/usr/bin/env python3
"""
Direct test chỉ import WebSearchAgentLocalMerged
"""

import sys
import os

# Thêm đường dẫn để import
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def main():
    print("🔍 DIRECT IMPORT TEST")
    print("=" * 30)
    
    try:
        print("Attempting to import WebSearchAgentLocalMerged...")
        from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
        print("✅ Import successful!")
        
        print("Attempting to create instance...")
        agent = WebSearchAgentLocalMerged(
            verbose=False,
            use_cache=False,
            enable_credibility_evaluation=False,
            enable_feedback_system=False,
            deep_crawl=False,
            use_llm=False,
            timeout=5
        )
        print("✅ Instance creation successful!")
        
        print("🎉 ALL TESTS PASSED!")
        print("WebSearchAgentLocalMerged is working!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
