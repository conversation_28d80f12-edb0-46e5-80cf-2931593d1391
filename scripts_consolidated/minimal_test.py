#!/usr/bin/env python3
"""
Minimal test để kiểm tra import cơ bản
"""

import sys
import os

# Thêm đường dẫn để import
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_imports():
    """Test các import cơ bản"""
    print("Testing basic imports...")
    
    try:
        import requests
        print("✅ requests imported")
    except ImportError as e:
        print(f"❌ requests failed: {e}")
    
    try:
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup imported")
    except ImportError as e:
        print(f"❌ BeautifulSoup failed: {e}")
    
    try:
        import json
        print("✅ json imported")
    except ImportError as e:
        print(f"❌ json failed: {e}")

def test_module_imports():
    """Test import các module trong project"""
    print("\nTesting project module imports...")
    
    # Test import từng module một
    modules_to_test = [
        "src.deep_research_core.utils.cache_utils",
        "src.deep_research_core.utils.query_utils", 
        "src.deep_research_core.utils.result_utils",
        "src.deep_research_core.utils.error_utils",
        "src.deep_research_core.utils.vietnamese_utils",
        "src.deep_research_core.utils.credibility_utils"
    ]
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name} imported")
        except ImportError as e:
            print(f"⚠️ {module_name} failed: {e}")
        except Exception as e:
            print(f"❌ {module_name} error: {e}")

def test_agent_import():
    """Test import WebSearchAgentLocalMerged"""
    print("\nTesting WebSearchAgentLocalMerged import...")
    
    try:
        from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
        print("✅ WebSearchAgentLocalMerged imported successfully!")
        return True
    except ImportError as e:
        print(f"❌ WebSearchAgentLocalMerged import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ WebSearchAgentLocalMerged error: {e}")
        return False

def main():
    print("🔍 MINIMAL IMPORT TEST")
    print("=" * 30)
    
    test_basic_imports()
    test_module_imports()
    success = test_agent_import()
    
    print("\n" + "=" * 30)
    if success:
        print("🎉 IMPORT TEST PASSED!")
    else:
        print("⚠️ IMPORT TEST FAILED!")

if __name__ == "__main__":
    main()
