#!/usr/bin/env python3
"""
Script để kiểm tra circular imports trong codebase.
"""

import sys
import os
import ast
import json
from collections import defaultdict, deque
from datetime import datetime

def find_imports_in_file(file_path):
    """<PERSON><PERSON><PERSON> tấ<PERSON> cả imports trong một file Python."""
    imports = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
                    
    except Exception as e:
        print(f"Lỗi khi parse {file_path}: {e}")
    
    return imports

def build_dependency_graph(src_dir):
    """Xây dựng dependency graph từ source directory."""
    graph = defaultdict(set)
    all_modules = set()
    
    # Tìm tất cả Python files
    python_files = []
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                python_files.append(os.path.join(root, file))
    
    print(f"Tìm thấy {len(python_files)} Python files")
    
    # Xây dựng graph
    for file_path in python_files:
        # Convert file path to module name
        rel_path = os.path.relpath(file_path, src_dir)
        module_name = rel_path.replace(os.sep, '.').replace('.py', '')
        
        all_modules.add(module_name)
        
        # Tìm imports
        imports = find_imports_in_file(file_path)
        
        for imp in imports:
            # Chỉ quan tâm imports trong project
            if imp.startswith('deep_research_core'):
                # Normalize import name
                normalized_imp = imp.replace('src.', '').replace('deep_research_core.', '')
                graph[module_name].add(normalized_imp)
    
    return graph, all_modules

def find_circular_dependencies(graph):
    """Tìm circular dependencies trong graph."""
    def dfs(node, path, visited, rec_stack):
        visited.add(node)
        rec_stack.add(node)
        path.append(node)
        
        for neighbor in graph.get(node, []):
            if neighbor in rec_stack:
                # Tìm thấy cycle
                cycle_start = path.index(neighbor)
                cycle = path[cycle_start:] + [neighbor]
                return cycle
            
            if neighbor not in visited:
                cycle = dfs(neighbor, path[:], visited, rec_stack)
                if cycle:
                    return cycle
        
        rec_stack.remove(node)
        return None
    
    visited = set()
    cycles = []
    
    for node in graph:
        if node not in visited:
            cycle = dfs(node, [], visited, set())
            if cycle:
                cycles.append(cycle)
    
    return cycles

def analyze_imports_complexity(graph):
    """Phân tích độ phức tạp của imports."""
    stats = {
        'total_modules': len(graph),
        'total_dependencies': sum(len(deps) for deps in graph.values()),
        'modules_with_most_deps': [],
        'most_imported_modules': defaultdict(int)
    }
    
    # Modules với nhiều dependencies nhất
    module_dep_counts = [(module, len(deps)) for module, deps in graph.items()]
    module_dep_counts.sort(key=lambda x: x[1], reverse=True)
    stats['modules_with_most_deps'] = module_dep_counts[:10]
    
    # Modules được import nhiều nhất
    for deps in graph.values():
        for dep in deps:
            stats['most_imported_modules'][dep] += 1
    
    most_imported = sorted(
        stats['most_imported_modules'].items(),
        key=lambda x: x[1],
        reverse=True
    )[:10]
    stats['most_imported_modules'] = most_imported
    
    return stats

def check_specific_patterns():
    """Kiểm tra các patterns có thể gây circular imports."""
    patterns = []
    
    # Kiểm tra WebSearchAgentLocalMerged imports
    agent_file = "src/deep_research_core/agents/web_search_agent_local_merged.py"
    if os.path.exists(agent_file):
        imports = find_imports_in_file(agent_file)
        
        # Kiểm tra imports có thể gây vấn đề
        problematic_imports = []
        for imp in imports:
            if any(keyword in imp.lower() for keyword in ['agent', 'search', 'crawler']):
                if imp != 'deep_research_core.agents.web_search_agent_local_merged':
                    problematic_imports.append(imp)
        
        if problematic_imports:
            patterns.append({
                'file': agent_file,
                'issue': 'Potential circular imports with other agents',
                'imports': problematic_imports
            })
    
    # Kiểm tra utils imports
    utils_dir = "src/deep_research_core/utils"
    if os.path.exists(utils_dir):
        utils_files = []
        for root, dirs, files in os.walk(utils_dir):
            for file in files:
                if file.endswith('.py'):
                    utils_files.append(os.path.join(root, file))
        
        for utils_file in utils_files:
            imports = find_imports_in_file(utils_file)
            agent_imports = [imp for imp in imports if 'agent' in imp.lower()]
            
            if agent_imports:
                patterns.append({
                    'file': utils_file,
                    'issue': 'Utils importing agents (potential circular)',
                    'imports': agent_imports
                })
    
    return patterns

def main():
    """Main function để chạy circular import check."""
    print("🔍 CIRCULAR IMPORTS CHECK")
    print("=" * 50)
    
    src_dir = "src"
    if not os.path.exists(src_dir):
        print("❌ Không tìm thấy src directory")
        return False
    
    # Build dependency graph
    print("📊 Building dependency graph...")
    graph, all_modules = build_dependency_graph(src_dir)
    
    # Find circular dependencies
    print("🔄 Checking for circular dependencies...")
    cycles = find_circular_dependencies(graph)
    
    # Analyze complexity
    print("📈 Analyzing import complexity...")
    stats = analyze_imports_complexity(graph)
    
    # Check specific patterns
    print("🎯 Checking specific patterns...")
    patterns = check_specific_patterns()
    
    # Generate report
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_modules': stats['total_modules'],
            'total_dependencies': stats['total_dependencies'],
            'circular_dependencies_found': len(cycles),
            'problematic_patterns': len(patterns)
        },
        'circular_dependencies': cycles,
        'complexity_stats': stats,
        'problematic_patterns': patterns
    }
    
    # Display results
    print("\n" + "=" * 50)
    print("📊 RESULTS")
    print("=" * 50)
    
    print(f"Total modules analyzed: {stats['total_modules']}")
    print(f"Total dependencies: {stats['total_dependencies']}")
    print(f"Circular dependencies found: {len(cycles)}")
    print(f"Problematic patterns: {len(patterns)}")
    
    if cycles:
        print("\n🔄 CIRCULAR DEPENDENCIES:")
        for i, cycle in enumerate(cycles, 1):
            print(f"  {i}. {' -> '.join(cycle)}")
    else:
        print("\n✅ No circular dependencies found!")
    
    if patterns:
        print("\n⚠️  PROBLEMATIC PATTERNS:")
        for pattern in patterns:
            print(f"  📁 {pattern['file']}")
            print(f"     Issue: {pattern['issue']}")
            print(f"     Imports: {', '.join(pattern['imports'][:3])}...")
    else:
        print("\n✅ No problematic patterns found!")
    
    # Top modules with most dependencies
    print("\n📈 MODULES WITH MOST DEPENDENCIES:")
    for module, count in stats['modules_with_most_deps'][:5]:
        print(f"  {module}: {count} dependencies")
    
    # Most imported modules
    print("\n🎯 MOST IMPORTED MODULES:")
    for module, count in stats['most_imported_modules'][:5]:
        print(f"  {module}: imported {count} times")
    
    # Save report
    os.makedirs('test_results', exist_ok=True)
    report_file = f"test_results/circular_imports_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Report saved: {report_file}")
    
    # Overall assessment
    if len(cycles) == 0 and len(patterns) <= 2:
        print("\n🎉 CIRCULAR IMPORTS CHECK: PASS")
        print("✅ Code structure is clean!")
        return True
    else:
        print("\n⚠️  CIRCULAR IMPORTS CHECK: NEEDS ATTENTION")
        print("❌ Found issues that should be addressed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
