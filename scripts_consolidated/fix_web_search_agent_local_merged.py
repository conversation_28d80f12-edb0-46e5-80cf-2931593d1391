#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script sửa lỗi cú pháp trong file web_search_agent_local_merged.py
"""

import re
import sys

def fix_captcha_handler_import(content):
    """Sửa lỗi indent trong phần import CaptchaHandler"""
    pattern = r'try:\s+from src\.deep_research_core\.utils\.captcha_handler import CaptchaHandler\s+captcha_handler_available = True\s+except ImportError:\s+# Thử import từ thư mục utils/shared\s+try:\s+from src\.deep_research_core\.utils\.shared\.captcha_handler import CaptchaHandler\s+captcha_handler_available = True\s+except ImportError:\s+# Thử import từ các thư mục khác\s+try:\s+from \.\.utils\.shared\.captcha_handler import CaptchaHandler\s+captcha_handler_available = True\s+except ImportError:\s+captcha_handler_available = False\s+logger\.warning\("CaptchaHandler không khả dụng\. Xử lý CAPTCHA sẽ bị hạn chế\."\)'
    
    replacement = """try:
                from src.deep_research_core.utils.captcha_handler import CaptchaHandler
                captcha_handler_available = True
            except ImportError:
                # Thử import từ thư mục utils/shared
                try:
                    from src.deep_research_core.utils.shared.captcha_handler import CaptchaHandler
                    captcha_handler_available = True
                except ImportError:
                    # Thử import từ các thư mục khác
                    try:
                        from ..utils.shared.captcha_handler import CaptchaHandler
                        captcha_handler_available = True
                    except ImportError:
                        captcha_handler_available = False
                        logger.warning("CaptchaHandler không khả dụng. Xử lý CAPTCHA sẽ bị hạn chế.")"""
    
    # Sử dụng re.DOTALL để có thể khớp nhiều dòng
    return re.sub(pattern, replacement, content, flags=re.DOTALL)

def fix_adaptive_scraping(content):
    """Sửa lỗi cú pháp trong hàm adaptive_scraping"""
    pattern = r'if "text/html" in content_type:\s+# Sử dụng _crawl_with_playwright hoặc _fetch_url\s+if PLAYWRIGHT_AVAILABLE:\s+return self\._crawl_with_playwright\(url\)\s+else:\s+return self\._fetch_url\(url\)\s+elif "application/pdf" in content_type:'
    
    replacement = """if "text/html" in content_type:
                # Sử dụng _crawl_with_playwright hoặc _fetch_url
                if PLAYWRIGHT_AVAILABLE:
                    return self._crawl_with_playwright(url)
                else:
                    return self._fetch_url(url)
            elif "application/pdf" in content_type:"""
    
    return re.sub(pattern, replacement, content, flags=re.DOTALL)

def fix_extract_content_for_results(content):
    """Sửa lỗi cú pháp trong hàm extract_content_for_results"""
    pattern = r'try:\s+scraped_data = self\.adaptive_scraping\(url\)\s+if scraped_data and scraped_data\.get\("success", False\):\s+enriched_result\["content"\] = scraped_data\.get\("content", \{\}\)\s+enriched_result\["links"\] = scraped_data\.get\("links", \[\]\)\s+enriched_result\["extracted"\] = True\s+else:\s+enriched_result\["extraction_error"\] = scraped_data\.get\("error", "Unknown error"\)\s+except Exception as e:\s+enriched_result\["extraction_error"\] = f"Error: \{str\(e\)\}"'
    
    replacement = """try:
                scraped_data = self.adaptive_scraping(url)
                if scraped_data and scraped_data.get("success", False):
                    enriched_result["content"] = scraped_data.get("content", {})
                    enriched_result["links"] = scraped_data.get("links", [])
                    enriched_result["extracted"] = True
                else:
                    enriched_result["extraction_error"] = scraped_data.get("error", "Unknown error")
            except Exception as e:
                enriched_result["extraction_error"] = f"Error: {str(e)}" """
    
    return re.sub(pattern, replacement, content, flags=re.DOTALL)

def fix_extract_links(content):
    """Sửa lỗi cú pháp trong phần trích xuất links bằng Playwright"""
    pattern = r'try:\s+links = page\.evaluate\(""".*?"""\)\s+result\["links"\] = links\s+except Exception as e:\s+self\.logger\.warning\(f"Không thể trích xuất links: \{str\(e\)\}"\)\s+\s+except Exception as e:'
    
    replacement = """try:
                        links = page.evaluate(\"\"\"() => {
                            return Array.from(document.querySelectorAll('a[href]')).map(a => {
                                return {
                                    url: a.href,
                                    text: a.innerText.trim(),
                                    rel: a.rel
                                }
                            });
                        }\"\"\")
                        result["links"] = links
                    except Exception as e:
                        self.logger.warning(f"Không thể trích xuất links: {str(e)}")
                
                except Exception as e:"""
    
    return re.sub(pattern, replacement, content, flags=re.DOTALL)

def fix_undefined_user_agent(content):
    """Sửa lỗi undefined name 'user_agent'"""
    pattern = r'(headers = \{[^\}]*"User-Agent": )user_agent'
    replacement = r'\1self.user_agent'
    return re.sub(pattern, replacement, content)

def main():
    if len(sys.argv) != 3:
        print(f"Usage: {sys.argv[0]} input_file output_file")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Áp dụng các hàm sửa lỗi
        content = fix_captcha_handler_import(content)
        content = fix_adaptive_scraping(content)
        content = fix_extract_content_for_results(content)
        content = fix_extract_links(content)
        content = fix_undefined_user_agent(content)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Đã sửa lỗi và lưu vào {output_file}")
        
    except Exception as e:
        print(f"Lỗi: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 