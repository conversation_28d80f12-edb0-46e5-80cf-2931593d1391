# Hướng dẫn sử dụng tính năng Tìm kiếm Tiếng Việt Nâng cao

Tài liệu này cung cấp hướng dẫn chi tiết bằng tiếng Việt về cách cài đặt và sử dụng tính năng tìm kiếm tiếng Việt nâng cao trong dự án Deep Research Core.

## Mục lục

1. [Giớ<PERSON> thiệu](#giới-thiệu)
2. [<PERSON><PERSON><PERSON> đặt](#cài-đặt)
3. [Sử dụng cơ bản](#sử-dụng-cơ-bản)
4. [<PERSON><PERSON><PERSON> hình nâng cao](#cấu-hình-nâng-cao)
5. [<PERSON><PERSON> dụ thực tế](#ví-dụ-thực-tế)
6. [X<PERSON> lý lỗi thường gặp](#xử-lý-lỗi-thường-gặp)
7. [<PERSON><PERSON><PERSON> hỏi thường gặp](#câu-hỏi-thường-gặp)

## Giớ<PERSON> thiệu

Tính năng Tìm kiếm Tiếng Việt Nâng cao giúp cải thiện đáng kể khả năng tìm kiếm thông tin từ các nguồn tiếng Việt như:

- Cốc Cốc
- Wikipedia tiếng Việt
- Báo Mới
- Google.com.vn
- VnExpress
- Zing News

Hệ thống tự động phát hiện ngôn ngữ tiếng Việt trong truy vấn và áp dụng các phương pháp tìm kiếm đặc biệt để mang lại kết quả chính xác hơn.

### Tính năng chính

- **Phát hiện ngôn ngữ tự động nâng cao**: Kết hợp nhiều phương pháp để nhận diện tiếng Việt chính xác
- **Tối ưu hóa truy vấn tiếng Việt**: Loại bỏ dấu câu và từ dừng không cần thiết
- **Tìm kiếm đa nguồn**: Kết hợp kết quả từ nhiều nguồn tìm kiếm phổ biến ở Việt Nam
- **Tích hợp liền mạch**: Tự động kích hoạt khi phát hiện truy vấn tiếng Việt
- **Thông tin ngôn ngữ**: Bổ sung thông tin về ngôn ngữ vào kết quả tìm kiếm

## Cài đặt

### Yêu cầu hệ thống

- Python 3.7 trở lên
- Các gói phụ thuộc cơ bản: `requests`, `beautifulsoup4`
- Các gói tùy chọn: `langdetect`, `langid`, `fasttext`

### Cài đặt các gói phụ thuộc

```bash
# Cài đặt các gói cơ bản
pip install requests beautifulsoup4

# Cài đặt các gói phát hiện ngôn ngữ (tùy chọn nhưng khuyến nghị)
pip install langdetect langid

# Cài đặt fasttext (tùy chọn, chỉ cần nếu muốn phát hiện ngôn ngữ chính xác hơn)
pip install fasttext
```

### Cài đặt từ mã nguồn

```bash
git clone https://github.com/your-username/deep_research_core.git
cd deep_research_core
pip install -e .
```

## Sử dụng cơ bản

Để sử dụng tính năng tìm kiếm tiếng Việt, bạn cần tạo một instance của WebSearchAgentLocal đã được tích hợp tính năng này.

### Tạo agent tìm kiếm với tính năng tiếng Việt

```python
from src.deep_research_core.utils.vietnamese_search_integration_setup import create_enhanced_agent

# Tạo agent tìm kiếm với tính năng tiếng Việt
agent = create_enhanced_agent()

# Thực hiện tìm kiếm (tự động phát hiện tiếng Việt)
results = agent.search("Việt Nam là quốc gia nằm ở đâu")

# In kết quả
if isinstance(results, dict) and results.get("success") and results.get("results"):
    print(f"Tìm thấy {len(results['results'])} kết quả")
    
    for i, result in enumerate(results["results"][:5], 1):
        print(f"{i}. {result['title']}")
        print(f"   Mô tả: {result['snippet']}")
        print(f"   Liên kết: {result['url']}")
        print()
else:
    print("Không tìm thấy kết quả")
```

### Tìm kiếm với nguồn cụ thể

```python
# Tìm kiếm trên VnExpress
results = agent.search_vietnamese_source("vnexpress", "Dự báo thời tiết Hà Nội")

# Tìm kiếm trên Wikipedia tiếng Việt
results = agent.search_vietnamese_source("wikitiengviet", "Chủ tịch Hồ Chí Minh")
```

## Cấu hình nâng cao

Bạn có thể tùy chỉnh nhiều tham số để điều chỉnh cách thức hoạt động của tính năng tìm kiếm tiếng Việt.

### Cấu hình cơ bản

```python
config = {
    "vietnamese_search": {
        "enable_vietnamese_search": True,  # Bật/tắt tính năng
        "vietnamese_search_sources": [     # Các nguồn tìm kiếm sẽ sử dụng
            "coccoc", "wikitiengviet", "baomoi", 
            "google_vietnam", "vnexpress", "zingnews"
        ],
        "vietnamese_search_timeout": 10,   # Thời gian timeout (giây)
        "vietnamese_search_retry_count": 3, # Số lần thử lại khi gặp lỗi
        "optimize_vietnamese_query": True  # Tối ưu hóa truy vấn
    }
}

agent = create_enhanced_agent(config=config)
```

### Cấu hình phát hiện ngôn ngữ nâng cao

```python
config = {
    "vietnamese_search": {
        # Cấu hình cơ bản như ở trên
        
        # Cấu hình cho bộ phát hiện ngôn ngữ
        "use_advanced_language_detector": True,
        "language_detector_config": {
            "supported_languages": ["vi", "en", "fr", "zh", "ja"], # Các ngôn ngữ được hỗ trợ
            "min_length": 5,          # Độ dài tối thiểu của văn bản để phát hiện
            "default_language": "vi", # Ngôn ngữ mặc định
            "confidence_threshold": 0.4, # Ngưỡng tin cậy
            "cache_dir": "/tmp/lang_cache", # Thư mục lưu cache
            "max_cache_size": 5000,   # Kích thước tối đa của cache
            "use_fasttext": False     # Sử dụng fasttext nếu có
        }
    }
}

agent = create_enhanced_agent(config=config)
```

## Ví dụ thực tế

### Ví dụ 1: Tìm kiếm tin tức về COVID-19 ở Việt Nam

```python
from src.deep_research_core.utils.vietnamese_search_integration_setup import create_enhanced_agent

agent = create_enhanced_agent()

# Tìm kiếm tin tức về COVID-19 ở Việt Nam
results = agent.search("Tình hình COVID-19 mới nhất ở Việt Nam")

# Xử lý kết quả
if isinstance(results, dict) and results.get("success") and results.get("results"):
    print(f"Tìm thấy {len(results['results'])} kết quả về COVID-19 ở Việt Nam")
    
    # In thông tin về nguồn tìm kiếm đã sử dụng
    if "metadata" in results and "sources_used" in results["metadata"]:
        print(f"Các nguồn đã sử dụng: {', '.join(results['metadata']['sources_used'])}")
    
    # In ra kết quả
    for i, result in enumerate(results["results"][:3], 1):
        print(f"\n{i}. {result['title']}")
        print(f"   Mô tả: {result['snippet']}")
        print(f"   Nguồn: {result.get('source', 'Không rõ')}")
        print(f"   Liên kết: {result['url']}")
else:
    print("Không tìm thấy kết quả về COVID-19 ở Việt Nam")
```

### Ví dụ 2: So sánh kết quả từ các nguồn khác nhau

```python
agent = create_enhanced_agent()
query = "Giá vàng hôm nay"

# Tìm kiếm từ nhiều nguồn
results_combined = agent.search_vietnamese(query)

# Tìm kiếm từ VnExpress
results_vnexpress = agent.search_vietnamese_source("vnexpress", query)

# Tìm kiếm từ Báo Mới
results_baomoi = agent.search_vietnamese_source("baomoi", query)

# In số lượng kết quả từ mỗi nguồn
print(f"Tổng số kết quả: {len(results_combined.get('results', []))}")
print(f"Kết quả từ VnExpress: {len(results_vnexpress.get('results', []))}")
print(f"Kết quả từ Báo Mới: {len(results_baomoi.get('results', []))}")
```

### Ví dụ 3: Sử dụng bộ phát hiện ngôn ngữ độc lập

```python
from src.deep_research_core.utils.advanced_language_detector import AdvancedLanguageDetector

# Tạo bộ phát hiện ngôn ngữ
detector = AdvancedLanguageDetector()

# Kiểm tra một số mẫu văn bản
samples = [
    "Xin chào các bạn, tôi đến từ Việt Nam",
    "Hello, I am from the United States",
    "Toi la nguoi Viet Nam",
    "こんにちは、日本から来ました",
    "你好，我来自中国"
]

for sample in samples:
    lang, conf = detector.detect_language(sample)
    print(f"Văn bản: {sample}")
    print(f"Ngôn ngữ: {lang}, Độ tin cậy: {conf:.2f}")
    print("Phải tiếng Việt không? ", "Có" if detector.is_vietnamese(sample) else "Không")
    print()
```

## Xử lý lỗi thường gặp

### Lỗi kết nối

Nếu bạn gặp lỗi kết nối khi truy cập các nguồn tìm kiếm, hãy thử các giải pháp sau:

1. Tăng thời gian timeout trong cấu hình:
   ```python
   config = {
       "vietnamese_search": {
           "vietnamese_search_timeout": 20  # Tăng lên 20 giây
       }
   }
   ```

2. Tăng số lần thử lại:
   ```python
   config = {
       "vietnamese_search": {
           "vietnamese_search_retry_count": 5  # Tăng lên 5 lần
       }
   }
   ```

3. Kiểm tra kết nối mạng và khả năng truy cập các trang web tìm kiếm.

### Lỗi không phát hiện tiếng Việt

Nếu hệ thống không phát hiện được truy vấn tiếng Việt:

1. Đảm bảo truy vấn đủ dài (ít nhất 5-10 ký tự)
2. Kiểm tra truy vấn có chứa từ hoặc ký tự tiếng Việt
3. Cấu hình ngưỡng tin cậy thấp hơn:
   ```python
   config = {
       "vietnamese_search": {
           "language_detector_config": {
               "confidence_threshold": 0.3  # Giảm ngưỡng tin cậy xuống
           }
       }
   }
   ```

### Lỗi import thư viện

Nếu gặp lỗi khi import các thư viện phát hiện ngôn ngữ:

```
ImportError: No module named 'langdetect'
```

Hãy đảm bảo đã cài đặt các thư viện phụ thuộc:

```bash
pip install langdetect langid
```

Lưu ý rằng các thư viện này là tùy chọn, hệ thống vẫn hoạt động nếu không có chúng, nhưng độ chính xác sẽ thấp hơn.

## Câu hỏi thường gặp

### 1. Tính năng tìm kiếm tiếng Việt có hoạt động nếu không cài đặt các thư viện phát hiện ngôn ngữ?

Có, tính năng vẫn hoạt động với phương thức phát hiện ngôn ngữ dự phòng đơn giản dựa trên ký tự đặc biệt và từ vựng tiếng Việt phổ biến. Tuy nhiên, độ chính xác sẽ thấp hơn và có thể bỏ qua một số truy vấn tiếng Việt không dấu.

### 2. Tôi có thể thêm nguồn tìm kiếm tiếng Việt mới không?

Có, bạn có thể mở rộng tính năng bằng cách thêm nguồn tìm kiếm mới vào module `vietnamese_search_methods.py`. Bạn cần triển khai một hàm tìm kiếm mới và thêm nó vào danh sách nguồn trong `search_vietnamese_combined`.

### 3. Tại sao kết quả tìm kiếm từ nguồn này khác với nguồn khác?

Mỗi nguồn tìm kiếm có thuật toán và cơ sở dữ liệu riêng, dẫn đến kết quả khác nhau. Hơn nữa, cấu trúc trang web của mỗi nguồn khác nhau, nên quá trình trích xuất thông tin có thể cho ra kết quả khác nhau.

### 4. Làm cách nào để tránh bị chặn khi tìm kiếm quá nhiều?

Để tránh bị chặn bởi các nguồn tìm kiếm:
- Giãn thời gian giữa các lần tìm kiếm
- Sử dụng proxy hoặc VPN để thay đổi địa chỉ IP
- Giới hạn số lượng tìm kiếm trên mỗi nguồn trong một khoảng thời gian cụ thể

### 5. Tôi có thể lưu kết quả tìm kiếm để sử dụng lại sau này không?

Có, bạn có thể lưu kết quả tìm kiếm thành file JSON hoặc cơ sở dữ liệu:

```python
import json

results = agent.search_vietnamese("Việt Nam")

# Lưu kết quả vào file JSON
with open("vietnam_search_results.json", "w", encoding="utf-8") as f:
    json.dump(results, f, ensure_ascii=False, indent=2)

# Đọc kết quả từ file JSON
with open("vietnam_search_results.json", "r", encoding="utf-8") as f:
    saved_results = json.load(f)
```

---

## Liên hệ và hỗ trợ

Nếu bạn gặp bất kỳ vấn đề nào hoặc có câu hỏi về tính năng tìm kiếm tiếng Việt, vui lòng tạo issue trên GitHub hoặc liên hệ với nhóm phát triển qua email: <EMAIL>. 