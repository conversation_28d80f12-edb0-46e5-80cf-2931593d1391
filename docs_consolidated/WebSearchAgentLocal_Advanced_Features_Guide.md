# Hướng dẫn sử dụng các tính năng nâng cao của WebSearchAgentLocal

## Giớ<PERSON> thiệu

Tài liệu này mô tả cách sử dụng các tính năng nâng cao mới của WebSearchAgentLocal, bao gồm:

1. Tr<PERSON>ch xuất nội dung nâng cao từ các trang web có cấu trúc phức tạp
2. Xử lý JavaScript động trên các trang web hiện đại (Single Page Applications)
3. Đ<PERSON>h giá độ tin cậy nguồn thông tin tự động
4. Tối ưu hóa hiệu suất khi crawl nhiều trang cùng lúc

## 1. Trích xuất nội dung nâng cao (AdvancedContentExtractor)

### Cài đặt

Đảm bảo bạn đã cài đặt các thư viện cần thiết:

```bash
pip install readability-lxml trafilatura inscriptis newspaper3k beautifulsoup4 lxml
```

### Tích hợp vào WebSearchAgentLocal

```python
from src.deep_research_core.utils.content_extraction_utils import extract_from_complex_sites
from src.deep_research_core.agents.content_extractor_integration import integrate_advanced_content_extractor

# Khởi tạo WebSearchAgentLocal
agent = WebSearchAgentLocal(...)

# Tích hợp AdvancedContentExtractor
config = {
    "use_readability": True,
    "use_trafilatura": True,
    "use_inscriptis": True,
    "use_newspaper": True,
    "use_bs4": True,
    "extract_metadata": True,
    "extract_images": False,
    "extract_tables": True,
    "extract_structured_data": True,
    "fallback_to_html": True,
    "min_content_length": 200
}
integrate_advanced_content_extractor(agent, config)
```

### Sử dụng trực tiếp

```python
from src.deep_research_core.utils.advanced_content_extractor import AdvancedContentExtractor

# Khởi tạo trình trích xuất
extractor = AdvancedContentExtractor(
    use_readability=True,
    use_trafilatura=True,
    use_inscriptis=True,
    use_newspaper=True,
    use_bs4=True,
    extract_metadata=True,
    extract_images=False,
    extract_tables=True,
    extract_structured_data=True
)

# Trích xuất nội dung từ URL
result = extractor.extract_content("https://example.com")

# Hoặc từ HTML có sẵn
html = "..."
result = extractor.extract_content("https://example.com", html)

# Kết quả
print(f"Title: {result['title']}")
print(f"Text: {result['text'][:100]}...")
print(f"Extraction method: {result['extraction_method']}")
print(f"Success: {result['success']}")
```

### Tính năng chính

- **Phát hiện tự động loại trang web**: Tự động phát hiện loại trang web (wiki, blog, tin tức, học thuật...) và áp dụng chiến lược trích xuất phù hợp.
- **Kết hợp nhiều kỹ thuật trích xuất**: Sử dụng song song nhiều thư viện và chọn kết quả tốt nhất.
- **Trình trích xuất chuyên biệt theo domain**: Có các trình trích xuất được tối ưu cho các trang web phổ biến như Wikipedia, GitHub, Stack Overflow...
- **Phân tích mật độ nội dung**: Phát hiện nội dung chính dựa trên mật độ văn bản và cấu trúc DOM.
- **Trích xuất dữ liệu có cấu trúc**: Hỗ trợ trích xuất JSON-LD, Microdata, OpenGraph, Twitter Cards.

## 2. Xử lý JavaScript động (AdvancedJavaScriptHandler)

### Cài đặt

```bash
pip install playwright
playwright install chromium
```

### Tích hợp vào WebSearchAgentLocal

```python
from src.deep_research_core.agents.advanced_javascript_handler import integrate_javascript_handler

# Khởi tạo WebSearchAgentLocal
agent = WebSearchAgentLocal(...)

# Tích hợp AdvancedJavaScriptHandler
config = {
    "use_playwright": True,
    "browser_type": "chromium",
    "headless": True,
    "timeout": 30,
    "handle_spa": True,
    "handle_infinite_scroll": True,
    "scroll_count": 3,
    "handle_lazy_loading": True,
    "detect_frameworks": True,
    "pool_size": 2
}
integrate_javascript_handler(agent, config)
```

### Sử dụng trực tiếp

```python
from src.deep_research_core.agents.advanced_javascript_handler import AdvancedJavaScriptHandler

# Khởi tạo handler
js_handler = AdvancedJavaScriptHandler(
    use_playwright=True,
    browser_type="chromium",
    headless=True,
    timeout=30,
    handle_spa=True,
    handle_infinite_scroll=True,
    handle_lazy_loading=True
)

# Trích xuất nội dung từ trang web động
result = js_handler.extract_content(
    url="https://example.com/spa-app",
    selectors=[".main-content", ".article-body"],
    timeout=60
)

# Kết quả
print(f"Title: {result['title']}")
print(f"Text: {result['text'][:100]}...")
print(f"Framework detected: {result['framework_detected']}")
print(f"Extraction time: {result['extraction_time']:.2f}s")

# Dọn dẹp tài nguyên khi hoàn thành
js_handler.cleanup()
```

### Tính năng chính

- **Phát hiện JavaScript framework**: Tự động phát hiện framework (React, Angular, Vue, Svelte...) và điều chỉnh chiến lược xử lý phù hợp.
- **Xử lý SPA (Single Page Applications)**: Chờ đợi thích hợp để đảm bảo nội dung động được tải hoàn toàn.
- **Xử lý cuộn vô hạn (Infinite Scroll)**: Tự động cuộn trang để tải thêm nội dung.
- **Xử lý Lazy Loading**: Đảm bảo hình ảnh và iframe được tải đầy đủ.
- **Trích xuất nội dung từ Shadow DOM**: Hỗ trợ trích xuất nội dung từ Shadow DOM trên các trang web hiện đại.
- **Quản lý pool trình duyệt**: Tối ưu hiệu suất bằng cách tái sử dụng các instance trình duyệt.

## 3. Đánh giá độ tin cậy nguồn thông tin

Tính năng đánh giá độ tin cậy nguồn thông tin đã được tích hợp vào WebSearchAgentLocal. Khi sử dụng các phương thức search và extract_content, kết quả sẽ bao gồm đánh giá độ tin cậy của nguồn.

### Các mức đánh giá

- **Rất đáng tin cậy (0.9-1.0)**: Nguồn có uy tín cao, được xác minh, có lịch sử chính xác, được trích dẫn rộng rãi
- **Đáng tin cậy (0.7-0.9)**: Nguồn có uy tín, ít sai sót, nội dung được biên tập và kiểm tra thực tế
- **Trung bình (0.5-0.7)**: Nguồn thông thường, có thể có thiên kiến nhẹ, nội dung hỗn hợp giữa thực tế và ý kiến
- **Đáng ngờ (0.3-0.5)**: Nguồn có vấn đề về độ tin cậy, nhiều thiên kiến, nội dung chủ yếu là ý kiến
- **Không đáng tin cậy (0.0-0.3)**: Nguồn có lịch sử sai sự thật, có thiên kiến nặng, nội dung thường không chính xác

### Ví dụ

```python
# Tìm kiếm với đánh giá độ tin cậy
results = agent.search("artificial intelligence trends", num_results=5, evaluate_credibility=True)

# In kết quả
for result in results.get("results", []):
    print(f"Title: {result.get('title')}")
    print(f"URL: {result.get('url')}")
    print(f"Credibility score: {result.get('credibility_score', 'N/A')}")
    print(f"Credibility factors: {result.get('credibility_factors', {})}")
    print("---")
```

## 4. Tối ưu hóa hiệu suất khi crawl nhiều trang

WebSearchAgentLocal đã được tối ưu để xử lý hiệu quả việc trích xuất nội dung từ nhiều trang web cùng lúc.

### Sử dụng chế độ bất đồng bộ

```python
import asyncio
from src.deep_research_core.agents.web_search_agent_local import WebSearchAgentLocalAsync

# Khởi tạo agent bất đồng bộ
agent = WebSearchAgentLocalAsync(...)

# Tìm kiếm và trích xuất nội dung bất đồng bộ
async def search_and_extract():
    # Tìm kiếm
    results = await agent.search_async("artificial intelligence trends", num_results=10)
    
    # Lấy danh sách URL
    urls = [result.get("url") for result in results.get("results", [])]
    
    # Trích xuất nội dung đồng thời từ tất cả URL
    contents = await agent.extract_content_batch_async(urls, 
                                                       max_workers=5, 
                                                       timeout=30, 
                                                       respect_robots=True)
    
    return contents

# Chạy tác vụ bất đồng bộ
contents = asyncio.run(search_and_extract())
```

### Tối ưu bộ nhớ cache

```python
# Khởi tạo với cấu hình cache tùy chỉnh
agent = WebSearchAgentLocal(
    cache_enabled=True,
    cache_dir="./cache",
    cache_expiry=86400,  # 1 ngày
    cache_size_limit=100,  # MB
    cache_compression=True
)
```

### Sử dụng trình trích xuất theo pool

```python
from src.deep_research_core.utils.extraction_pool import ContentExtractionPool

# Khởi tạo pool trích xuất
pool = ContentExtractionPool(
    max_workers=5,
    extraction_timeout=30,
    use_advanced_extractor=True,
    handle_javascript=True
)

# Trích xuất nội dung từ nhiều URL
urls = ["https://example1.com", "https://example2.com", "https://example3.com"]
results = pool.extract_batch(urls)

# Đóng pool khi hoàn thành
pool.shutdown()
```

## Lưu ý bổ sung

1. **Xử lý ngoại lệ**: Luôn bọc các phương thức trích xuất trong khối try-except để xử lý các lỗi có thể xảy ra.
2. **Robots.txt**: Đặt `respect_robots=True` để tuân thủ quy tắc robots.txt của trang web.
3. **Giới hạn tốc độ**: Sử dụng `rate_limit` để tránh gửi quá nhiều yêu cầu đến cùng một domain trong thời gian ngắn.
4. **Tiết kiệm tài nguyên**: Đóng các tài nguyên (browser instances, connections) khi không sử dụng.
5. **Proxy rotation**: Sử dụng `proxy_list` để thay đổi địa chỉ IP khi cần thiết.

## Kết luận

Các tính năng nâng cao của WebSearchAgentLocal giúp cải thiện đáng kể khả năng trích xuất nội dung từ các trang web hiện đại, đặc biệt là các trang web có cấu trúc phức tạp, sử dụng nhiều JavaScript, và có nội dung động. Việc tích hợp các công cụ này vào quy trình làm việc của bạn sẽ giúp nâng cao chất lượng dữ liệu thu thập được. 