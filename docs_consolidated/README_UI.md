# Giao diện người dùng quản lý dữ liệu đánh giá độ tin cậy

Đây là giao diện web để quản lý dữ liệu đánh giá độ tin cậy nguồn thông tin cho hệ thống WebSearchAgentLocal. Giao diện cung cấp các tính năng quản lý danh sách domain tin cậy, domain không tin cậy, điểm tin cậy, danh mục domain và chủ đề.

## Cài đặt

Cài đặt các gói phụ thuộc cần thiết:

```bash
pip install -r requirements_ui.txt
```

## Chạy ứng dụng

Chạy ứng dụng web với lệnh sau:

```bash
python3 examples/credibility_ui_example.py
```

Hoặc sử dụng các tùy chọn:

```bash
python3 examples/credibility_ui_example.py --host 127.0.0.1 --port 5001 --data-dir data/credibility
```

## Tùy chọn

- `--host`: Địa chỉ host để chạy ứng dụng (mặc định: 127.0.0.1)
- `--port`: Cổng để chạy ứng dụng (mặc định: 5000)
- `--no-browser`: Không mở trình duyệt tự động
- `--data-dir`: Thư mục lưu trữ dữ liệu (mặc định: data/credibility)
- `--debug`: Chạy ứng dụng ở chế độ debug

## Tính năng

Ứng dụng web cung cấp các tính năng sau:

### 1. Quản lý domain tin cậy
- Xem danh sách domain tin cậy
- Thêm domain mới vào danh sách tin cậy
- Xóa domain khỏi danh sách tin cậy
- Tìm kiếm domain trong danh sách

### 2. Quản lý domain không tin cậy
- Xem danh sách domain không tin cậy
- Thêm domain mới vào danh sách không tin cậy
- Xóa domain khỏi danh sách không tin cậy
- Tìm kiếm domain trong danh sách

### 3. Quản lý điểm tin cậy domain
- Xem danh sách domain có điểm tin cậy
- Cập nhật điểm tin cậy cho domain
- Xóa điểm tin cậy của domain
- Tìm kiếm domain trong danh sách

### 4. Quản lý danh mục domain
- Xem danh sách domain và danh mục của chúng
- Thêm danh mục mới cho domain
- Xóa danh mục của domain
- Tìm kiếm domain và danh mục

### 5. Quản lý chủ đề
- Xem danh sách chủ đề
- Thêm chủ đề mới
- Xóa chủ đề
- Thêm domain vào chủ đề với mức độ tin cậy
- Xóa domain khỏi chủ đề

### 6. Kiểm tra độ tin cậy
- Kiểm tra độ tin cậy của URL hoặc domain
- Xem thông tin chi tiết về độ tin cậy

## Cấu trúc thư mục

```
src/deep_research_core/ui/
├── app.py              # Ứng dụng Flask chính
├── run.py              # Script để chạy ứng dụng
├── static/             # Thư mục tài nguyên tĩnh
│   ├── css/            # Thư mục CSS
│   │   └── style.css   # File CSS chính
│   └── js/             # Thư mục JavaScript
│       └── main.js     # File JavaScript chính
└── templates/          # Thư mục template Jinja2
    ├── base.html       # Template cơ sở
    ├── index.html      # Trang chủ
    ├── trusted_domains.html      # Trang quản lý domain tin cậy
    ├── untrusted_domains.html    # Trang quản lý domain không tin cậy
    ├── domain_scores.html        # Trang quản lý điểm tin cậy
    ├── domain_categories.html    # Trang quản lý danh mục domain
    └── topics.html               # Trang quản lý chủ đề
```

## API

Ứng dụng cung cấp các API sau:

1. `POST /api/evaluate_url`: Đánh giá độ tin cậy của URL
   - Input: `{"url": "https://example.com/page"}`
   - Output: Thông tin chi tiết về độ tin cậy

2. `POST /api/evaluate_domain`: Đánh giá độ tin cậy của domain
   - Input: `{"domain": "example.com"}`
   - Output: Thông tin chi tiết về độ tin cậy

## Ví dụ

Xem file `examples/credibility_ui_example.py` để biết cách sử dụng ứng dụng web quản lý dữ liệu đánh giá độ tin cậy. 