# Danh sách công việc (Tasks)

Tài liệu này theo dõi các công việc cần thực hiện cho dự án Deep Research Core.

## Công việc đang thực hiện

### Tìm kiếm tiếng Việt

- [x] Cải thiện hệ thống cache cho tìm kiếm tiếng Việt
  - [x] Thêm cấu trúc cache đa cấp
  - [x] Cài đặt tính năng ghi nhớ kết quả tìm kiếm phổ biến
  - [x] Tối ưu hóa dung lượng cache

- [x] Thêm tính năng xếp hạng kết quả tìm kiếm tiếng Việt
  - [x] Phát triển thuật toán xếp hạng kết quả dựa trên ngữ cảnh tiếng Việt
  - [x] Tích hợ<PERSON> hệ thống đánh giá độ liên quan
  - [x] Cài đặt tùy chọn sắp xếp kết quả tùy chỉnh

- [x] Cải thiện khả năng xử lý biến thể từ tiếng Việt không dấu
  - [x] Xây dựng module chuyển đổi từ không dấu sang có dấu
  - [x] Cải thiện thuật toán so khớp từ khóa
  - [x] Thêm hỗ trợ từ đồng nghĩa tiếng Việt

### Kiểm thử

- [ ] Viết kiểm thử đơn vị cho các module mới
  - [x] Kiểm thử cho module cache
  - [x] Kiểm thử cho thuật toán xếp hạng
  - [x] Kiểm thử cho module xử lý từ không dấu

- [ ] Cập nhật kiểm thử tích hợp
  - [ ] Mở rộng test case cho tìm kiếm tích hợp
  - [ ] Thêm kiểm thử hiệu suất

### Tài liệu

- [x] Cập nhật tài liệu hướng dẫn sử dụng
  - [x] Thêm ví dụ về các tính năng mới
  - [x] Cập nhật phần cấu hình

- [x] Tạo tài liệu API
  - [x] Viết tài liệu API cho mỗi module
  - [x] Tạo ví dụ sử dụng API

## Công việc sắp tới

### Giai đoạn 3: Đánh giá độ tin cậy thông tin

- [x] Xây dựng hệ thống đánh giá độ tin cậy của nguồn thông tin
  - [x] Thiết kế cấu trúc dữ liệu cho thông tin độ tin cậy
  - [x] Xây dựng thuật toán đánh giá cơ bản

- [ ] Phát triển bộ phát hiện tin giả và thông tin sai lệch
  - [x] Nghiên cứu các mẫu tin giả phổ biến
  - [x] Cài đặt các quy tắc phát hiện cơ bản
  - [ ] Cải thiện độ chính xác phát hiện

- [ ] Tích hợp với các API kiểm tra sự thật
  - [ ] Xác định các API kiểm tra sự thật phù hợp
  - [ ] Tạo module tích hợp

- [x] Xây dựng cơ sở dữ liệu nguồn tin cậy tiếng Việt
  - [x] Thu thập danh sách các nguồn tin cậy tiếng Việt
  - [x] Phân loại độ tin cậy cho các nguồn

- [x] Phát triển mô hình đánh giá nội dung tiếng Việt
  - [x] Nghiên cứu các đặc điểm của nội dung sai lệch tiếng Việt
  - [x] Phát triển bộ phân tích văn bản tiếng Việt

### Kiểm thử

- [ ] Viết kiểm thử đơn vị cho các module mới
  - [x] Kiểm thử cho module cache
  - [x] Kiểm thử cho thuật toán xếp hạng
  - [x] Kiểm thử cho module xử lý từ không dấu
  - [x] Kiểm thử cho module đánh giá độ tin cậy

- [ ] Cập nhật kiểm thử tích hợp
  - [ ] Mở rộng test case cho tìm kiếm tích hợp
  - [ ] Thêm kiểm thử hiệu suất

### Tài liệu

- [x] Cập nhật tài liệu hướng dẫn sử dụng
  - [x] Thêm ví dụ về các tính năng mới
  - [x] Cập nhật phần cấu hình

- [x] Tạo tài liệu API
  - [x] Viết tài liệu API cho mỗi module
  - [x] Tạo ví dụ sử dụng API

## Công việc sắp tới

### Giai đoạn 4: Tối ưu hóa hiệu suất và triển khai

- [ ] Tối ưu hóa hiệu suất
  - [ ] Áp dụng xử lý bất đồng bộ cho tìm kiếm đa nguồn
  - [ ] Cải thiện hiệu suất phân tích nội dung

- [ ] Triển khai ứng dụng
  - [ ] Phát triển API RESTful
  - [ ] Xây dựng giao diện web demo
  - [ ] Chuẩn bị tài liệu triển khai

- [ ] Cải thiện tính bảo mật
  - [ ] Cài đặt hệ thống xoay vòng User-Agent
  - [ ] Thêm chế độ proxy để tránh bị chặn
  - [ ] Thêm giới hạn tốc độ và bảo vệ IP

## Công việc đã hoàn thành

### Nền tảng tìm kiếm cơ bản

- [x] Xây dựng WebSearchAgentLocal
- [x] Phát triển tính năng tìm kiếm tiếng Việt cơ bản
- [x] Tạo hệ thống phát hiện ngôn ngữ
- [x] Tích hợp các nguồn tìm kiếm tiếng Việt
- [x] Viết kiểm thử cơ bản

### Mở rộng tính năng tìm kiếm tiếng Việt

- [x] Cải thiện bộ phát hiện ngôn ngữ nâng cao
- [x] Tối ưu hóa truy vấn tiếng Việt
- [x] Thêm nhiều nguồn tìm kiếm tiếng Việt
- [x] Viết tài liệu hướng dẫn đầy đủ
- [x] Cải thiện hệ thống cache cho tìm kiếm tiếng Việt
- [x] Cải thiện khả năng xử lý biến thể từ tiếng Việt không dấu
- [x] Tạo tài liệu API cho module tiếng Việt không dấu
- [x] Thêm tính năng xếp hạng kết quả tìm kiếm tiếng Việt

### Đánh giá độ tin cậy thông tin

- [x] Xây dựng hệ thống đánh giá độ tin cậy của nguồn thông tin tiếng Việt
- [x] Phát triển bộ phát hiện clickbait và nội dung đáng ngờ
- [x] Xây dựng cơ sở dữ liệu nguồn tin cậy tiếng Việt
- [x] Tích hợp đánh giá độ tin cậy với hệ thống tìm kiếm
- [x] Tạo tài liệu hướng dẫn đánh giá độ tin cậy

## Vấn đề đã biết

### Tìm kiếm tiếng Việt

- [ ] Một số trang web chặn requests tự động, cần cài đặt cơ chế xoay vòng User-Agent
- [ ] Hiệu suất giảm khi tìm kiếm đồng thời từ nhiều nguồn
- [x] Bị giới hạn số lượng request từ một số nguồn tìm kiếm

### Phát hiện ngôn ngữ

- [x] Không phát hiện được truy vấn tiếng Việt ngắn không dấu
- [ ] Đôi khi phát hiện sai tiếng Việt với một số ngôn ngữ Đông Nam Á khác

### Đánh giá độ tin cậy

- [ ] Danh sách nguồn tin cậy chưa đầy đủ
- [ ] Phát hiện clickbait và nội dung đáng ngờ chưa chính xác hoàn toàn
- [ ] Chưa có khả năng kiểm tra thông tin với các nguồn bên ngoài

## Đề xuất cải tiến

- [x] Sử dụng Redis cho cache phân tán
- [ ] Thêm chế độ proxy để tránh bị chặn
- [ ] Tích hợp với underthesea để cải thiện NLP tiếng Việt
- [ ] Phát triển API RESTful cho dịch vụ tìm kiếm
- [ ] Triển khai ứng dụng web demo

## Công việc ưu tiên cao

- [x] Sửa lỗi linter trong `request_security.py`
- [x] Sửa lỗi linter trong `rest_api.py`
- [x] Tạo file README.md cho dự án
- [x] Tạo file cấu hình mẫu cho hệ thống API
- [x] Tạo file kế hoạch phát triển (PLANNING.md)
- [x] Tạo file quản lý công việc (TASKS.md)

## Phát triển tính năng

### Đánh giá độ tin cậy
- [ ] Cập nhật danh sách nguồn tin cậy tiếng Việt
- [ ] Thêm phương pháp đánh giá độ tin cậy dựa trên nội dung
- [ ] Tối ưu hóa thuật toán đánh giá độ tin cậy
- [ ] Thêm phân tích xu hướng chính trị

### Phát hiện tin giả
- [ ] Thêm các mẫu tin giả mới
- [ ] Tối ưu hóa thuật toán phát hiện tin giả
- [ ] Thêm phân tích hình ảnh để phát hiện tin giả
- [ ] Tổ chức lại cấu trúc mã nguồn

### Tích hợp kiểm tra sự thật
- [ ] Thêm các nguồn kiểm tra sự thật tiếng Việt mới
- [ ] Tối ưu hóa thuật toán tìm kiếm thông tin kiểm chứng
- [ ] Thêm phân tích ngữ cảnh cho tuyên bố cần kiểm tra
- [ ] Cải thiện phương pháp đánh giá độ tin cậy dựa trên kết quả kiểm tra

### Tìm kiếm bất đồng bộ
- [ ] Cải thiện hiệu suất tìm kiếm song song
- [ ] Thêm các chiến lược kết hợp kết quả mới
- [ ] Tối ưu hóa xử lý timeout
- [ ] Thêm các nguồn tìm kiếm mới

### Bảo mật yêu cầu
- [ ] Thêm danh sách proxy miễn phí tự động cập nhật
- [ ] Cải thiện thuật toán xoay vòng User-Agent
- [ ] Thêm phương pháp phát hiện bot detection
- [ ] Tối ưu hóa thuật toán giới hạn tốc độ

## Kiểm thử và tài liệu

### Kiểm thử
- [ ] Thêm test case cho module `vietnamese_source_credibility`
- [ ] Thêm test case cho module `async_search`
- [ ] Thêm test case cho module `request_security`
- [ ] Thêm test case tích hợp cho toàn bộ hệ thống
- [ ] Thiết lập CI/CD với GitHub Actions

### Tài liệu
- [ ] Tạo hướng dẫn API chi tiết
- [ ] Tạo hướng dẫn sử dụng bằng tiếng Việt
- [ ] Tạo tài liệu kiến trúc hệ thống
- [ ] Tạo tài liệu cấu hình và triển khai

## Cải tiến kỹ thuật

### Hiệu suất
- [ ] Tối ưu hóa bộ nhớ cache
- [ ] Giảm thời gian phản hồi API
- [ ] Cải thiện thuật toán xử lý tiếng Việt
- [ ] Giảm sử dụng tài nguyên khi tìm kiếm

### Cấu trúc mã nguồn
- [ ] Tổ chức lại cấu trúc thư mục dự án
- [ ] Chuẩn hóa kiểu dữ liệu với Pydantic
- [ ] Tách riêng các thành phần phụ thuộc
- [ ] Tạo các interface chuẩn để dễ dàng mở rộng

### Triển khai
- [ ] Tạo Dockerfile cho dự án
- [ ] Thiết lập Docker Compose để dễ dàng triển khai
- [ ] Tạo script cài đặt tự động
- [ ] Tạo hướng dẫn triển khai trên các nền tảng khác nhau

## Lỗi cần sửa

- [x] Sửa lỗi import trong `request_security.py`
- [x] Sửa lỗi validator trong `rest_api.py`
- [x] Sửa lỗi deep_analysis không tồn tại trong `rest_api.py`
- [ ] Sửa các lỗi tương thích Type Checking trong toàn bộ dự án
- [ ] Sửa lỗi xử lý Unicode không đúng trong các module xử lý tiếng Việt
- [ ] Chuẩn hóa logger trong toàn bộ dự án 