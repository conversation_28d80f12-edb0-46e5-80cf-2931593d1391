# Thiết kế module UserAgentManager dùng chung

Dựa trên phân tích các module User-Agent hi<PERSON><PERSON> t<PERSON>, tôi đề xuất thiết kế module UserAgentManager dùng chung như sau:

## Cấu trúc module

```python
import os
import random
import threading
import time
import logging
from typing import Dict, List, Optional, Any, Tuple, Union

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

class UserAgentManager:
    """
    Module quản lý User-Agent dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    
    # Danh sách User-Agent mặc định
    DEFAULT_USER_AGENTS = [
        # Windows Chrome
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
        
        # Windows Firefox
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:118.0) Gecko/20100101 Firefox/118.0",
        
        # Windows Edge
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0",
        
        # macOS Chrome
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        
        # macOS Safari
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15",
        
        # Linux Chrome
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        
        # Linux Firefox
        "Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (X11; Linux x86_64; rv:119.0) Gecko/20100101 Firefox/119.0",
        
        # Mobile Chrome (Android)
        "Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.43 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; SM-A536B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.163 Mobile Safari/537.36",
        
        # Mobile Safari (iOS)
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    ]
    
    # Trọng số trình duyệt mặc định
    DEFAULT_BROWSER_WEIGHTS = {
        "chrome": 0.5,
        "firefox": 0.2,
        "safari": 0.15,
        "edge": 0.1,
        "opera": 0.05
    }
    
    # Trọng số hệ điều hành mặc định
    DEFAULT_OS_WEIGHTS = {
        "windows": 0.6,
        "macos": 0.2,
        "linux": 0.1,
        "android": 0.05,
        "ios": 0.05
    }
    
    def __init__(
        self,
        user_agents: Optional[List[str]] = None,
        user_agent_file: Optional[str] = None,
        rotation_strategy: str = "round_robin",
        rotation_interval: int = 10,
        device_type: str = "all",
        browser_type: str = "all",
        os_type: str = "all",
        include_random: bool = False,
        random_ratio: float = 0.1,
        browser_weights: Optional[Dict[str, float]] = None,
        os_weights: Optional[Dict[str, float]] = None,
        verbose: bool = False
    ):
        """
        Khởi tạo UserAgentManager.
        
        Args:
            user_agents: Danh sách User-Agent
            user_agent_file: Đường dẫn đến file chứa danh sách User-Agent
            rotation_strategy: Chiến lược xoay vòng ("round_robin", "random", "weighted")
            rotation_interval: Khoảng thời gian xoay vòng (số lần sử dụng)
            device_type: Loại thiết bị ("all", "desktop", "mobile", "tablet", "bot")
            browser_type: Loại trình duyệt ("all", "chrome", "firefox", "safari", "edge", "opera")
            os_type: Loại hệ điều hành ("all", "windows", "macos", "linux", "android", "ios")
            include_random: Có bao gồm User-Agent ngẫu nhiên hay không
            random_ratio: Tỷ lệ sử dụng User-Agent ngẫu nhiên (0.0 đến 1.0)
            browser_weights: Trọng số cho các trình duyệt
            os_weights: Trọng số cho các hệ điều hành
            verbose: Ghi log chi tiết
        """
        # Khởi tạo các thuộc tính
        self.user_agents = user_agents or self.DEFAULT_USER_AGENTS.copy()
        self.user_agent_file = user_agent_file
        self.rotation_strategy = rotation_strategy
        self.rotation_interval = rotation_interval
        self.device_type = device_type
        self.browser_type = browser_type
        self.os_type = os_type
        self.include_random = include_random
        self.random_ratio = random_ratio
        self.browser_weights = browser_weights or self.DEFAULT_BROWSER_WEIGHTS.copy()
        self.os_weights = os_weights or self.DEFAULT_OS_WEIGHTS.copy()
        self.verbose = verbose
        
        # Khởi tạo các thuộc tính bổ sung
        self.current_index = 0
        self.usage_count = 0
        self.filtered_user_agents = []
        self.user_agent_stats = {}
        self.user_agent_lock = threading.Lock()
        
        # Khởi tạo các thành phần
        self._initialize_components()
        
        logger.info(f"UserAgentManager được khởi tạo với {len(self.filtered_user_agents)} User-Agent")
    
    def _initialize_components(self):
        """Khởi tạo các thành phần."""
        # Tải User-Agent từ file nếu có
        if self.user_agent_file and os.path.exists(self.user_agent_file):
            self._load_user_agents_from_file()
        
        # Lọc User-Agent theo loại thiết bị, trình duyệt và hệ điều hành
        self._filter_user_agents()
        
        # Khởi tạo thống kê cho mỗi User-Agent
        for user_agent in self.filtered_user_agents:
            self.user_agent_stats[user_agent] = {
                "usage_count": 0,
                "last_used": 0
            }
```

## Các phương thức chính

1. **get_user_agent(strategy=None)**: Lấy User-Agent theo chiến lược xoay vòng
2. **add_user_agent(user_agent)**: Thêm User-Agent vào danh sách
3. **remove_user_agent(user_agent)**: Xóa User-Agent khỏi danh sách
4. **filter_user_agents(device_type, browser_type, os_type)**: Lọc User-Agent theo loại thiết bị, trình duyệt và hệ điều hành
5. **load_user_agents_from_file(file_path)**: Tải danh sách User-Agent từ file
6. **generate_random_user_agent()**: Tạo User-Agent ngẫu nhiên
7. **get_stats()**: Lấy thống kê về việc sử dụng User-Agent

## Các chiến lược xoay vòng

1. **round_robin**: Xoay vòng tuần tự
2. **random**: Xoay vòng ngẫu nhiên
3. **weighted**: Xoay vòng theo trọng số

## Các tính năng đặc biệt

1. **Lọc User-Agent**: Lọc User-Agent theo loại thiết bị, trình duyệt và hệ điều hành
2. **Tạo User-Agent ngẫu nhiên**: Tạo User-Agent ngẫu nhiên dựa trên trọng số trình duyệt và hệ điều hành
3. **Thống kê sử dụng**: Theo dõi thống kê sử dụng User-Agent
4. **Tải từ file**: Tải danh sách User-Agent từ file
5. **Khóa đồng bộ**: Sử dụng khóa đồng bộ để tránh xung đột

## Cách tích hợp

### Tích hợp vào WebSearchAgentLocal

```python
from ..utils.user_agent_manager import UserAgentManager

def integrate_user_agent_manager(agent, config=None):
    """
    Tích hợp UserAgentManager vào WebSearchAgentLocal.
    """
    # Cấu hình mặc định
    default_config = {
        "rotation_strategy": "round_robin",
        "device_type": "desktop",
        "browser_type": "all",
        "os_type": "all",
        "include_random": True,
        "random_ratio": 0.1
    }
    
    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    user_agent_config = {**default_config, **(config or {})}
    
    # Khởi tạo UserAgentManager
    agent._user_agent_manager = UserAgentManager(**user_agent_config)
    
    # Thêm phương thức get_user_agent vào agent
    agent._get_rotated_user_agent = lambda: agent._user_agent_manager.get_user_agent()
```

### Tích hợp vào AdaptiveCrawler

```python
from ..utils.user_agent_manager import UserAgentManager

def integrate_user_agent_manager(crawler, config=None):
    """
    Tích hợp UserAgentManager vào AdaptiveCrawler.
    """
    # Cấu hình mặc định
    default_config = {
        "rotation_strategy": "weighted",
        "device_type": "all",
        "browser_type": "all",
        "os_type": "all",
        "include_random": True,
        "random_ratio": 0.2
    }
    
    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    user_agent_config = {**default_config, **(config or {})}
    
    # Khởi tạo UserAgentManager
    crawler._user_agent_manager = UserAgentManager(**user_agent_config)
    
    # Thay thế phương thức get_random_user_agent
    crawler.get_random_user_agent = lambda: crawler._user_agent_manager.get_user_agent()
```
