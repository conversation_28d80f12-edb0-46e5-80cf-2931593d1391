# Kết quả kiểm tra tìm kiếm sâu (Deep Crawl)
Thời gian: 2025-05-15 02:26:38

### Deep Crawl trực tiếp với URL cụ thể

```
URL: https://www.python.org/
Thời gian crawl: 11.07 giây
Số trang đã crawl: 5

Các trang đã crawl:
Trang 1:
  URL: https://www.python.org/
  Tiêu đề: Welcome to Python.org
  Độ sâu: 0
  Số ký tự nội dung: 1981
  Số liên kết: 207

Trang 2:
  URL: https://www.python.org/psf/
  Tiêu đề: Python Software Foundation
  Độ sâu: 1
  Số ký tự nội dung: 2080
  Số liên kết: 152

Trang 3:
  URL: https://docs.python.org
  Tiêu đề: 3.13.3 Documentation
  Độ sâu: 1
  Số ký tự nội dung: 2511
  Số liên kết: 90

... và 2 trang khác

```

### Deep Crawl kết hợp với tìm kiếm

```
Câu hỏi: What are the key features of Python 3.9 and how do they improve developer productivity?
Thời gian tìm kiếm: 0.10 giây
Thành công: True
Số kết quả: 5

Kết quả tìm kiếm:
Kết quả 1:
  Tiêu đề: Python Programming - Wikipedia
  URL: https://en.wikipedia.org/wiki/Python_(programming_language)
  Snippet: Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code ...
  Source: local
  Độ dài nội dung: 565 ký tự
  Nội dung (trích đoạn): Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentatio...

Kết quả 2:
  Tiêu đề: Python.org
  URL: https://www.python.org/
  Snippet: The official home of the Python Programming Language. Python is a programming language that lets you...
  Source: local
  Độ dài nội dung: 530 ký tự
  Nội dung (trích đoạn): The official home of the Python Programming Language. Python is a programming language that lets you work quickly and integrate systems more effective...

Kết quả 3:
  Tiêu đề: Learn Python - Free Interactive Python Tutorial
  URL: https://www.learnpython.org/
  Snippet: Learn Python, a powerful programming language used for many different applications. Learn Python int...
  Source: local
  Độ dài nội dung: 559 ký tự
  Nội dung (trích đoạn): Learn Python, a powerful programming language used for many different applications. Learn Python interactively with our free Python tutorial.

More in...

... và 2 kết quả khác

```

### Deep Crawl với câu hỏi phức tạp

```
Câu hỏi: Compare and contrast the performance characteristics of Python's asyncio, threading, and multiprocessing modules for different types of I/O-bound and CPU-bound tasks, including specific code examples and benchmarks.
Thời gian tìm kiếm: 0.10 giây
Thành công: True
Số kết quả: 5

Đánh giá câu hỏi:
Độ phức tạp: medium
Điểm phức tạp: 0.5122448148148149

Chiến lược đề xuất: auto
Số kết quả đề xuất: 7
Trích xuất nội dung: True
Tìm kiếm sâu: False

Kết quả tìm kiếm:
Kết quả 1:
  Tiêu đề: Python Programming - Wikipedia
  URL: https://en.wikipedia.org/wiki/Python_(programming_language)
  Snippet: Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code ...
  Source: local
  Độ dài nội dung: 565 ký tự
  Nội dung (trích đoạn): Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentatio...

Kết quả 2:
  Tiêu đề: Python.org
  URL: https://www.python.org/
  Snippet: The official home of the Python Programming Language. Python is a programming language that lets you...
  Source: local
  Độ dài nội dung: 530 ký tự
  Nội dung (trích đoạn): The official home of the Python Programming Language. Python is a programming language that lets you work quickly and integrate systems more effective...

Kết quả 3:
  Tiêu đề: Learn Python - Free Interactive Python Tutorial
  URL: https://www.learnpython.org/
  Snippet: Learn Python, a powerful programming language used for many different applications. Learn Python int...
  Source: local
  Độ dài nội dung: 559 ký tự
  Nội dung (trích đoạn): Learn Python, a powerful programming language used for many different applications. Learn Python interactively with our free Python tutorial.

More in...

... và 2 kết quả khác

```

### Deep Crawl với câu hỏi tiếng Việt

```
Câu hỏi: So sánh và phân tích chi tiết các đặc điểm của Python và JavaScript trong phát triển ứng dụng web, bao gồm hiệu suất, bảo mật và khả năng mở rộng.
Thời gian tìm kiếm: 0.10 giây
Thành công: True
Số kết quả: 5

Đánh giá câu hỏi:
Độ phức tạp: high
Điểm phức tạp: 0.7100882086021505

Chiến lược đề xuất: crawlee
Số kết quả đề xuất: 12
Trích xuất nội dung: True
Tìm kiếm sâu: True

Kết quả tìm kiếm:
Kết quả 1:
  Tiêu đề: Python Programming - Wikipedia
  URL: https://en.wikipedia.org/wiki/Python_(programming_language)
  Snippet: Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code ...
  Source: local
  Độ dài nội dung: 565 ký tự
  Nội dung (trích đoạn): Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentatio...

Kết quả 2:
  Tiêu đề: Python.org
  URL: https://www.python.org/
  Snippet: The official home of the Python Programming Language. Python is a programming language that lets you...
  Source: local
  Độ dài nội dung: 530 ký tự
  Nội dung (trích đoạn): The official home of the Python Programming Language. Python is a programming language that lets you work quickly and integrate systems more effective...

Kết quả 3:
  Tiêu đề: Learn Python - Free Interactive Python Tutorial
  URL: https://www.learnpython.org/
  Snippet: Learn Python, a powerful programming language used for many different applications. Learn Python int...
  Source: local
  Độ dài nội dung: 559 ký tự
  Nội dung (trích đoạn): Learn Python, a powerful programming language used for many different applications. Learn Python interactively with our free Python tutorial.

More in...

... và 2 kết quả khác

```

### Deep Crawl khi câu trả lời ban đầu kém

```
Câu hỏi: What are the advanced features of Python's asyncio module?
Câu trả lời ban đầu: Asyncio is a module in Python.

Đánh giá chất lượng câu trả lời:
Chất lượng: low
Điểm chất lượng: 0.3845357142857143
Cần tìm kiếm thêm: True

Chỉ số relevance:
  Điểm: 4.285714285714286
  Giải thích: Đánh giá tính liên quan dựa trên sự trùng lặp từ khóa và cấu trúc câu trả lời.

Chỉ số completeness:
  Điểm: 1.0083333333333333
  Giải thích: Đánh giá tính đầy đủ dựa trên độ dài câu trả lời và mức độ bao phủ các khía cạnh của câu hỏi.

Chỉ số source_attribution:
  Điểm: 7.0
  Giải thích: Đánh giá trích dẫn nguồn dựa trên việc sử dụng thông tin từ tài liệu.

Thời gian tìm kiếm sâu: 0.10 giây
Thành công: True
Số kết quả: 5

Kết quả tìm kiếm sâu:
Kết quả 1:
  Tiêu đề: Python Programming - Wikipedia
  URL: https://en.wikipedia.org/wiki/Python_(programming_language)
  Snippet: Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code ...
  Source: local
  Độ dài nội dung: 565 ký tự
  Nội dung (trích đoạn): Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentatio...

Kết quả 2:
  Tiêu đề: Python.org
  URL: https://www.python.org/
  Snippet: The official home of the Python Programming Language. Python is a programming language that lets you...
  Source: local
  Độ dài nội dung: 530 ký tự
  Nội dung (trích đoạn): The official home of the Python Programming Language. Python is a programming language that lets you work quickly and integrate systems more effective...

Kết quả 3:
  Tiêu đề: Learn Python - Free Interactive Python Tutorial
  URL: https://www.learnpython.org/
  Snippet: Learn Python, a powerful programming language used for many different applications. Learn Python int...
  Source: local
  Độ dài nội dung: 559 ký tự
  Nội dung (trích đoạn): Learn Python, a powerful programming language used for many different applications. Learn Python interactively with our free Python tutorial.

More in...


```

