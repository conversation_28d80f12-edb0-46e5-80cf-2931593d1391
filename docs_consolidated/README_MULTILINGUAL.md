# Hỗ trợ Đa Ngôn Ngữ Cho Hệ Thống Đánh Giá Độ Tin Cậy

Tài liệu này mô tả cách sử dụng và mở rộng tính năng đa ngôn ngữ của hệ thống đánh giá độ tin cậy nguồn thông tin.

## Tổng quan

Hệ thống đánh giá độ tin cậy nguồn thông tin đã được mở rộng để hỗ trợ phân tích và đánh giá độ tin cậy cho nhiều ngôn ngữ khác nhau. Điều này cho phép hệ thống:

1. Ph<PERSON>t hiện ngôn ngữ của nội dung tự động
2. Á<PERSON> dụng các quy tắc đánh giá phù hợp với ngôn ngữ được phát hiện
3. Sử dụng dữ liệu đặc thù cho từng ngôn ngữ (từ khóa thiên vị, mẫu click<PERSON>it, quy tắc ngữ pháp)
4. <PERSON><PERSON><PERSON> b<PERSON><PERSON> cáo đ<PERSON>h giá độ tin cậy phù hợp với ngôn ngữ nội dung

## Các ngôn ngữ được hỗ trợ

Hiện tại, hệ thống hỗ trợ các ngôn ngữ sau:

- Tiếng Anh (en)
- Tiếng Việt (vi)
- Tiếng Pháp (fr)
- Tiếng Đức (de)
- Tiếng Tây Ban Nha (es)
- Tiếng Trung (zh)
- Tiếng Nhật (ja)
- Tiếng Hàn (ko)
- Tiếng Nga (ru)
- Tiếng Ả Rập (ar)

## Cài đặt

Để sử dụng tính năng đa ngôn ngữ, bạn cần cài đặt các gói phụ thuộc bổ sung:

```bash
pip install -r requirements_ui.txt
```

Một số gói như `pyicu`, `pycld2` và `morpholibpy` có thể yêu cầu các thư viện hệ thống. Trên Ubuntu/Debian, bạn có thể cài đặt chúng bằng:

```bash
sudo apt-get install python3-dev libicu-dev pkg-config
```

## Cấu trúc dữ liệu

Dữ liệu đa ngôn ngữ được lưu trữ trong thư mục `data/credibility/multilingual` với cấu trúc sau:

```
data/credibility/multilingual/
├── bias_words_en.json        # Từ khóa thiên vị tiếng Anh
├── bias_words_vi.json        # Từ khóa thiên vị tiếng Việt
├── bias_words_fr.json        # Từ khóa thiên vị tiếng Pháp
├── clickbait_patterns_en.json # Mẫu clickbait tiếng Anh
├── clickbait_patterns_vi.json # Mẫu clickbait tiếng Việt
├── clickbait_patterns_fr.json # Mẫu clickbait tiếng Pháp
├── grammar_rules_en.json     # Quy tắc ngữ pháp tiếng Anh
├── grammar_rules_vi.json     # Quy tắc ngữ pháp tiếng Việt
├── grammar_rules_fr.json     # Quy tắc ngữ pháp tiếng Pháp
└── ... (và các file cho các ngôn ngữ khác)
```

## Sử dụng

### Phát hiện ngôn ngữ tự động

```python
from src.deep_research_core.utils.language_support import LanguageDetector

detector = LanguageDetector()
text = "Đây là một đoạn văn bản tiếng Việt"
language = detector.detect_language(text)
print(f"Ngôn ngữ được phát hiện: {language}")  # Output: Ngôn ngữ được phát hiện: vi
```

### Phân tích nội dung đa ngôn ngữ

```python
from src.deep_research_core.utils.language_support import MultilingualContentAnalyzer

analyzer = MultilingualContentAnalyzer()
text = "Đây là một đoạn văn bản tiếng Việt với một số từ ngữ thiên vị như chắc chắn, tuyệt đối, luôn luôn."
result = analyzer.analyze_content(text)
print(f"Ngôn ngữ: {result['language']}")
print(f"Điểm thiên vị: {result['bias_analysis']['bias_score']}")
print(f"Điểm chất lượng tổng thể: {result['overall_quality_score']}")
```

### Sử dụng trong CredibilityEvaluator

```python
from src.deep_research_core.utils.credibility_evaluator import CredibilityEvaluator

evaluator = CredibilityEvaluator(multilingual_enabled=True)
url = "https://example.com/article"
content = "Đây là một đoạn văn bản tiếng Việt của bài báo."
result = evaluator.evaluate_url(url, content)
print(f"Ngôn ngữ: {result['content_evaluation']['language']}")
print(f"Điểm tin cậy: {result['credibility_score']}")
```

Hoặc bạn có thể chỉ định ngôn ngữ:

```python
result = evaluator.evaluate_url(url, content, language="vi")
```

## Mở rộng

### Thêm từ khóa thiên vị cho một ngôn ngữ mới

Để thêm từ khóa thiên vị cho một ngôn ngữ mới, bạn cần tạo file JSON tương ứng trong thư mục `data/credibility/multilingual`:

```json
[
    "từ_khóa_1",
    "từ_khóa_2",
    "từ_khóa_3",
    ...
]
```

### Thêm mẫu clickbait cho một ngôn ngữ mới

Tương tự, bạn có thể thêm mẫu clickbait cho một ngôn ngữ mới:

```json
[
    "(?i)\\b(mẫu_regex_1)\\b",
    "(?i)\\b(mẫu_regex_2)\\b",
    ...
]
```

### Thêm quy tắc ngữ pháp cho một ngôn ngữ mới

```json
{
    "ruleSets": [
        {
            "name": "Tên tập quy tắc",
            "rules": [
                {
                    "id": "rule_id",
                    "pattern": "(?i)\\b(mẫu_regex)\\b",
                    "message": "Thông báo lỗi",
                    "weight": 0.5
                },
                ...
            ]
        },
        ...
    ]
}
```

### Thêm hỗ trợ cho một ngôn ngữ mới

Để thêm hỗ trợ cho một ngôn ngữ mới, bạn cần cập nhật file `src/deep_research_core/utils/language_support.py`:

1. Thêm ngôn ngữ mới vào `SUPPORTED_LANGUAGES`:

```python
SUPPORTED_LANGUAGES = {
    'en': 'English',
    'vi': 'Vietnamese',
    # Thêm ngôn ngữ mới ở đây
    'new_lang_code': 'New Language Name'
}
```

2. Thêm ánh xạ file dữ liệu cho ngôn ngữ mới vào `LANGUAGE_DATA_MAPPING`:

```python
LANGUAGE_DATA_MAPPING = {
    # Các ngôn ngữ hiện có
    'new_lang_code': {
        'bias_words_file': 'bias_words_new_lang_code.json',
        'clickbait_patterns_file': 'clickbait_patterns_new_lang_code.json',
        'grammar_rules_file': 'grammar_rules_new_lang_code.json'
    }
}
```

3. Tạo các file dữ liệu tương ứng trong thư mục `data/credibility/multilingual`.

## Ví dụ

Kiểm tra độ tin cậy của một URL có nội dung tiếng Việt:

```python
from src.deep_research_core.utils.credibility_evaluator import CredibilityEvaluator

evaluator = CredibilityEvaluator(multilingual_enabled=True)
url = "https://example.com/article"
content = """
Đây là một bài báo tiếng Việt. Chắc chắn rằng thông tin này là chính xác 100%. 
Không có ai có thể phủ nhận điều này. Tất cả mọi người đều đồng ý rằng đây là 
sự thật tuyệt đối. Bạn sẽ không tin được điều sắp xảy ra tiếp theo!
"""

report = evaluator.get_credibility_report(url, content)
print(f"Ngôn ngữ: {report['language']}")
print(f"Điểm tin cậy: {report['credibility_score']}")
print(f"Mức độ tin cậy: {report['credibility_level']}")
print(f"Lời khuyên: {report['advice']}")
```

## Ghi chú

- Nếu không có thư viện phát hiện ngôn ngữ (langdetect, polyglot), hệ thống sẽ sử dụng phương pháp phát hiện đơn giản dựa trên các ký tự đặc trưng của ngôn ngữ.
- Bạn có thể cấu hình hệ thống để tắt tính năng đa ngôn ngữ bằng cách đặt `multilingual_enabled=False` khi khởi tạo `CredibilityEvaluator`.
- Ngôn ngữ mặc định là tiếng Anh (en). Bạn có thể thay đổi ngôn ngữ mặc định bằng cách đặt `default_language="vi"` khi khởi tạo.

## Công cụ đánh giá ngữ pháp

Hệ thống sử dụng thư viện `language-tool-python` để kiểm tra ngữ pháp, hỗ trợ nhiều ngôn ngữ bao gồm:
- Tiếng Anh (en-US, en-GB, en-CA, en-AU, en-NZ, en-ZA)
- Tiếng Việt (vi-VN)
- Tiếng Pháp (fr)
- Tiếng Đức (de-DE)
- Tiếng Tây Ban Nha (es)
- Và nhiều ngôn ngữ khác

Khi thư viện này không khả dụng, hệ thống sẽ sử dụng các quy tắc được định nghĩa trong các file grammar_rules_*.json. 