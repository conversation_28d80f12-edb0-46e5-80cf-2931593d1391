# Website Crawler

Công cụ để crawl toàn bộ một website sử dụng AdaptiveCrawler từ module deepresearch.

## Tính năng

- Crawl toàn bộ một website từ một URL bắt đầu
- Giới hạn số trang tối đa và độ sâu tối đa
- Tùy chọn tôn trọng hoặc bỏ qua robots.txt
- Sử dụng Playwright để xử lý các trang web động với JavaScript
- Lưu kết quả dưới dạng Markdown và JSON
- Lọc các trang cùng tên miền với URL bắt đầu
- Trích xuất nội dung, tiêu đề, liên kết từ các trang web

## Cài đặt

Đảm bảo bạn đã cài đặt các thư viện cần thiết:

```bash
pip install requests beautifulsoup4 playwright
```

<PERSON><PERSON><PERSON> bạn muốn sử dụng Playwright, bạn cần cài đặt các trình duyệt:

```bash
playwright install
```

## Sử dụng

### Dòng lệnh

```bash
python website_crawler.py https://example.com --output results.md --max-pages 100 --max-depth 3
```

### Các tham số

- `url`: URL bắt đầu (bắt buộc)
- `--output`, `-o`: Đường dẫn file để lưu kết quả (mặc định: crawl_results.md)
- `--max-pages`, `-p`: Số trang tối đa để crawl (mặc định: 100)
- `--max-depth`, `-d`: Độ sâu tối đa để crawl (mặc định: 3)
- `--timeout`, `-t`: Thời gian chờ tối đa cho mỗi request (giây) (mặc định: 30)
- `--respect-robots`, `-r`: Tôn trọng robots.txt (mặc định: False)
- `--no-playwright`, `-n`: Không sử dụng Playwright (mặc định: False)

### Ví dụ

1. Crawl một website với các tham số mặc định:

```bash
python website_crawler.py https://example.com
```

2. Crawl một website với số trang tối đa là 50 và độ sâu tối đa là 2:

```bash
python website_crawler.py https://example.com --max-pages 50 --max-depth 2
```

3. Crawl một website và tôn trọng robots.txt:

```bash
python website_crawler.py https://example.com --respect-robots
```

4. Crawl một website không sử dụng Playwright:

```bash
python website_crawler.py https://example.com --no-playwright
```

## Kết quả

Kết quả sẽ được lưu vào hai file:

1. File Markdown (mặc định: crawl_results.md): Chứa thông tin về quá trình crawl và danh sách các trang đã crawl dưới dạng dễ đọc.
2. File JSON (tên file Markdown với phần mở rộng .json): Chứa dữ liệu thô dưới dạng JSON.

### Cấu trúc file Markdown

```
# Kết quả crawl website: https://example.com
Thời gian: 2023-05-15 12:34:56 - 2023-05-15 12:35:30
Thời gian crawl: 34.50 giây
Tổng số trang: 50
Số trang cùng tên miền: 45
Độ sâu tối đa: 3
Tôn trọng robots.txt: False
Sử dụng Playwright: True

## Danh sách các trang đã crawl

### 1. Trang chủ
URL: https://example.com/
Độ sâu: 0
Số ký tự nội dung: 1234
Số liên kết: 20
Nguồn: playwright

#### Nội dung (trích đoạn)
```
Nội dung trang web...
```

### 2. Giới thiệu
...
```

### Cấu trúc file JSON

```json
{
  "crawl_info": {
    "url": "https://example.com",
    "domain": "example.com",
    "start_time": "2023-05-15 12:34:56",
    "end_time": "2023-05-15 12:35:30",
    "duration": 34.5,
    "total_pages": 50,
    "same_domain_pages": 45,
    "max_pages": 100,
    "max_depth": 3,
    "respect_robots_txt": false,
    "use_playwright": true
  },
  "pages": [
    {
      "url": "https://example.com/",
      "title": "Trang chủ",
      "content": "Nội dung trang web...",
      "links": ["https://example.com/about", "https://example.com/contact", ...],
      "depth": 0,
      "source": "playwright"
    },
    ...
  ]
}
```

## Sử dụng trong mã Python

Bạn cũng có thể sử dụng hàm `crawl_website` trong mã Python của bạn:

```python
from website_crawler import crawl_website

crawl_info = crawl_website(
    url="https://example.com",
    output_file="results.md",
    max_pages=100,
    max_depth=3,
    timeout=30,
    respect_robots_txt=False,
    use_playwright=True
)

print(f"Đã crawl {crawl_info['total_pages']} trang trong {crawl_info['duration']:.2f} giây")
```

## Lưu ý

- Crawl một website lớn có thể mất nhiều thời gian và tài nguyên.
- Đảm bảo bạn có quyền crawl website đó và không vi phạm các điều khoản dịch vụ.
- Sử dụng tùy chọn `--respect-robots` để tôn trọng robots.txt nếu bạn không chắc chắn.
- Sử dụng tùy chọn `--max-pages` và `--max-depth` để giới hạn số lượng trang và độ sâu crawl.
- Sử dụng Playwright để xử lý các trang web động với JavaScript, nhưng nó có thể chậm hơn so với requests.
