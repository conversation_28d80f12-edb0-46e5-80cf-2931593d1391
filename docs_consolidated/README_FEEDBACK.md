# Hệ Thống Phản Hồi Để Cải Thiện Độ Chính Xác Đánh Giá

Tài liệu này mô tả cách sử dụng và mở rộng hệ thống phản hồi để cải thiện độ chính xác của đánh giá độ tin cậy nguồn thông tin theo thời gian.

## Tổng quan

Hệ thống phản hồi cho phép thu thập và phân tích phản hồi của người dùng về độ tin cậy của các nguồn thông tin, từ đó điều chỉnh các đánh giá trong tương lai để cải thiện độ chính xác. Hệ thống này hoạt động dựa trên các nguyên tắc:

1. Thu thập phản hồi từ người dùng về độ tin cậy của các URL/domain
2. T<PERSON><PERSON> to<PERSON> sự chênh lệch giữa đánh giá của người dùng và hệ thống
3. <PERSON><PERSON><PERSON> lũy dữ liệu phản hồi theo thời gian để phát hiện xu hướng
4. Điều chỉnh trọng số dựa trên xu hướng phản hồi
5. Áp dụng các điều chỉnh này vào các đánh giá trong tương lai

## Cài đặt

Hệ thống phản hồi được tích hợp sẵn trong module `credibility_evaluator.py` và không yêu cầu cài đặt bổ sung ngoài các thư viện Python tiêu chuẩn.

## Cấu trúc dữ liệu

Hệ thống phản hồi lưu trữ dữ liệu trong thư mục `data/credibility/feedback` với cấu trúc sau:

```
data/credibility/feedback/
├── feedback_db.json        # Cơ sở dữ liệu phản hồi
├── feedback_stats.json     # Thống kê phản hồi
└── adjustment_weights.json # Trọng số điều chỉnh
```

## Sử dụng cơ bản

### Khởi tạo hệ thống

```python
from src.deep_research_core.utils.credibility_evaluator import CredibilityEvaluator

# Khởi tạo với hệ thống phản hồi được bật
evaluator = CredibilityEvaluator(
    feedback_enabled=True,
    learning_rate=0.1,  # Tốc độ học từ phản hồi (0.0-1.0)
    min_feedback_count=5,  # Số lượng phản hồi tối thiểu để áp dụng điều chỉnh
    verbose=True
)
```

### Thêm phản hồi người dùng

```python
# Đánh giá một URL
result = evaluator.evaluate_url("https://example.com/article")

# Thêm phản hồi người dùng
evaluator.add_user_feedback(
    url="https://example.com/article",
    user_score=0.8,  # Điểm đánh giá của người dùng (0.0-1.0)
    evaluation_result=result,  # Kết quả đánh giá của hệ thống
    feedback_text="Trang này đáng tin cậy hơn đánh giá của hệ thống"
)
```

Hoặc thêm phản hồi đơn giản hơn:

```python
evaluator.add_user_feedback(
    url="https://example.com/article",
    user_score=0.8,
    system_score=0.6,  # Điểm đánh giá của hệ thống
    domain="example.com",  # Domain (nếu không cung cấp sẽ tự động trích xuất)
    feedback_text="Trang này đáng tin cậy"
)
```

### Đánh giá với điều chỉnh từ phản hồi

Sau khi thêm đủ phản hồi (vượt qua `min_feedback_count`), các đánh giá mới sẽ tự động áp dụng điều chỉnh từ phản hồi:

```python
# Đánh giá URL (sẽ tự động áp dụng điều chỉnh từ phản hồi)
result = evaluator.evaluate_url("https://example.com/another-article")

# Kiểm tra xem có áp dụng điều chỉnh không
if result.get("has_feedback_adjustment", False):
    print("Đã áp dụng điều chỉnh từ phản hồi người dùng")
```

### Lấy thống kê phản hồi

```python
# Lấy thống kê phản hồi
feedback_stats = evaluator.get_feedback_stats()

# Lấy thông tin phản hồi cho một domain cụ thể
domain_feedback = evaluator.get_domain_feedback("example.com")

# Lấy thông tin điều chỉnh
adjustment_info = evaluator.get_feedback_adjustment("example.com", language="en")
```

### Tạo báo cáo phản hồi

```python
# Tạo báo cáo phản hồi tổng hợp
feedback_report = evaluator.generate_feedback_report()
```

## Các tham số quan trọng

### Tốc độ học (learning_rate)

Tham số `learning_rate` (0.0-1.0) kiểm soát mức độ ảnh hưởng của phản hồi người dùng đến điều chỉnh:
- Giá trị thấp (0.1): Thay đổi chậm, ổn định hơn
- Giá trị cao (0.5-1.0): Thay đổi nhanh, nhạy cảm hơn với phản hồi gần đây

```python
evaluator = CredibilityEvaluator(learning_rate=0.2)
```

### Số lượng phản hồi tối thiểu (min_feedback_count)

Tham số `min_feedback_count` quy định số lượng phản hồi tối thiểu cần có trước khi áp dụng điều chỉnh:
- Giá trị thấp (2-3): Áp dụng điều chỉnh sớm, nhưng có thể kém chính xác
- Giá trị cao (10+): Áp dụng điều chỉnh muộn hơn, nhưng chính xác hơn

```python
evaluator = CredibilityEvaluator(min_feedback_count=5)
```

## Ví dụ đầy đủ

Xem tệp `examples/feedback_system_example.py` để biết ví dụ đầy đủ về cách sử dụng hệ thống phản hồi.

## Mở rộng hệ thống

### Thêm tiêu chí đánh giá mới

Để thêm tiêu chí đánh giá mới vào hệ thống phản hồi:

1. Cập nhật phương thức `add_user_feedback` để thu thập phản hồi cho tiêu chí mới
2. Thêm tiêu chí mới vào `feedback_metrics` khi thêm phản hồi
3. Cập nhật phương thức `_calculate_combined_score` để áp dụng điều chỉnh cho tiêu chí mới

### Tích hợp với giao diện người dùng

Để tích hợp hệ thống phản hồi với giao diện người dùng:

1. Thêm tính năng đánh giá độ tin cậy vào giao diện
2. Tạo form cho người dùng cung cấp điểm đánh giá và nhận xét
3. Gửi phản hồi tới hệ thống qua phương thức `add_user_feedback`
4. Hiển thị thông tin về điều chỉnh phản hồi trong kết quả đánh giá

## Ghi chú

- Hệ thống phản hồi lưu trữ dữ liệu cục bộ trong các file JSON. Với ứng dụng lớn, bạn có thể cần chuyển sang cơ sở dữ liệu như SQLite hoặc MongoDB.
- Để tránh lạm dụng, bạn nên giới hạn số lượng phản hồi từ một người dùng cho một URL/domain cụ thể.
- Điều chỉnh từ phản hồi được giới hạn trong khoảng -0.3 đến 0.3 để tránh thay đổi quá mức.
- Hệ thống học theo thời gian, vì vậy độ chính xác sẽ tăng lên khi có nhiều phản hồi hơn.

## Quy trình làm việc điển hình

1. Hệ thống đánh giá độ tin cậy của một URL
2. Người dùng xem kết quả đánh giá và cung cấp phản hồi
3. Hệ thống lưu trữ phản hồi và cập nhật thống kê
4. Khi đủ phản hồi, hệ thống tính toán điều chỉnh cho domain và ngôn ngữ
5. Các đánh giá mới cho domain và ngôn ngữ đó sẽ áp dụng điều chỉnh
6. Theo thời gian, hệ thống học từ phản hồi và cải thiện độ chính xác 