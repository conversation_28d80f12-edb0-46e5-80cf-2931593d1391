# Thiết kế module Capt<PERSON><PERSON><PERSON><PERSON> dùng chung

Dựa trên phân tích các module CAPTC<PERSON> hiện tại, tôi đề xuất thiết kế module CaptchaHandler dùng chung như sau:

## Cấu trúc module

```python
from enum import Enum
import logging
import time
import re
import random
from typing import Dict, Any, List, Optional, Tuple, Union
from urllib.parse import urlparse
import requests
from bs4 import BeautifulSoup

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

class CaptchaType(Enum):
    """Các loại CAPTCHA được hỗ trợ."""
    UNKNOWN = "unknown"
    RECAPTCHA_V2 = "recaptcha_v2"
    RECAPTCHA_V3 = "recaptcha_v3"
    HCAPTCHA = "hcaptcha"
    IMAGE_CAPTCHA = "image_captcha"
    TEXT_CAPTCHA = "text_captcha"
    CLOUDFLARE = "cloudflare"
    VIETNAMESE = "vietnamese"

class CaptchaHandler:
    """
    Module xử lý CAPTCHA dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    def __init__(
        self,
        auto_solve: bool = False,
        use_selenium: bool = False,
        use_playwright: bool = True,
        anticaptcha_key: Optional[str] = None,
        max_retries: int = 5,
        retry_delay: float = 3.0,
        user_agents: Optional[List[str]] = None,
        user_agent_manager: Optional[Any] = None,
        timeout: int = 30,
        use_proxy: bool = False,
        proxies: Optional[List[str]] = None,
        proxy_rotation: bool = False,
        browser_emulation: bool = True,
        persistent_browser: bool = False,
        stealth_mode: bool = True,
        cache_captcha_domains: bool = True,
        cache_ttl: int = 3600 * 24,
        vietnamese_support: bool = True,
        verbose: bool = False,
        **kwargs
    ):
        """
        Khởi tạo CaptchaHandler.

        Args:
            auto_solve: Tự động giải quyết CAPTCHA
            use_selenium: Sử dụng Selenium để giải quyết CAPTCHA
            use_playwright: Sử dụng Playwright để giải quyết CAPTCHA
            anticaptcha_key: API key cho dịch vụ Anti-Captcha
            max_retries: Số lần thử lại tối đa
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            user_agents: Danh sách User-Agent để luân chuyển
            user_agent_manager: Đối tượng UserAgentManager để lấy User-Agent
            timeout: Thời gian chờ tối đa (giây)
            use_proxy: Sử dụng proxy hay không
            proxies: Danh sách proxy để luân chuyển
            proxy_rotation: Xoay vòng proxy hay không
            browser_emulation: Sử dụng giả lập trình duyệt hay không
            persistent_browser: Giữ trình duyệt mở giữa các lần gọi hay không
            stealth_mode: Sử dụng chế độ ẩn danh hay không
            cache_captcha_domains: Lưu cache domain có CAPTCHA hay không
            cache_ttl: Thời gian sống của cache (giây)
            vietnamese_support: Hỗ trợ CAPTCHA tiếng Việt hay không
            verbose: Ghi log chi tiết
            **kwargs: Các tham số bổ sung
        """
        # Khởi tạo các thuộc tính
        self.auto_solve = auto_solve
        self.use_selenium = use_selenium
        self.use_playwright = use_playwright
        self.anticaptcha_key = anticaptcha_key
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.user_agents = user_agents or []
        self.user_agent_manager = user_agent_manager
        self.timeout = timeout
        self.use_proxy = use_proxy
        self.proxies = proxies or []
        self.proxy_rotation = proxy_rotation
        self.browser_emulation = browser_emulation
        self.persistent_browser = persistent_browser
        self.stealth_mode = stealth_mode
        self.cache_captcha_domains = cache_captcha_domains
        self.cache_ttl = cache_ttl
        self.vietnamese_support = vietnamese_support
        self.verbose = verbose

        # Khởi tạo các thuộc tính bổ sung
        self.captcha_detections = {}  # Lưu thông tin về các lần phát hiện CAPTCHA
        self.known_captcha_domains = {}  # Lưu thông tin về các domain có CAPTCHA
        self.browser = None  # Đối tượng trình duyệt (Playwright hoặc Selenium)
        self.current_user_agent_index = 0  # Chỉ số User-Agent hiện tại

        # Khởi tạo các bộ phát hiện CAPTCHA
        self._initialize_detectors()

        # Khởi tạo các chiến lược xử lý CAPTCHA
        self._initialize_strategies()

        # Khởi tạo Playwright hoặc Selenium nếu cần
        self._initialize_browser()
```

## Các phương thức chính

1. **detect_captcha(html_content)**: Phát hiện CAPTCHA trong nội dung HTML
2. **handle_captcha(url, html_content)**: Xử lý CAPTCHA
3. **_try_different_user_agent(url, html_content, captcha_type, captcha_data)**: Thử sử dụng User-Agent khác
4. **_try_delay_and_retry(url, html_content, captcha_type, captcha_data)**: Thử chờ và thử lại
5. **_try_browser_emulation(url, html_content, captcha_type, captcha_data)**: Thử giả lập trình duyệt
6. **_try_vietnamese_browser_emulation(url, html_content, captcha_type, captcha_data)**: Thử giả lập trình duyệt với cấu hình tiếng Việt
7. **is_domain_known_captcha(url)**: Kiểm tra xem domain có CAPTCHA đã biết hay không
8. **clear_captcha_cache()**: Xóa cache CAPTCHA

## Các tính năng đặc biệt

1. **Hỗ trợ nhiều loại CAPTCHA**: reCAPTCHA v2, reCAPTCHA v3, hCAPTCHA, Cloudflare, CAPTCHA hình ảnh, CAPTCHA văn bản, CAPTCHA tiếng Việt
2. **Hỗ trợ nhiều chiến lược xử lý CAPTCHA**: Thay đổi User-Agent, chờ và thử lại, giả lập trình duyệt
3. **Hỗ trợ cả Playwright và Selenium**: Có thể sử dụng cả Playwright và Selenium để giả lập trình duyệt
4. **Hỗ trợ CAPTCHA tiếng Việt**: Có thể phát hiện và xử lý CAPTCHA tiếng Việt
5. **Cache domain có CAPTCHA**: Lưu cache domain có CAPTCHA để tránh truy cập lại
6. **Tích hợp với UserAgentManager**: Có thể sử dụng UserAgentManager để lấy User-Agent

## Cách tích hợp

### Tích hợp vào WebSearchAgentLocal

```python
from ..utils.captcha_handler import CaptchaHandler

def integrate_captcha_handler(agent, config=None):
    """
    Tích hợp CaptchaHandler vào WebSearchAgentLocal.
    """
    # Cấu hình mặc định
    default_config = {
        "use_playwright": True,
        "browser_emulation": True,
        "vietnamese_support": True,
        "cache_captcha_domains": True,
    }
    
    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    captcha_config = {**default_config, **(config or {})}
    
    # Khởi tạo CaptchaHandler
    agent._captcha_handler = CaptchaHandler(**captcha_config)
    
    # Thêm phương thức handle_captcha vào agent
    agent.handle_captcha = lambda html_content, url: agent._captcha_handler.handle_captcha(url, html_content)
```

### Tích hợp vào AdaptiveCrawler

```python
from ..utils.captcha_handler import CaptchaHandler

def integrate_captcha_handler(crawler, config=None):
    """
    Tích hợp CaptchaHandler vào AdaptiveCrawler.
    """
    # Cấu hình mặc định
    default_config = {
        "use_playwright": True,
        "browser_emulation": True,
        "vietnamese_support": True,
        "cache_captcha_domains": True,
    }
    
    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    captcha_config = {**default_config, **(config or {})}
    
    # Khởi tạo CaptchaHandler
    crawler._captcha_handler = CaptchaHandler(**captcha_config)
    
    # Thay thế phương thức _is_captcha và _solve_captcha
    crawler._is_captcha = lambda content: crawler._captcha_handler.detect_captcha(content)[0]
    crawler._solve_captcha = lambda url, response: crawler._captcha_handler.handle_captcha(url, response.get("content", "")).get("success", False)
```
