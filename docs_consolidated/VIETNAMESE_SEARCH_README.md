# Tính năng Tìm kiếm Tiếng Việt Nâng cao cho WebSearchAgentLocal

Tài liệu này mô tả tính năng tìm kiếm tiếng Việt nâng cao đã được tích hợp vào WebSearchAgentLocal, gi<PERSON><PERSON> cải thiện đáng kể khả năng tìm kiếm thông tin tiếng Việt.

## Tổng quan

Tính năng tìm kiếm tiếng Việt nâng cao cung cấp khả năng tìm kiếm thông tin từ các nguồn tiếng Việt như:

- **Cốc Cốc**: C<PERSON>ng cụ tìm kiếm phổ biến ở Việt Nam
- **Wiki Tiếng Việt**: Wikipedia tiếng Việt
- **Báo Mới**: Trang tổng hợp tin tức tiếng Việt
- **Google Vietnam**: google.com.vn
- **VnExpress**: Báo điện tử tiếng Việt phổ biến
- **Zing News**: Báo điện tử tiếng Việt phổ biến

Hệ thống sẽ tự động phát hiện khi người dùng nhập truy vấn tiếng Việt và sử dụng các phương thức tìm kiếm đặc biệt để cải thiện kết quả.

## Tính năng chính

1. **Phát hiện ngôn ngữ tự động nâng cao**:
   - Phát hiện ngôn ngữ dựa trên mẫu ký tự đặc biệt
   - Phát hiện dựa trên từ vựng đặc trưng
   - Kết hợp nhiều phương pháp phát hiện với trọng số
   - Hỗ trợ nhiều ngôn ngữ (vi, en, fr, de, es, zh, ja, ko, ru, ar, th)
   - Cache kết quả phát hiện để tăng hiệu suất

2. **Tối ưu hóa truy vấn tiếng Việt**:
   - Loại bỏ dấu câu không cần thiết
   - Loại bỏ từ dừng tiếng Việt phổ biến (với câu truy vấn dài)

3. **Tìm kiếm từ nhiều nguồn tiếng Việt**:
   - Tìm kiếm kết hợp từ tất cả các nguồn
   - Tìm kiếm từ một nguồn cụ thể

4. **Tích hợp liền mạch với WebSearchAgentLocal**:
   - Tự động phát hiện và sử dụng tìm kiếm tiếng Việt
   - Fallback về phương thức tìm kiếm chuẩn nếu tìm kiếm tiếng Việt thất bại

5. **Làm giàu kết quả tìm kiếm với thông tin ngôn ngữ**:
   - Thêm metadata về ngôn ngữ vào kết quả tìm kiếm
   - Hỗ trợ phân tích nội dung đa ngôn ngữ

## Cài đặt và Sử dụng

### Cài đặt

Để sử dụng tính năng tìm kiếm tiếng Việt và phát hiện ngôn ngữ nâng cao, bạn cần cài đặt các gói phụ thuộc sau:

```bash
pip install beautifulsoup4 requests

# Tuỳ chọn: Thư viện phát hiện ngôn ngữ nâng cao
pip install langdetect langid
```

Để sử dụng phát hiện ngôn ngữ nâng cao hơn nữa với fastText:

```bash
pip install fasttext
```

### Tích hợp vào WebSearchAgentLocal

Tính năng tìm kiếm tiếng Việt có thể được tích hợp vào WebSearchAgentLocal bằng cách sử dụng script `vietnamese_search_integration_setup.py`:

```python
from src.deep_research_core.utils.vietnamese_search_integration_setup import create_enhanced_agent

# Tạo instance của WebSearchAgentLocal với khả năng tìm kiếm tiếng Việt
agent = create_enhanced_agent()

# Thực hiện tìm kiếm (sẽ tự động phát hiện truy vấn tiếng Việt)
results = agent.search("Thủ đô của Việt Nam")
```

### Cấu hình

Bạn có thể tùy chỉnh tính năng tìm kiếm tiếng Việt và phát hiện ngôn ngữ bằng cách cung cấp cấu hình khi tạo agent:

```python
config = {
    "vietnamese_search": {
        "enable_vietnamese_search": True,
        "vietnamese_search_sources": ["coccoc", "wikitiengviet", "baomoi", "google_vietnam", "vnexpress", "zingnews"],
        "vietnamese_search_timeout": 10,
        "vietnamese_search_retry_count": 3,
        "optimize_vietnamese_query": True,
        "use_advanced_language_detector": True,
        "language_detector_config": {
            "supported_languages": ["vi", "en", "fr", "de", "es", "zh"],
            "min_length": 10,
            "default_language": "en",
            "confidence_threshold": 0.5,
            "cache_dir": "/path/to/cache",
            "max_cache_size": 10000,
            "use_fasttext": False,
            "fasttext_model_path": None
        }
    }
}

agent = create_enhanced_agent(config=config)
```

### Sử dụng trực tiếp các phương thức tìm kiếm tiếng Việt

Bạn có thể sử dụng trực tiếp các phương thức tìm kiếm tiếng Việt thay vì phụ thuộc vào phát hiện tự động:

```python
# Tìm kiếm tiếng Việt từ tất cả các nguồn
results = agent.search_vietnamese("Hà Nội là thủ đô của nước nào")

# Tìm kiếm tiếng Việt từ một nguồn cụ thể
results = agent.search_vietnamese_source("vnexpress", "Giá vàng hôm nay")
```

### Sử dụng bộ phát hiện ngôn ngữ nâng cao độc lập

Bạn cũng có thể sử dụng bộ phát hiện ngôn ngữ độc lập:

```python
from src.deep_research_core.utils.advanced_language_detector import (
    AdvancedLanguageDetector,
    detect_language,
    is_vietnamese
)

# Sử dụng hàm tiện ích
lang, conf = detect_language("Xin chào thế giới")
print(f"Ngôn ngữ: {lang}, Độ tin cậy: {conf}")

# Kiểm tra tiếng Việt
if is_vietnamese("Đây là một câu tiếng Việt"):
    print("Đây là tiếng Việt")

# Sử dụng lớp AdvancedLanguageDetector đầy đủ
detector = AdvancedLanguageDetector({
    "supported_languages": ["vi", "en", "zh", "ja", "ko"],
    "cache_dir": "/tmp/language_cache"
})

# Phát hiện ngôn ngữ
lang, conf = detector.detect_language("こんにちは世界")
print(f"Ngôn ngữ: {lang}, Độ tin cậy: {conf}")

# Làm giàu kết quả tìm kiếm với thông tin ngôn ngữ
results = {
    "title": "Kết quả tìm kiếm",
    "results": [
        {"title": "Kết quả 1", "snippet": "Đây là kết quả tiếng Việt"},
        {"title": "Result 2", "snippet": "This is an English result"}
    ]
}

enriched_results = detector.augment_with_language_info(results)
```

## API

### WebSearchAgentLocal

Sau khi tích hợp, WebSearchAgentLocal sẽ có thêm các phương thức sau:

#### search_vietnamese

```python
def search_vietnamese(self, query, num_results=10, **kwargs):
    """
    Tìm kiếm tiếng Việt với các nguồn tìm kiếm Việt Nam.
    
    Args:
        query: Từ khóa tìm kiếm
        num_results: Số kết quả tối đa trả về
        **kwargs: Các tham số tìm kiếm khác
        
    Returns:
        dict: Kết quả tìm kiếm
    """
```

#### search_vietnamese_source

```python
def search_vietnamese_source(self, source, query, num_results=10, **kwargs):
    """
    Tìm kiếm tiếng Việt với một nguồn cụ thể.
    
    Args:
        source: Nguồn tìm kiếm (coccoc, wikitiengviet, baomoi, google_vietnam, vnexpress, zingnews)
        query: Từ khóa tìm kiếm
        num_results: Số kết quả tối đa trả về
        **kwargs: Các tham số tìm kiếm khác
        
    Returns:
        dict: Kết quả tìm kiếm
    """
```

### AdvancedLanguageDetector

Lớp phát hiện ngôn ngữ nâng cao cung cấp các phương thức sau:

#### detect_language

```python
def detect_language(self, text: str) -> Tuple[str, float]:
    """
    Phát hiện ngôn ngữ của văn bản.
    
    Args:
        text: Văn bản cần phát hiện ngôn ngữ
        
    Returns:
        Tuple[str, float]: (Mã ngôn ngữ, độ tin cậy)
    """
```

#### is_vietnamese

```python
def is_vietnamese(self, text: str) -> bool:
    """
    Kiểm tra xem văn bản có phải tiếng Việt hay không.
    
    Args:
        text: Văn bản cần kiểm tra
        
    Returns:
        bool: True nếu là tiếng Việt, False nếu không
    """
```

#### augment_with_language_info

```python
def augment_with_language_info(self, data: Dict) -> Dict:
    """
    Bổ sung thông tin về ngôn ngữ vào dữ liệu.
    
    Args:
        data: Dữ liệu cần bổ sung thông tin ngôn ngữ
        
    Returns:
        Dict: Dữ liệu đã được bổ sung thông tin ngôn ngữ
    """
```

### Các phương thức tìm kiếm tiếng Việt cơ bản

Các phương thức tìm kiếm tiếng Việt cơ bản được định nghĩa trong module `vietnamese_search_methods.py`:

- `search_coccoc`: Tìm kiếm với Cốc Cốc
- `search_wikitiengviet`: Tìm kiếm với Wiki tiếng Việt
- `search_baomoi`: Tìm kiếm với Báo Mới
- `search_google_vietnam`: Tìm kiếm với Google Vietnam
- `search_vnexpress`: Tìm kiếm với VnExpress
- `search_zingnews`: Tìm kiếm với Zing News
- `search_vietnamese_combined`: Tìm kiếm kết hợp từ nhiều nguồn
- `optimize_vietnamese_query`: Tối ưu hóa truy vấn tiếng Việt

## Ví dụ

### Tìm kiếm tự động

```python
from src.deep_research_core.utils.vietnamese_search_integration_setup import create_enhanced_agent

# Tạo instance của WebSearchAgentLocal với khả năng tìm kiếm tiếng Việt
agent = create_enhanced_agent()

# Thực hiện tìm kiếm (sẽ tự động phát hiện đây là truy vấn tiếng Việt)
results = agent.search("Thủ đô của Việt Nam")

# In ra kết quả
if isinstance(results, dict) and results.get("success") and results.get("results"):
    print(f"Tìm thấy {len(results['results'])} kết quả:")
    for i, result in enumerate(results["results"][:3], 1):
        print(f"{i}. {result['title']}")
        print(f"   URL: {result['url']}")
        print(f"   Mô tả: {result['snippet']}")
        print(f"   Ngôn ngữ: {result.get('metadata', {}).get('language', {}).get('code', 'không xác định')}")
        print()
else:
    print("Không tìm thấy kết quả hoặc tìm kiếm thất bại")
```

### Tìm kiếm từ nguồn cụ thể với phát hiện ngôn ngữ

```python
# Tìm kiếm với VnExpress
results = agent.search_vietnamese_source("vnexpress", "Giá vàng hôm nay")

# In ra kết quả
if isinstance(results, dict) and results.get("success") and results.get("results"):
    print(f"Tìm thấy {len(results['results'])} kết quả từ VnExpress:")
    for i, result in enumerate(results["results"][:3], 1):
        print(f"{i}. {result['title']}")
        print(f"   URL: {result['url']}")
        print(f"   Mô tả: {result['snippet']}")
        
        # Hiển thị thông tin ngôn ngữ nếu có
        if "metadata" in result and "language" in result["metadata"]:
            lang_info = result["metadata"]["language"]
            print(f"   Ngôn ngữ: {lang_info['code']} (độ tin cậy: {lang_info['confidence']:.2f})")
        print()
else:
    print("Không tìm thấy kết quả hoặc tìm kiếm thất bại")
```

## Lưu ý

1. **Giới hạn tốc độ**: Các nguồn tìm kiếm có thể giới hạn tốc độ truy cập. Nếu bạn gặp lỗi, hãy tăng giá trị timeout hoặc thử lại sau.

2. **CAPTCHA**: Một số nguồn tìm kiếm có thể yêu cầu giải CAPTCHA nếu phát hiện nhiều truy cập. Tính năng này đã được tích hợp với CaptchaHandler của WebSearchAgentLocal.

3. **Thay đổi cấu trúc trang web**: Nếu cấu trúc trang web của các nguồn tìm kiếm thay đổi, các phương thức tìm kiếm có thể cần được cập nhật.

4. **Phụ thuộc vào các thư viện phát hiện ngôn ngữ**: Để có hiệu suất tốt nhất cho việc phát hiện ngôn ngữ, bạn nên cài đặt thêm `langdetect` và `langid`.

## Lịch sử phát triển

- **v1.0.0**: Bản phát hành đầu tiên
  - Thêm phương thức tìm kiếm Cốc Cốc, Wiki tiếng Việt, Báo Mới
  - Tích hợp cơ bản với WebSearchAgentLocal

- **v1.1.0**: Cải tiến
  - Thêm phương thức tìm kiếm Google Vietnam, VnExpress, Zing News
  - Cải thiện phát hiện truy vấn tiếng Việt
  - Thêm tối ưu hóa truy vấn tiếng Việt

- **v1.2.0**: Phát hiện ngôn ngữ nâng cao
  - Thêm bộ phát hiện ngôn ngữ nâng cao với nhiều phương pháp kết hợp
  - Hệ thống cache để tăng hiệu suất phát hiện ngôn ngữ
  - Làm giàu kết quả tìm kiếm với thông tin ngôn ngữ
  - Hỗ trợ nhiều ngôn ngữ ngoài tiếng Việt

## Phát triển trong tương lai

Các tính năng dự kiến phát triển trong tương lai:

1. **Hỗ trợ thêm nguồn tìm kiếm tiếng Việt**:
   - Cốc Cốc API chính thức
   - Google News tiếng Việt
   - Báo Tuổi Trẻ, Thanh Niên

2. **Cải thiện phân tích ngữ nghĩa tiếng Việt**:
   - Phân tích từ loại tiếng Việt
   - Xử lý đồng nghĩa/trái nghĩa tiếng Việt

3. **Tích hợp với công cụ NLP tiếng Việt**:
   - VnCoreNLP
   - underthesea

4. **Tích hợp với các mô hình ngôn ngữ lớn tiếng Việt**:
   - Sử dụng mô hình riêng cho tiếng Việt để phát hiện ngôn ngữ, phân tích nội dung, và xếp hạng kết quả 