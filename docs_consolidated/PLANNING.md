# Kế hoạch phát triển Deep Research Core

Tài liệu này mô tả kế hoạch phát triển cho dự án Deep Research Core, bao gồm cả lộ trình ngắn hạn và dài hạn.

## Mục tiêu tổng thể

1. X<PERSON>y dựng nền tảng nghiên cứu và tìm kiếm thông tin toàn diện
2. Cung cấp khả năng tìm kiếm nâng cao với hỗ trợ đặc biệt cho tiếng Việt
3. <PERSON><PERSON>t triển các công cụ phân tích và đánh giá độ tin cậy thông tin
4. T<PERSON><PERSON> hệ sinh thái mở và dễ mở rộng

## Lộ trình phát triển

### Giai đoạn 1: Nền tảng tìm kiếm cơ bản (Hoà<PERSON> thành)

- [x] X<PERSON><PERSON> dựng WebSearchAgentLocal
- [x] Ph<PERSON>t triển tính năng tìm kiếm tiếng Việt cơ bản
- [x] T<PERSON><PERSON> hệ thống phát hiện ngôn ngữ
- [x] Tích hợp các nguồn tìm kiếm tiếng Việt
- [x] Viết kiểm thử cơ bản

### Giai đoạn 2: Mở rộng tính năng tìm kiếm tiếng Việt (Hiện tại)

- [x] Cải thiện bộ phát hiện ngôn ngữ nâng cao
- [x] Tối ưu hóa truy vấn tiếng Việt
- [x] Thêm nhiều nguồn tìm kiếm tiếng Việt
- [x] Viết tài liệu hướng dẫn đầy đủ
- [ ] Cải thiện hệ thống cache cho tìm kiếm tiếng Việt
- [ ] Thêm tính năng xếp hạng kết quả tìm kiếm tiếng Việt
- [ ] Cải thiện khả năng xử lý biến thể từ tiếng Việt không dấu

### Giai đoạn 3: Đánh giá độ tin cậy thông tin (Sắp tới)

- [ ] Xây dựng hệ thống đánh giá độ tin cậy của nguồn thông tin
- [ ] Phát triển bộ phát hiện tin giả và thông tin sai lệch
- [ ] Tích hợp với các API kiểm tra sự thật
- [ ] Xây dựng cơ sở dữ liệu nguồn tin cậy tiếng Việt
- [ ] Phát triển mô hình đánh giá nội dung tiếng Việt

### Giai đoạn 4: Tích hợp mô hình ngôn ngữ lớn (LLM)

- [ ] Tích hợp với các LLM phổ biến (OpenAI, Hugging Face)
- [ ] Phát triển prompts đặc biệt cho tiếng Việt
- [ ] Tạo pipeline phân tích nội dung sử dụng LLM
- [ ] Xây dựng hệ thống tổng hợp thông tin tự động
- [ ] Tăng cường khả năng trích xuất thông tin chính xác

### Giai đoạn 5: Ứng dụng và giao diện người dùng

- [ ] Xây dựng API RESTful
- [ ] Phát triển ứng dụng web
- [ ] Tạo giao diện dòng lệnh (CLI)
- [ ] Phát triển SDK cho JavaScript/TypeScript
- [ ] Tích hợp với các nền tảng phổ biến (Slack, Discord)

## Các tính năng ưu tiên sắp tới

1. **Thêm các công cụ xử lý ngôn ngữ tự nhiên tiếng Việt**
   - Tích hợp với các thư viện NLP tiếng Việt (VnCoreNLP, underthesea)
   - Xây dựng bộ lọc từ dừng tiếng Việt tùy chỉnh
   - Thêm công cụ gợi ý sửa lỗi chính tả tiếng Việt

2. **Cải thiện khả năng phát hiện ngôn ngữ**
   - Tích hợp mô hình fastText để phát hiện ngôn ngữ chính xác hơn
   - Tạo tập dữ liệu ngôn ngữ hỗn hợp (Việt-Anh) để huấn luyện
   - Cải thiện khả năng phát hiện cho văn bản ngắn

3. **Mở rộng nguồn tìm kiếm tiếng Việt**
   - Thêm API tìm kiếm chính thức từ Cốc Cốc (nếu có)
   - Thêm nguồn tìm kiếm từ Báo Tuổi Trẻ, Thanh Niên
   - Tích hợp với Google News tiếng Việt

4. **Xây dựng hệ thống đánh giá độ tin cậy đặc biệt cho nguồn tiếng Việt**
   - Tạo danh sách trắng/đen các domain tiếng Việt
   - Phát triển mô hình đánh giá nội dung tiếng Việt
   - Tích hợp với các nguồn kiểm tra sự thật tiếng Việt

## Kiến trúc dài hạn

### Cấu trúc module tương lai

```
src/deep_research_core/
├── core/                  # Các chức năng cốt lõi
│   ├── config/            # Quản lý cấu hình
│   ├── cache/             # Quản lý cache
│   └── logging/           # Quản lý log
├── search/                # Tìm kiếm thông tin
│   ├── agents/            # Các agent tìm kiếm
│   ├── vietnamese/        # Tìm kiếm tiếng Việt
│   └── engines/           # Các engine tìm kiếm
├── credibility/           # Đánh giá độ tin cậy
│   ├── models/            # Các mô hình đánh giá
│   ├── evaluators/        # Các bộ đánh giá
│   └── detectors/         # Phát hiện tin giả
├── nlp/                   # Xử lý ngôn ngữ tự nhiên
│   ├── vietnamese/        # NLP tiếng Việt
│   ├── detector/          # Phát hiện ngôn ngữ
│   └── analyzers/         # Phân tích văn bản
├── integrations/          # Tích hợp bên ngoài
│   ├── factcheck/         # API kiểm tra sự thật
│   ├── llm/               # Tích hợp LLM
│   └── ml/                # Tích hợp ML
└── ui/                    # Giao diện người dùng
    ├── web/               # Ứng dụng web
    ├── api/               # API RESTful
    └── cli/               # Giao diện dòng lệnh
```

## Phụ thuộc và công nghệ

### Hiện tại

- Python 3.7+
- BeautifulSoup, Requests
- langdetect, langid (tùy chọn)
- Unittest cho kiểm thử

### Dự kiến bổ sung

- fastText cho phát hiện ngôn ngữ
- Redis cho cache
- Flask/FastAPI cho API
- React/Vue cho giao diện web
- VnCoreNLP, underthesea cho NLP tiếng Việt
- MongoDB/SQLite cho lưu trữ dữ liệu
- pytest cho kiểm thử nâng cao
- Thư viện khác: Trafilatura, Readability, Newspaper3k

## Đóng góp vào kế hoạch

Kế hoạch này sẽ được cập nhật thường xuyên. Nếu bạn có ý tưởng hoặc đề xuất, vui lòng mở issue hoặc gửi pull request.

# Kế hoạch triển khai PlaywrightHandler

## Tổng quan

PlaywrightHandler sẽ cung cấp một giao diện thống nhất cho việc sử dụng Playwright để tương tác với các trang web động. Module này giúp đơn giản hóa việc crawl các trang web sử dụng JavaScript và các công nghệ hiện đại, với khả năng xử lý các trang web phức tạp.

## Thiết kế

### Cấu trúc lớp

```python
class PlaywrightHandler:
    def __init__(self,
              headless: bool = True, 
              browser_type: str = "chromium", 
              user_agent: Optional[str] = None,
              proxy: Optional[Dict[str, str]] = None,
              viewport: Optional[Dict[str, int]] = None,
              timeout: int = 30000,
              cookies: Optional[List[Dict[str, Any]]] = None,
              storage_state: Optional[Dict[str, Any]] = None,
              device_scale_factor: float = 1.0,
              geolocation: Optional[Dict[str, float]] = None,
              locale: str = "en-US",
              timezone_id: str = "UTC",
              permissions: Optional[List[str]] = None,
              extra_http_headers: Optional[Dict[str, str]] = None,
              java_script_enabled: bool = True,
              bypass_csp: bool = False,
              user_agent_manager: Optional[Any] = None,
              verbose: bool = False,
              logger: Optional[logging.Logger] = None,
              **kwargs):
        pass
```

### Phương thức chính

1. **setup()**: Khởi tạo Playwright engine
2. **create_browser()**: Tạo instance trình duyệt (Chromium, Firefox, WebKit)
3. **create_context()**: Tạo context browser với các tùy chọn
4. **create_page()**: Tạo page mới trong context đã cho
5. **navigate()**: Điều hướng đến URL cụ thể
6. **extract_content()**: Trích xuất nội dung từ trang web
7. **extract_links()**: Trích xuất danh sách liên kết từ trang web
8. **close_page()**: Đóng page
9. **close_context()**: Đóng context
10. **close_browser()**: Đóng trình duyệt
11. **cleanup()**: Dọn dẹp tài nguyên

### Hàm tiện ích

```python
@staticmethod
def handle_playwright_session(url: str, **kwargs) -> Dict[str, Any]:
    """
    Hàm tiện ích để xử lý trọn vẹn một phiên Playwright.
    Tự động tạo trình duyệt, context, page, điều hướng, trích xuất nội dung và dọn dẹp.
    
    Args:
        url: URL cần truy cập
        **kwargs: Các tham số tùy chọn cho PlaywrightHandler
        
    Returns:
        Dict chứa nội dung đã trích xuất, trạng thái và các thông tin khác
    """
    pass
```

## Quản lý tài nguyên

PlaywrightHandler sẽ triển khai context manager protocol để đảm bảo việc dọn dẹp tài nguyên một cách tự động:

```python
def __enter__(self):
    self.setup()
    return self
    
def __exit__(self, exc_type, exc_val, exc_tb):
    self.cleanup()
```

## Xử lý lỗi

PlaywrightHandler sẽ bao gồm xử lý các lỗi phổ biến:

1. **TimeoutError**: Xử lý khi page không load trong thời gian quy định
2. **NavigationError**: Xử lý lỗi điều hướng
3. **NetworkError**: Xử lý lỗi mạng
4. **BrowserError**: Xử lý lỗi trình duyệt
5. **ResourceError**: Xử lý lỗi tài nguyên

## Tùy chọn nâng cao

1. **Chụp ảnh màn hình**: Chụp toàn bộ trang hoặc một phần
2. **Xuất PDF**: Xuất trang thành file PDF
3. **Tự động cuộn**: Tự động cuộn để load nội dung lazy-loading
4. **Chặn request**: Chặn các request không cần thiết (ads, analytics)
5. **Mô phỏng tương tác người dùng**: Click, typing, hover
6. **Xử lý JavaScript động**: Chờ và xử lý nội dung được tạo bởi JavaScript
7. **Xử lý AJAX**: Chờ và xử lý các request AJAX

# Kế hoạch triển khai FileProcessor

## Tổng quan

FileProcessor sẽ cung cấp một giao diện thống nhất cho việc xử lý và trích xuất nội dung từ nhiều định dạng file khác nhau. Module này giúp đơn giản hóa việc xử lý các file PDF, DOCX, XLSX, TXT, HTML, và các định dạng phổ biến khác, cung cấp khả năng trích xuất văn bản, metadata, và phân tích cấu trúc.

## Thiết kế

### Cấu trúc lớp

```python
class FileProcessor:
    def __init__(self,
                 file_path: Optional[str] = None,
                 file_content: Optional[bytes] = None,
                 file_type: Optional[str] = None,
                 encoding: str = 'utf-8',
                 chunk_size: int = 4096,
                 extract_metadata: bool = True,
                 extract_images: bool = False,
                 ocr_enabled: bool = False,
                 language: str = 'en',
                 temp_dir: Optional[str] = None,
                 max_file_size: int = 100 * 1024 * 1024,  # 100MB
                 timeout: int = 60,
                 verbose: bool = False,
                 logger: Optional[logging.Logger] = None,
                 **kwargs):
        pass
```

### Phương thức chính

1. **load_file()**: Tải file từ đường dẫn hoặc nội dung binary
2. **identify_file_type()**: Nhận dạng loại file dựa trên content hoặc extension
3. **extract_text()**: Trích xuất văn bản từ file
4. **extract_metadata()**: Trích xuất metadata từ file
5. **extract_images()**: Trích xuất hình ảnh từ file (nếu có)
6. **extract_tables()**: Trích xuất bảng từ file (PDF, DOCX, XLSX)
7. **convert_to_text()**: Chuyển đổi file sang định dạng văn bản thuần
8. **convert_to_html()**: Chuyển đổi file sang định dạng HTML
9. **split_into_chunks()**: Chia nội dung thành các phần nhỏ hơn
10. **cleanup()**: Dọn dẹp tài nguyên tạm thời

### Hàm tiện ích

```python
@staticmethod
def process_file(file_path: str, **kwargs) -> Dict[str, Any]:
    """
    Hàm tiện ích để xử lý trọn vẹn một file.
    Tự động tải file, trích xuất nội dung, metadata và dọn dẹp.
    
    Args:
        file_path: Đường dẫn đến file cần xử lý
        **kwargs: Các tham số tùy chọn cho FileProcessor
        
    Returns:
        Dict chứa nội dung đã trích xuất, metadata và các thông tin khác
    """
    pass
```

## Xử lý theo loại file

### PDF

- Sử dụng PyPDF2/PyMuPDF để trích xuất văn bản
- Trích xuất cấu trúc (heading, paragraph)
- Trích xuất bảng (sử dụng tabula-py)
- Trích xuất hình ảnh
- Xử lý OCR cho PDF có hình ảnh/scan (sử dụng Tesseract)

### DOCX

- Sử dụng python-docx để trích xuất văn bản
- Trích xuất cấu trúc (heading, paragraph, list)
- Trích xuất bảng
- Trích xuất hình ảnh nhúng

### XLSX/XLS

- Sử dụng pandas/openpyxl để trích xuất dữ liệu
- Chuyển đổi sheet thành DataFrame
- Xử lý các công thức và định dạng

### HTML

- Sử dụng BeautifulSoup/lxml để phân tích
- Trích xuất văn bản có cấu trúc
- Trích xuất bảng
- Xử lý CSS và JavaScript

### TXT/CSV

- Xử lý các encoding khác nhau
- Phát hiện định dạng CSV và parse dữ liệu

### Hình ảnh (PNG, JPG, etc.)

- Trích xuất văn bản từ hình ảnh sử dụng OCR
- Trích xuất metadata (EXIF)

## Quản lý tài nguyên

FileProcessor sẽ triển khai context manager protocol để đảm bảo việc dọn dẹp tài nguyên một cách tự động:

```python
def __enter__(self):
    return self
    
def __exit__(self, exc_type, exc_val, exc_tb):
    self.cleanup()
```

## Xử lý lỗi

FileProcessor sẽ bao gồm xử lý các lỗi phổ biến:

1. **FileNotFoundError**: Xử lý khi file không tồn tại
2. **PermissionError**: Xử lý khi không có quyền đọc file
3. **UnsupportedFileTypeError**: Xử lý khi loại file không được hỗ trợ
4. **FileTooLargeError**: Xử lý khi file quá lớn
5. **TimeoutError**: Xử lý khi xử lý file mất quá nhiều thời gian
6. **CorruptedFileError**: Xử lý khi file bị hỏng

## Tùy chọn nâng cao

1. **Xử lý file nén**: ZIP, RAR, TAR, etc.
2. **Xử lý file đa phương tiện**: MP3, MP4 (trích xuất metadata)
3. **Xử lý file mã nguồn**: Phân tích mã nguồn, trích xuất comment
4. **Tích hợp với OCR nâng cao**: Hỗ trợ nhiều ngôn ngữ và cải thiện độ chính xác
5. **Phát hiện nội dung trùng lặp**: So sánh nội dung giữa các file
6. **Phân tích ngữ nghĩa**: Trích xuất chủ đề, từ khóa, thực thể
7. **Xử lý song song**: Xử lý nhiều file cùng lúc 