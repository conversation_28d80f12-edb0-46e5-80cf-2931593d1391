# Kế hoạch nâng cấp AdaptiveCrawler

## Tổng quan

Tài liệu này mô tả kế hoạch nâng cấp AdaptiveCrawler để hỗ trợ crawl toàn bộ một trang web bao gồm nội dung, <PERSON><PERSON><PERSON>nh, file và HTML. Kế hoạch được chia thành các phần nhỏ có thể triển khai độc lập.

## Phân chia nhiệm vụ giữa các thành phần

### 1. AdaptiveCrawler
- **Vai trò**: <PERSON>i<PERSON><PERSON> phối tổng thể, quyết định chiến lược crawl
- **Nhiệm vụ**: <PERSON><PERSON><PERSON><PERSON> lý cấu hình, lọ<PERSON>, điều phối các phương pháp crawl

### 2. Playwright Integration
- **Vai trò**: <PERSON><PERSON> lý các trang web động có JavaScript
- **Nhiệm vụ**: Render JavaScript, tương tác với trang web, trích xuất nội dung

### 3. MemoryOptimizedCrawler
- **Vai trò**: <PERSON><PERSON><PERSON> <PERSON>u hóa bộ nhớ khi crawl nhiều trang
- **Nhiệm vụ**: Crawl theo batch, dọn dẹp bộ nhớ, giám sát tài nguyên

### 4. Crawlee
- **Vai trò**: Framework crawling mạnh mẽ
- **Nhiệm vụ**: Quản lý hàng đợi URL, xử lý request song song

## Nhiệm vụ nâng cấp

### A. Nâng cấp cấu trúc và tham số

| ID | Nhiệm vụ | Mô tả | Độ ưu tiên | Trạng thái |
|----|----------|-------|------------|------------|
| A1 | Thêm tham số crawl_mode | Bổ sung tham số với các giá trị "basic", "full_site", "content_only", "media_only" | Cao | Hoàn thành |
| A2 | Thêm tham số download_media | Cho phép tải xuống hình ảnh, video và các file đa phương tiện | Cao | Hoàn thành |
| A3 | Thêm tham số download_path | Đường dẫn để lưu các file đã tải xuống | Cao | Hoàn thành |
| A4 | Thêm tham số site_map_enabled | Tạo sitemap cho trang web đã crawl | Trung bình | Hoàn thành |
| A5 | Thêm tham số max_media_size_mb | Giới hạn kích thước file media có thể tải xuống | Trung bình | Hoàn thành |

### B. Cải thiện quản lý URL và điều hướng

| ID | Nhiệm vụ | Mô tả | Độ ưu tiên | Trạng thái |
|----|----------|-------|------------|------------|
| B1 | Cải thiện quản lý URL queue | Sử dụng cấu trúc dữ liệu ưu tiên để crawl các URL quan trọng trước | Cao | Hoàn thành |
| B2 | Thêm hỗ trợ sitemap.xml | Phát hiện và sử dụng sitemap.xml để tìm tất cả các URL | Trung bình | Hoàn thành |
| B3 | Cải thiện hỗ trợ robots.txt | Cải thiện việc phân tích robots.txt để tuân thủ các quy tắc | Trung bình | Hoàn thành |
| B4 | Thêm hỗ trợ pagination | Phát hiện và xử lý các trang phân trang | Cao | Hoàn thành |

### C. Cải thiện trích xuất nội dung

| ID | Nhiệm vụ | Mô tả | Độ ưu tiên | Trạng thái |
|----|----------|-------|------------|------------|
| C1 | Cải thiện trích xuất nội dung chính | Sử dụng thuật toán phức tạp hơn để xác định nội dung chính | Cao | Hoàn thành |
| C2 | Thêm hỗ trợ trích xuất bảng | Trích xuất dữ liệu từ các bảng HTML | Trung bình | Hoàn thành |
| C3 | Thêm hỗ trợ trích xuất form | Phát hiện và trích xuất thông tin từ các form | Thấp | Hoàn thành |
| C4 | Thêm hỗ trợ trích xuất comment | Trích xuất các bình luận từ trang web | Thấp | Hoàn thành |

### D. Cải thiện trích xuất và tải xuống media

| ID | Nhiệm vụ | Mô tả | Độ ưu tiên | Trạng thái |
|----|----------|-------|------------|------------|
| D1 | Cải thiện trích xuất hình ảnh | Trích xuất tất cả hình ảnh, bao gồm cả background image trong CSS | Cao | Hoàn thành |
| D2 | Thêm hỗ trợ trích xuất video | Trích xuất video từ các thẻ `<video>` và các nhúng như YouTube, Vimeo | Cao | Hoàn thành |
| D3 | Thêm hỗ trợ trích xuất audio | Trích xuất audio từ các thẻ `<audio>` | Trung bình | Hoàn thành |
| D4 | Thêm hỗ trợ tải xuống media | Tải xuống hình ảnh, video, audio và các file đa phương tiện khác | Cao | Hoàn thành |

### E. Cải thiện trích xuất và tải xuống file

| ID | Nhiệm vụ | Mô tả | Độ ưu tiên | Trạng thái |
|----|----------|-------|------------|------------|
| E1 | Mở rộng danh sách file extensions | Bổ sung thêm các định dạng file cần hỗ trợ | Cao | Hoàn thành |
| E2 | Thêm hỗ trợ tải xuống file | Tải xuống các file đã phát hiện | Cao | Hoàn thành |
| E3 | Thêm hỗ trợ trích xuất nội dung file | Trích xuất nội dung từ các file PDF, DOCX, XLSX, v.v. | Trung bình | Hoàn thành |
| E4 | Thêm hỗ trợ phân loại file | Phân loại file theo loại (văn bản, hình ảnh, video, v.v.) | Trung bình | Hoàn thành |

### F. Cải thiện xử lý JavaScript và trang web động

| ID | Nhiệm vụ | Mô tả | Độ ưu tiên | Trạng thái |
|----|----------|-------|------------|------------|
| F1 | Cải thiện hỗ trợ JavaScript | Xử lý tốt hơn các trang web sử dụng JavaScript để tải nội dung | Cao | Hoàn thành |
| F2 | Thêm hỗ trợ Single Page Application (SPA) | Xử lý các trang web SPA như React, Angular, Vue | Cao | Hoàn thành |
| F3 | Thêm hỗ trợ Infinite Scroll | Phát hiện và xử lý các trang web sử dụng infinite scroll | Cao | Hoàn thành |
| F4 | Thêm hỗ trợ AJAX | Phát hiện và xử lý các request AJAX để lấy nội dung | Trung bình | Hoàn thành |

### G. Cải thiện hiệu suất và khả năng mở rộng

| ID | Nhiệm vụ | Mô tả | Độ ưu tiên | Trạng thái |
|----|----------|-------|------------|------------|
| G1 | Thêm hỗ trợ crawl đa luồng | Sử dụng nhiều luồng để crawl nhiều URL cùng lúc | Cao | Hoàn thành |
| G2 | Thêm hỗ trợ crawl phân tán | Hỗ trợ crawl phân tán trên nhiều máy | Thấp | Hoàn thành |
| G3 | Cải thiện quản lý bộ nhớ | Tối ưu hóa việc sử dụng bộ nhớ khi crawl nhiều trang | Cao | Hoàn thành |
| G4 | Thêm hỗ trợ checkpoint và resume | Lưu trạng thái crawl và có thể tiếp tục từ điểm dừng | Trung bình | Hoàn thành |

### H. Cải thiện xử lý lỗi và retry

| ID | Nhiệm vụ | Mô tả | Độ ưu tiên | Trạng thái |
|----|----------|-------|------------|------------|
| H1 | Cải thiện xử lý lỗi | Xử lý tốt hơn các lỗi như timeout, connection reset, v.v. | Cao | Hoàn thành |
| H2 | Cải thiện cơ chế retry | Cải thiện cơ chế retry với backoff strategy | Cao | Hoàn thành |
| H3 | Thêm hỗ trợ proxy rotation | Xoay vòng proxy để tránh bị chặn | Trung bình | Hoàn thành |
| H4 | Thêm hỗ trợ user agent rotation | Xoay vòng user agent để tránh bị phát hiện | Trung bình | Hoàn thành |

### I. Cải thiện lưu trữ và xuất kết quả

| ID | Nhiệm vụ | Mô tả | Độ ưu tiên | Trạng thái |
|----|----------|-------|------------|------------|
| I1 | Thêm hỗ trợ lưu trữ cấu trúc trang web | Lưu trữ cấu trúc trang web đã crawl | Cao | Hoàn thành |
| I2 | Thêm hỗ trợ xuất kết quả nhiều định dạng | Xuất kết quả dưới dạng JSON, CSV, XML, v.v. | Trung bình | Hoàn thành |
| I3 | Thêm hỗ trợ lưu trữ HTML | Lưu trữ HTML của các trang đã crawl | Cao | Hoàn thành |
| I4 | Thêm hỗ trợ lưu trữ ảnh chụp màn hình | Chụp ảnh màn hình của các trang đã crawl | Trung bình | Hoàn thành |

## Kế hoạch triển khai

### Giai đoạn 1: Nâng cấp cơ bản (Tuần 1-2)
- ✅ Thêm các tham số mới (A1-A5)
- Cải thiện trích xuất hình ảnh (D1)
- Thêm hỗ trợ tải xuống file (E2)
- Cải thiện xử lý lỗi (H1)

### Giai đoạn 2: Nâng cấp trích xuất nội dung (Tuần 3-4)
- Cải thiện trích xuất nội dung chính (C1)
- Thêm hỗ trợ trích xuất video (D2)
- Thêm hỗ trợ trích xuất audio (D3)
- Thêm hỗ trợ tải xuống media (D4)

### Giai đoạn 3: Nâng cấp xử lý trang web động (Tuần 5-6)
- Cải thiện hỗ trợ JavaScript (F1)
- Thêm hỗ trợ SPA (F2)
- Thêm hỗ trợ Infinite Scroll (F3)
- Thêm hỗ trợ pagination (B4)

### Giai đoạn 4: Nâng cấp hiệu suất và lưu trữ (Tuần 7-8)
- Thêm hỗ trợ crawl đa luồng (G1)
- Cải thiện quản lý bộ nhớ (G3)
- Thêm hỗ trợ lưu trữ cấu trúc trang web (I1)
- Thêm hỗ trợ lưu trữ HTML (I3)

## Các module mới cần tạo

### 1. MediaHandler
```python
class MediaHandler:
    """
    Xử lý việc trích xuất và tải xuống media.
    """
    def __init__(self, download_path, max_size_mb=10):
        # ...

    def extract_images(self, content, url, page=None):
        # ...

    def extract_videos(self, content, url, page=None):
        # ...

    def extract_audio(self, content, url, page=None):
        # ...

    def download_media(self, url, save_path):
        # ...
```

### 2. FileHandler
```python
class FileHandler:
    """
    Xử lý việc trích xuất và tải xuống file.
    """
    def __init__(self, download_path, max_size_mb=50):
        # ...

    def extract_files(self, content, url, page=None):
        # ...

    def download_file(self, url, save_path):
        # ...

    def extract_file_content(self, file_path):
        # ...
```

### 3. SiteStructureHandler
```python
class SiteStructureHandler:
    """
    Xử lý cấu trúc trang web.
    """
    def __init__(self, respect_robots=True, use_sitemap=True):
        # ...

    def parse_robots_txt(self, url):
        # ...

    def parse_sitemap(self, url):
        # ...

    def build_site_structure(self, start_url, max_depth=3):
        # ...

    def prioritize_urls(self, urls, start_url):
        # ...
```

## Tích hợp AdaptiveCrawler và WebSearchAgentLocal

### Phân tích tính năng trùng lặp

| ID | Tính năng | AdaptiveCrawler | WebSearchAgentLocal | Đánh giá |
|----|-----------|-----------------|---------------------|----------|
| T1 | Trích xuất nội dung từ URL | extract_content() | _extract_content_from_url(), extract_content() | WebSearchAgentLocal hoàn thiện hơn |
| T2 | Xử lý CAPTCHA | _is_captcha(), _solve_captcha() | CaptchaHandler, nhiều chiến lược xử lý | WebSearchAgentLocal hoàn thiện hơn |
| T3 | Xử lý User-Agent | UserAgentRotationManager | _get_rotated_user_agent() | Tương đương |
| T4 | Tải xuống và xử lý file | _download_files(), _extract_files() | WebSearchFileProcessor | WebSearchAgentLocal hoàn thiện hơn |
| T5 | Tích hợp Playwright | Xử lý SPA, infinite scroll | Xử lý CAPTCHA, tải file | AdaptiveCrawler hoàn thiện hơn |
| T6 | Xử lý ngôn ngữ tiếng Việt | _detect_language() | Nhiều phương thức xử lý tiếng Việt | WebSearchAgentLocal hoàn thiện hơn |
| T7 | Tối ưu hóa bộ nhớ | MemoryOptimizedCrawler | EnhancedWebSearchCache | AdaptiveCrawler hoàn thiện hơn |
| T8 | Độ sâu crawl | max_depth | max_depth trong _deep_crawl() | Trùng lặp hoàn toàn |
| T9 | Số lượng trang tối đa | max_pages | max_pages trong _deep_crawl() | Trùng lặp hoàn toàn |
| T10 | Timeout | timeout | timeout trong nhiều phương thức | Trùng lặp hoàn toàn |
| T11 | Xoay vòng User-Agent | user_agent_rotation | Cơ chế tương tự | Trùng lặp về chức năng |
| T12 | Deep Crawl | crawl() | _deep_crawl(), _perform_deep_crawl() | AdaptiveCrawler hoàn thiện hơn |
| T13 | Xử lý đa ngôn ngữ | EnhancedAdaptiveCrawler | Phương thức xử lý tiếng Việt | WebSearchAgentLocal tốt hơn cho tiếng Việt |
| T14 | Trích xuất cấu trúc trang web | SiteStructureHandler | Chức năng trong _deep_crawl() | AdaptiveCrawler hoàn thiện hơn |
| T15 | Xử lý phân trang | PaginationHandler | Hỗ trợ xử lý phân trang | AdaptiveCrawler hoàn thiện hơn |

### Kế hoạch tích hợp

| ID | Nhiệm vụ | Mô tả | Độ ưu tiên | Trạng thái |
|----|----------|-------|------------|------------|
| J1 | Giữ riêng hai module chính | Giữ WebSearchAgentLocal cho tìm kiếm và AdaptiveCrawler cho crawl | Cao | Chưa hoàn thành |
| J2 | Tích hợp AdaptiveCrawler vào WebSearchAgentLocal | Sử dụng AdaptiveCrawler khi cần crawl sâu | Cao | Chưa hoàn thành |
| J3 | Thống nhất module xử lý CAPTCHA | Tạo module CaptchaHandler dùng chung | Trung bình | Chưa hoàn thành |
| J4 | Thống nhất module quản lý User-Agent | Tạo module UserAgentManager dùng chung | Trung bình | Chưa hoàn thành |
| J5 | Thống nhất module xử lý file | Tạo module FileProcessor dùng chung | Trung bình | Chưa hoàn thành |
| J6 | Cải thiện tích hợp Playwright | Thống nhất cách sử dụng Playwright | Cao | Chưa hoàn thành |
| J7 | Tối ưu hóa tích hợp | Đảm bảo hiệu suất khi tích hợp | Cao | Chưa hoàn thành |
| J8 | Thống nhất tham số cấu hình | Tạo module ConfigManager dùng chung | Cao | Chưa hoàn thành |
| J9 | Thống nhất tính năng Deep Crawl | Sử dụng AdaptiveCrawler cho deep crawl | Cao | Chưa hoàn thành |
| J10 | Tích hợp xử lý đa ngôn ngữ | Kết hợp xử lý đa ngôn ngữ và tiếng Việt | Trung bình | Chưa hoàn thành |
| J11 | Thống nhất trích xuất cấu trúc trang web | Tạo module SiteStructureHandler dùng chung | Trung bình | Chưa hoàn thành |
| J12 | Thống nhất xử lý phân trang | Tạo module PaginationHandler dùng chung | Trung bình | Chưa hoàn thành |

## Các task chi tiết cho tích hợp

### J1: Giữ riêng hai module chính

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J1.1 | Rà soát cấu trúc hiện tại | Phân tích cấu trúc hiện tại của cả hai module | Cao | Hoàn thành |
| J1.2 | Xác định ranh giới rõ ràng | Xác định ranh giới rõ ràng giữa chức năng tìm kiếm và crawl | Cao | Hoàn thành |
| J1.3 | Tái cấu trúc WebSearchAgentLocal | Tái cấu trúc để tách biệt chức năng tìm kiếm | Cao | Chưa hoàn thành |
| J1.4 | Tái cấu trúc AdaptiveCrawler | Tái cấu trúc để tập trung vào chức năng crawl | Cao | Chưa hoàn thành |

### J2: Tích hợp AdaptiveCrawler vào WebSearchAgentLocal

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J2.1 | Cải thiện adaptive_crawler_integration.py | Cải thiện module tích hợp hiện tại | Cao | Hoàn thành |
| J2.2 | Thêm phương thức _deep_crawl_with_adaptive_crawler | Thêm phương thức để sử dụng AdaptiveCrawler khi cần crawl sâu | Cao | Hoàn thành |
| J2.3 | Thêm tham số cho deep_crawl | Thêm tham số để kiểm soát việc sử dụng AdaptiveCrawler | Trung bình | Hoàn thành |
| J2.4 | Thêm xử lý lỗi | Thêm xử lý lỗi khi tích hợp | Cao | Hoàn thành |
| J2.5 | Viết unit test | Viết unit test cho tích hợp | Cao | Hoàn thành |

### J3: Thống nhất module xử lý CAPTCHA

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J3.1 | Phân tích các module CAPTCHA hiện tại | Phân tích CaptchaHandler và _solve_captcha | Cao | Hoàn thành |
| J3.2 | Tạo module CaptchaHandler thống nhất | Tạo module dùng chung cho cả hai module | Cao | Hoàn thành |
| J3.3 | Cập nhật WebSearchAgentLocal | Cập nhật để sử dụng module mới | Trung bình | Hoàn thành |
| J3.4 | Cập nhật AdaptiveCrawler | Cập nhật để sử dụng module mới | Trung bình | Hoàn thành |
| J3.5 | Viết unit test | Viết unit test cho module mới | Trung bình | Hoàn thành |

### J4: Thống nhất module quản lý User-Agent

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J4.1 | Phân tích các module User-Agent hiện tại | Phân tích UserAgentRotationManager và _get_rotated_user_agent | Cao | Hoàn thành |
| J4.2 | Tạo module UserAgentManager thống nhất | Tạo module dùng chung cho cả hai module | Cao | Đang thực hiện |
| J4.3 | Cập nhật WebSearchAgentLocal | Cập nhật để sử dụng module mới | Trung bình | Chưa hoàn thành |
| J4.4 | Cập nhật AdaptiveCrawler | Cập nhật để sử dụng module mới | Trung bình | Chưa hoàn thành |
| J4.5 | Viết unit test | Viết unit test cho module mới | Trung bình | Chưa hoàn thành |

### J5: Thống nhất module xử lý file

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J5.1 | Phân tích các module xử lý file hiện tại | Phân tích WebSearchFileProcessor và _download_files, _extract_files | Cao | Hoàn thành |
| J5.2 | Tạo module FileProcessor thống nhất | Tạo module dùng chung cho cả hai module | Cao | Đang thực hiện |
| J5.3 | Cập nhật WebSearchAgentLocal | Cập nhật để sử dụng module mới | Trung bình | Chưa hoàn thành |
| J5.4 | Cập nhật AdaptiveCrawler | Cập nhật để sử dụng module mới | Trung bình | Chưa hoàn thành |
| J5.5 | Viết unit test | Viết unit test cho module mới | Trung bình | Chưa hoàn thành |

### J6: Cải thiện tích hợp Playwright

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J6.1 | Phân tích cách sử dụng Playwright hiện tại | Phân tích cách sử dụng Playwright trong cả hai module | Cao | Hoàn thành |
| J6.2 | Tạo module PlaywrightHandler thống nhất | Tạo module dùng chung cho cả hai module | Cao | Đang thực hiện |
| J6.3 | Thêm hỗ trợ SPA và infinite scroll | Thêm hỗ trợ từ AdaptiveCrawler vào module mới | Cao | Chưa hoàn thành |
| J6.4 | Thêm hỗ trợ xử lý CAPTCHA | Thêm hỗ trợ từ WebSearchAgentLocal vào module mới | Cao | Chưa hoàn thành |
| J6.5 | Viết unit test | Viết unit test cho module mới | Trung bình | Chưa hoàn thành |

### J7: Tối ưu hóa tích hợp

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J7.1 | Đo hiệu suất trước khi tích hợp | Đo hiệu suất của cả hai module trước khi tích hợp | Cao | Hoàn thành |
| J7.2 | Tối ưu hóa bộ nhớ | Tích hợp MemoryOptimizedCrawler và EnhancedWebSearchCache | Cao | Chưa hoàn thành |
| J7.3 | Tối ưu hóa tốc độ | Tối ưu hóa tốc độ khi tích hợp | Cao | Chưa hoàn thành |
| J7.4 | Đo hiệu suất sau khi tích hợp | Đo hiệu suất sau khi tích hợp | Cao | Chưa hoàn thành |
| J7.5 | Viết tài liệu | Viết tài liệu về cách sử dụng module tích hợp | Trung bình | Chưa hoàn thành |

### J8: Thống nhất tham số cấu hình

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J8.1 | Phân tích tham số cấu hình hiện tại | Phân tích tham số cấu hình trong cả hai module | Cao | Hoàn thành |
| J8.2 | Tạo module ConfigManager | Tạo module quản lý cấu hình dùng chung | Cao | Đang thực hiện |
| J8.3 | Thống nhất tham số độ sâu crawl | Thống nhất tham số max_depth | Cao | Chưa hoàn thành |
| J8.4 | Thống nhất tham số số lượng trang tối đa | Thống nhất tham số max_pages | Cao | Chưa hoàn thành |
| J8.5 | Thống nhất tham số timeout | Thống nhất tham số timeout | Cao | Chưa hoàn thành |
| J8.6 | Cập nhật WebSearchAgentLocal | Cập nhật để sử dụng ConfigManager | Trung bình | Chưa hoàn thành |
| J8.7 | Cập nhật AdaptiveCrawler | Cập nhật để sử dụng ConfigManager | Trung bình | Chưa hoàn thành |
| J8.8 | Viết unit test | Viết unit test cho ConfigManager | Trung bình | Chưa hoàn thành |

### J9: Thống nhất tính năng Deep Crawl

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J9.1 | Phân tích tính năng Deep Crawl hiện tại | Phân tích tính năng Deep Crawl trong cả hai module | Cao | Hoàn thành |
| J9.2 | Cải thiện _deep_crawl_with_adaptive_crawler | Cải thiện phương thức tích hợp | Cao | Chưa hoàn thành |
| J9.3 | Thay thế _deep_crawl trong WebSearchAgentLocal | Sử dụng AdaptiveCrawler thay vì phương thức cũ | Cao | Chưa hoàn thành |
| J9.4 | Thêm tham số điều khiển chiến lược crawl | Thêm tham số để điều khiển chiến lược crawl | Trung bình | Chưa hoàn thành |
| J9.5 | Viết unit test | Viết unit test cho tính năng Deep Crawl | Trung bình | Chưa hoàn thành |

### J10: Tích hợp xử lý đa ngôn ngữ

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J10.1 | Phân tích tính năng xử lý đa ngôn ngữ hiện tại | Phân tích tính năng xử lý đa ngôn ngữ trong cả hai module | Cao | Hoàn thành |
| J10.2 | Tạo module LanguageHandler | Tạo module xử lý ngôn ngữ dùng chung | Cao | Đang thực hiện |
| J10.3 | Tích hợp xử lý tiếng Việt | Tích hợp các phương thức xử lý tiếng Việt từ WebSearchAgentLocal | Cao | Chưa hoàn thành |
| J10.4 | Tích hợp xử lý đa ngôn ngữ | Tích hợp các phương thức xử lý đa ngôn ngữ từ EnhancedAdaptiveCrawler | Trung bình | Chưa hoàn thành |
| J10.5 | Viết unit test | Viết unit test cho LanguageHandler | Trung bình | Chưa hoàn thành |

### J11: Thống nhất trích xuất cấu trúc trang web

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J11.1 | Phân tích tính năng trích xuất cấu trúc trang web hiện tại | Phân tích tính năng trích xuất cấu trúc trang web trong cả hai module | Cao | Hoàn thành |
| J11.2 | Tạo module SiteStructureHandler | Tạo module trích xuất cấu trúc trang web dùng chung | Cao | Đang thực hiện |
| J11.3 | Cập nhật WebSearchAgentLocal | Cập nhật để sử dụng SiteStructureHandler | Trung bình | Chưa hoàn thành |
| J11.4 | Cập nhật AdaptiveCrawler | Cập nhật để sử dụng SiteStructureHandler | Trung bình | Chưa hoàn thành |
| J11.5 | Viết unit test | Viết unit test cho SiteStructureHandler | Trung bình | Chưa hoàn thành |

### J12: Thống nhất xử lý phân trang

| ID | Task | Mô tả | Độ ưu tiên | Trạng thái |
|----|------|-------|------------|------------|
| J12.1 | Phân tích tính năng xử lý phân trang hiện tại | Phân tích tính năng xử lý phân trang trong cả hai module | Cao | Hoàn thành |
| J12.2 | Tạo module PaginationHandler | Tạo module xử lý phân trang dùng chung | Cao | Đang thực hiện |
| J12.3 | Cập nhật WebSearchAgentLocal | Cập nhật để sử dụng PaginationHandler | Trung bình | Chưa hoàn thành |
| J12.4 | Cập nhật AdaptiveCrawler | Cập nhật để sử dụng PaginationHandler | Trung bình | Chưa hoàn thành |
| J12.5 | Viết unit test | Viết unit test cho PaginationHandler | Trung bình | Chưa hoàn thành |

## Kế hoạch triển khai tích hợp

### Giai đoạn 1: Phân tích và chuẩn bị (Tuần 1)
- Hoàn thành J1.1: Rà soát cấu trúc hiện tại
- Hoàn thành J1.2: Xác định ranh giới rõ ràng
- Hoàn thành J3.1: Phân tích các module CAPTCHA hiện tại
- Hoàn thành J4.1: Phân tích các module User-Agent hiện tại
- Hoàn thành J5.1: Phân tích các module xử lý file hiện tại
- Hoàn thành J6.1: Phân tích cách sử dụng Playwright hiện tại
- Hoàn thành J7.1: Đo hiệu suất trước khi tích hợp
- Hoàn thành J8.1: Phân tích tham số cấu hình hiện tại
- Hoàn thành J9.1: Phân tích tính năng Deep Crawl hiện tại
- Hoàn thành J10.1: Phân tích tính năng xử lý đa ngôn ngữ hiện tại
- Hoàn thành J11.1: Phân tích tính năng trích xuất cấu trúc trang web hiện tại
- Hoàn thành J12.1: Phân tích tính năng xử lý phân trang hiện tại

### Giai đoạn 2: Tái cấu trúc và tạo module dùng chung (Tuần 2-3)
- Hoàn thành J1.3: Tái cấu trúc WebSearchAgentLocal
- Hoàn thành J1.4: Tái cấu trúc AdaptiveCrawler
- Hoàn thành J3.2: Tạo module CaptchaHandler thống nhất
- Hoàn thành J4.2: Tạo module UserAgentManager thống nhất
- Hoàn thành J5.2: Tạo module FileProcessor thống nhất
- Hoàn thành J6.2: Tạo module PlaywrightHandler thống nhất
- Hoàn thành J8.2: Tạo module ConfigManager thống nhất
- Hoàn thành J8.3: Thống nhất tham số độ sâu crawl
- Hoàn thành J8.4: Thống nhất tham số số lượng trang tối đa
- Hoàn thành J8.5: Thống nhất tham số timeout
- Hoàn thành J10.2: Tạo module LanguageHandler
- Hoàn thành J11.2: Tạo module SiteStructureHandler
- Hoàn thành J12.2: Tạo module PaginationHandler

### Giai đoạn 3: Cập nhật và tích hợp (Tuần 4-5)
- ✅ Hoàn thành J2.1: Cải thiện adaptive_crawler_integration.py
- ✅ Hoàn thành J2.2: Thêm phương thức _deep_crawl_with_adaptive_crawler
- ✅ Hoàn thành J2.3: Thêm tham số cho deep_crawl
- ✅ Hoàn thành J3.3: Cập nhật WebSearchAgentLocal để sử dụng CaptchaHandler mới
- ✅ Hoàn thành J3.4: Cập nhật AdaptiveCrawler để sử dụng CaptchaHandler mới
- Hoàn thành J4.3: Cập nhật WebSearchAgentLocal để sử dụng UserAgentManager mới
- Hoàn thành J4.4: Cập nhật AdaptiveCrawler để sử dụng UserAgentManager mới
- Hoàn thành J5.3: Cập nhật WebSearchAgentLocal để sử dụng FileProcessor mới
- Hoàn thành J5.4: Cập nhật AdaptiveCrawler để sử dụng FileProcessor mới
- Hoàn thành J6.3: Thêm hỗ trợ SPA và infinite scroll
- Hoàn thành J6.4: Thêm hỗ trợ xử lý CAPTCHA
- Hoàn thành J8.6: Cập nhật WebSearchAgentLocal để sử dụng ConfigManager
- Hoàn thành J8.7: Cập nhật AdaptiveCrawler để sử dụng ConfigManager
- Hoàn thành J9.2: Cải thiện _deep_crawl_with_adaptive_crawler
- Hoàn thành J9.3: Thay thế _deep_crawl trong WebSearchAgentLocal
- Hoàn thành J9.4: Thêm tham số điều khiển chiến lược crawl
- Hoàn thành J10.3: Tích hợp xử lý tiếng Việt
- Hoàn thành J10.4: Tích hợp xử lý đa ngôn ngữ
- Hoàn thành J11.3: Cập nhật WebSearchAgentLocal để sử dụng SiteStructureHandler
- Hoàn thành J11.4: Cập nhật AdaptiveCrawler để sử dụng SiteStructureHandler
- Hoàn thành J12.3: Cập nhật WebSearchAgentLocal để sử dụng PaginationHandler
- Hoàn thành J12.4: Cập nhật AdaptiveCrawler để sử dụng PaginationHandler

### Giai đoạn 4: Kiểm thử và tối ưu hóa (Tuần 6-7)
- ✅ Hoàn thành J2.4: Thêm xử lý lỗi
- ✅ Hoàn thành J2.5: Viết unit test cho tích hợp
- ✅ Hoàn thành J3.5: Viết unit test cho CaptchaHandler
- Hoàn thành J4.5: Viết unit test cho UserAgentManager
- Hoàn thành J5.5: Viết unit test cho FileProcessor
- Hoàn thành J6.5: Viết unit test cho PlaywrightHandler
- Hoàn thành J7.2: Tối ưu hóa bộ nhớ
- Hoàn thành J7.3: Tối ưu hóa tốc độ
- Hoàn thành J7.4: Đo hiệu suất sau khi tích hợp
- Hoàn thành J8.8: Viết unit test cho ConfigManager
- Hoàn thành J9.5: Viết unit test cho tính năng Deep Crawl
- Hoàn thành J10.5: Viết unit test cho LanguageHandler
- Hoàn thành J11.5: Viết unit test cho SiteStructureHandler
- Hoàn thành J12.5: Viết unit test cho PaginationHandler

### Giai đoạn 5: Tài liệu và hoàn thiện (Tuần 8)
- Hoàn thành J7.5: Viết tài liệu
- Rà soát lại toàn bộ code
- Tạo pull request và merge vào nhánh chính

## Mô hình tích hợp đề xuất

```
WebSearchAgentLocal
├── Chức năng tìm kiếm chính
├── Cache kết quả tìm kiếm
└── Tích hợp với AdaptiveCrawler
    └── _deep_crawl_with_adaptive_crawler()

AdaptiveCrawler
├── Chiến lược crawl thích ứng
├── Tối ưu hóa bộ nhớ
└── Trích xuất dữ liệu có cấu trúc

Các module dùng chung
├── CaptchaHandler (xử lý CAPTCHA)
├── UserAgentManager (quản lý User-Agent)
├── FileProcessor (xử lý file)
├── PlaywrightHandler (xử lý JavaScript, SPA)
├── ConfigManager (quản lý cấu hình)
├── LanguageHandler (xử lý đa ngôn ngữ và tiếng Việt)
├── SiteStructureHandler (trích xuất cấu trúc trang web)
└── PaginationHandler (xử lý phân trang)
```

## Thiết kế chi tiết các module dùng chung

### 1. CaptchaHandler

```python
class CaptchaHandler:
    """
    Module xử lý CAPTCHA dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    def __init__(
        self,
        auto_solve: bool = False,
        use_selenium: bool = False,
        max_retries: int = 5,
        retry_delay: float = 3.0,
        verbose: bool = True,
        **kwargs
    ):
        # Khởi tạo các thuộc tính
        self.auto_solve = auto_solve
        self.use_selenium = use_selenium
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.verbose = verbose

        # Khởi tạo các bộ phát hiện CAPTCHA
        self._initialize_detectors()

        # Khởi tạo các chiến lược xử lý CAPTCHA
        self._initialize_strategies()

    def detect_captcha(self, html_content: str) -> Tuple[bool, CaptchaType, Dict[str, Any]]:
        """
        Phát hiện CAPTCHA trong nội dung HTML.
        """
        # Kết hợp phương thức phát hiện từ cả hai module
        pass

    def solve_captcha(self, html_content: str, url: str, captcha_type: CaptchaType) -> str:
        """
        Giải quyết CAPTCHA.
        """
        # Kết hợp phương thức giải quyết từ cả hai module
        pass

    def get_user_agent(self, rotate: bool = True) -> str:
        """
        Lấy User-Agent để xử lý CAPTCHA.
        """
        # Sử dụng UserAgentManager
        pass
```

### 2. UserAgentManager

```python
class UserAgentManager:
    """
    Module quản lý User-Agent dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    def __init__(
        self,
        user_agents: Optional[List[str]] = None,
        rotation_strategy: str = "round_robin",
        device_type: str = "all",
        browser_type: str = "all",
        os_type: str = "all",
        include_random: bool = False,
        random_ratio: float = 0.1,
        verbose: bool = False,
        **kwargs
    ):
        # Khởi tạo các thuộc tính
        self.user_agents = user_agents or self._get_default_user_agents()
        self.rotation_strategy = rotation_strategy
        self.device_type = device_type
        self.browser_type = browser_type
        self.os_type = os_type
        self.include_random = include_random
        self.random_ratio = random_ratio
        self.verbose = verbose

        # Khởi tạo các thành phần
        self._initialize_components()

    def get_user_agent(self, strategy: Optional[str] = None) -> str:
        """
        Lấy User-Agent theo chiến lược xoay vòng.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def add_user_agent(self, user_agent: str) -> bool:
        """
        Thêm User-Agent vào danh sách.
        """
        pass

    def _generate_random_user_agent(self) -> str:
        """
        Tạo User-Agent ngẫu nhiên.
        """
        pass
```

### 3. FileProcessor

```python
class FileProcessor:
    """
    Module xử lý file dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    def __init__(
        self,
        download_dir: str = "downloads",
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        supported_extensions: Optional[List[str]] = None,
        timeout: float = 30.0,
        user_agent: Optional[str] = None,
        use_playwright: bool = False,
        verbose: bool = False,
        **kwargs
    ):
        # Khởi tạo các thuộc tính
        self.download_dir = download_dir
        self.max_file_size = max_file_size
        self.supported_extensions = supported_extensions or self._get_default_extensions()
        self.timeout = timeout
        self.user_agent = user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
        self.use_playwright = use_playwright
        self.verbose = verbose

        # Khởi tạo các thành phần
        self._initialize_components()

    def download_file(self, url: str, output_path: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Tải xuống file từ URL.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def process_file(self, url: str, output_path: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Tải xuống và phân tích file từ URL.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def extract_file_content(self, file_path: str, mime_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Trích xuất nội dung từ file.
        """
        # Kết hợp phương thức từ cả hai module
        pass
```

### 4. PlaywrightHandler

```python
class PlaywrightHandler:
    """
    Module xử lý Playwright dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    def __init__(
        self,
        headless: bool = True,
        browser_type: str = "chromium",
        timeout: int = 30,
        user_agent: Optional[str] = None,
        viewport_size: Dict[str, int] = {"width": 1280, "height": 800},
        handle_javascript: bool = True,
        handle_spa: bool = True,
        handle_infinite_scroll: bool = True,
        handle_captcha: bool = True,
        verbose: bool = False,
        **kwargs
    ):
        # Khởi tạo các thuộc tính
        self.headless = headless
        self.browser_type = browser_type
        self.timeout = timeout
        self.user_agent = user_agent
        self.viewport_size = viewport_size
        self.handle_javascript = handle_javascript
        self.handle_spa = handle_spa
        self.handle_infinite_scroll = handle_infinite_scroll
        self.handle_captcha = handle_captcha
        self.verbose = verbose

        # Khởi tạo các thành phần
        self._initialize_components()

    def extract_content(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Trích xuất nội dung từ URL sử dụng Playwright.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def handle_spa(self, page, url: str) -> bool:
        """
        Xử lý Single Page Application.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def handle_infinite_scroll(self, page, url: str) -> bool:
        """
        Xử lý Infinite Scroll.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def handle_captcha(self, page, url: str) -> bool:
        """
        Xử lý CAPTCHA.
        """
        # Sử dụng CaptchaHandler
        pass
```

### 5. ConfigManager

```python
class ConfigManager:
    """
    Module quản lý cấu hình dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    def __init__(
        self,
        config_file: Optional[str] = None,
        default_config: Optional[Dict[str, Any]] = None,
        verbose: bool = False,
        **kwargs
    ):
        # Khởi tạo các thuộc tính
        self.config_file = config_file
        self.default_config = default_config or self._get_default_config()
        self.verbose = verbose
        self.config = {}

        # Khởi tạo các thành phần
        self._initialize_components()

        # Tải cấu hình
        self._load_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """
        Lấy cấu hình mặc định.
        """
        return {
            # Cấu hình chung
            "verbose": False,
            "timeout": 30,

            # Cấu hình crawl
            "max_depth": 2,
            "max_pages": 10,
            "respect_robots": True,
            "follow_redirects": True,

            # Cấu hình User-Agent
            "user_agent_rotation": True,
            "user_agent_rotation_strategy": "round_robin",

            # Cấu hình Playwright
            "use_playwright": True,
            "headless": True,
            "browser_type": "chromium",
            "viewport_size": {"width": 1280, "height": 800},

            # Cấu hình CAPTCHA
            "handle_captcha": True,
            "captcha_auto_solve": False,

            # Cấu hình file
            "download_files": False,
            "download_dir": "downloads",
            "max_file_size": 10 * 1024 * 1024,  # 10MB

            # Cấu hình bộ nhớ
            "use_memory_optimization": True,
            "memory_threshold": 80.0,  # 80%
            "memory_cleanup_interval": 5,  # 5 giây

            # Cấu hình cache
            "use_cache": True,
            "cache_ttl": 3600,  # 1 giờ
        }

    def _load_config(self) -> None:
        """
        Tải cấu hình từ file.
        """
        # Tải cấu hình từ file nếu có
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r") as f:
                    file_config = json.load(f)
                    self.config.update(file_config)
            except Exception as e:
                if self.verbose:
                    print(f"Lỗi khi tải cấu hình từ file: {str(e)}")

        # Sử dụng cấu hình mặc định cho các tham số còn thiếu
        for key, value in self.default_config.items():
            if key not in self.config:
                self.config[key] = value

    def save_config(self) -> bool:
        """
        Lưu cấu hình vào file.
        """
        if not self.config_file:
            return False

        try:
            with open(self.config_file, "w") as f:
                json.dump(self.config, f, indent=4)
            return True
        except Exception as e:
            if self.verbose:
                print(f"Lỗi khi lưu cấu hình vào file: {str(e)}")
            return False

    def get(self, key: str, default: Any = None) -> Any:
        """
        Lấy giá trị cấu hình.
        """
        return self.config.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """
        Đặt giá trị cấu hình.
        """
        self.config[key] = value

    def update(self, config: Dict[str, Any]) -> None:
        """
        Cập nhật cấu hình.
        """
        self.config.update(config)

    def get_crawler_config(self) -> Dict[str, Any]:
        """
        Lấy cấu hình cho AdaptiveCrawler.
        """
        crawler_config = {}

        # Lọc các tham số cấu hình cho AdaptiveCrawler
        for key, value in self.config.items():
            if key in [
                "max_depth", "max_pages", "timeout", "respect_robots",
                "follow_redirects", "user_agent_rotation", "use_playwright",
                "headless", "browser_type", "viewport_size", "handle_captcha",
                "download_files", "download_dir", "max_file_size",
                "use_memory_optimization", "memory_threshold",
                "memory_cleanup_interval", "verbose"
            ]:
                crawler_config[key] = value

        return crawler_config

    def get_search_agent_config(self) -> Dict[str, Any]:
        """
        Lấy cấu hình cho WebSearchAgentLocal.
        """
        search_agent_config = {}

        # Lọc các tham số cấu hình cho WebSearchAgentLocal
        for key, value in self.config.items():
            if key in [
                "timeout", "respect_robots", "user_agent_rotation",
                "use_playwright", "headless", "browser_type", "viewport_size",
                "handle_captcha", "download_files", "download_dir",
                "max_file_size", "use_cache", "cache_ttl", "verbose"
            ]:
                search_agent_config[key] = value

        return search_agent_config
```

### 6. LanguageHandler

```python
class LanguageHandler:
    """
    Module xử lý ngôn ngữ dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    def __init__(
        self,
        default_language: str = "en",
        supported_languages: Optional[List[str]] = None,
        vietnamese_support: bool = True,
        use_language_detection: bool = True,
        verbose: bool = False,
        **kwargs
    ):
        # Khởi tạo các thuộc tính
        self.default_language = default_language
        self.supported_languages = supported_languages or ["en", "vi", "fr", "de", "es", "zh", "ja", "ko"]
        self.vietnamese_support = vietnamese_support
        self.use_language_detection = use_language_detection
        self.verbose = verbose

        # Khởi tạo các thành phần
        self._initialize_components()

    def detect_language(self, text: str) -> str:
        """
        Phát hiện ngôn ngữ của văn bản.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def is_vietnamese_text(self, text: str) -> bool:
        """
        Kiểm tra xem văn bản có phải là tiếng Việt không.
        """
        # Kết hợp phương thức từ WebSearchAgentLocal
        pass

    def fix_vietnamese_encoding(self, text: str) -> str:
        """
        Sửa lỗi mã hóa tiếng Việt.
        """
        # Kết hợp phương thức từ WebSearchAgentLocal
        pass

    def improve_vietnamese_paragraphs(self, text: str) -> str:
        """
        Cải thiện phát hiện đoạn văn tiếng Việt.
        """
        # Kết hợp phương thức từ WebSearchAgentLocal
        pass

    def remove_vietnamese_boilerplate(self, text: str) -> str:
        """
        Loại bỏ nội dung thừa tiếng Việt.
        """
        # Kết hợp phương thức từ WebSearchAgentLocal
        pass

    def translate_text(self, text: str, target_language: str) -> str:
        """
        Dịch văn bản sang ngôn ngữ đích.
        """
        # Kết hợp phương thức từ EnhancedAdaptiveCrawler
        pass
```

### 7. SiteStructureHandler

```python
class SiteStructureHandler:
    """
    Module trích xuất cấu trúc trang web dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    def __init__(
        self,
        max_depth: int = 2,
        max_pages: int = 10,
        extract_metadata: bool = True,
        extract_links: bool = True,
        extract_images: bool = False,
        extract_files: bool = False,
        respect_robots: bool = True,
        verbose: bool = False,
        **kwargs
    ):
        # Khởi tạo các thuộc tính
        self.max_depth = max_depth
        self.max_pages = max_pages
        self.extract_metadata = extract_metadata
        self.extract_links = extract_links
        self.extract_images = extract_images
        self.extract_files = extract_files
        self.respect_robots = respect_robots
        self.verbose = verbose

        # Khởi tạo các thành phần
        self._initialize_components()

    def extract_site_structure(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Trích xuất cấu trúc trang web.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def extract_links(self, html_content: str, base_url: str) -> List[Dict[str, Any]]:
        """
        Trích xuất liên kết từ nội dung HTML.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def extract_metadata(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Trích xuất metadata từ nội dung HTML.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def extract_images(self, html_content: str, base_url: str) -> List[Dict[str, Any]]:
        """
        Trích xuất hình ảnh từ nội dung HTML.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def extract_files(self, html_content: str, base_url: str) -> List[Dict[str, Any]]:
        """
        Trích xuất file từ nội dung HTML.
        """
        # Kết hợp phương thức từ cả hai module
        pass
```

### 8. PaginationHandler

```python
class PaginationHandler:
    """
    Module xử lý phân trang dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    def __init__(
        self,
        max_pages: int = 5,
        pagination_patterns: Optional[List[Dict[str, Any]]] = None,
        use_playwright: bool = True,
        handle_infinite_scroll: bool = True,
        handle_load_more: bool = True,
        verbose: bool = False,
        **kwargs
    ):
        # Khởi tạo các thuộc tính
        self.max_pages = max_pages
        self.pagination_patterns = pagination_patterns or self._get_default_patterns()
        self.use_playwright = use_playwright
        self.handle_infinite_scroll = handle_infinite_scroll
        self.handle_load_more = handle_load_more
        self.verbose = verbose

        # Khởi tạo các thành phần
        self._initialize_components()

    def _get_default_patterns(self) -> List[Dict[str, Any]]:
        """
        Lấy các mẫu phân trang mặc định.
        """
        return [
            # Mẫu phân trang thông thường
            {
                "type": "link",
                "selector": "a.next, a.pagination__next, a[rel='next'], a:contains('Next')",
                "priority": 1
            },
            # Mẫu phân trang số trang
            {
                "type": "page_number",
                "selector": "ul.pagination li a, div.pagination a",
                "priority": 2
            },
            # Mẫu nút "Load more"
            {
                "type": "load_more",
                "selector": "button:contains('Load more'), a:contains('Load more'), button.load-more, a.load-more",
                "priority": 3
            },
            # Mẫu infinite scroll
            {
                "type": "infinite_scroll",
                "scroll_percentage": 0.8,
                "max_scroll_attempts": 5,
                "priority": 4
            }
        ]

    def detect_pagination_type(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Phát hiện loại phân trang.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def handle_pagination(self, url: str, html_content: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Xử lý phân trang.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def handle_infinite_scroll_pagination(self, page, url: str) -> List[Dict[str, Any]]:
        """
        Xử lý phân trang kiểu infinite scroll.
        """
        # Kết hợp phương thức từ cả hai module
        pass

    def handle_load_more_pagination(self, page, url: str) -> List[Dict[str, Any]]:
        """
        Xử lý phân trang kiểu "Load more".
        """
        # Kết hợp phương thức từ cả hai module
        pass
```

## Tiến độ thực hiện

### Tổng quan tiến độ

| Nhiệm vụ | Trạng thái |
|----------|------------|
| J1.1: Rà soát cấu trúc hiện tại | Hoàn thành |
| J1.2: Xác định ranh giới rõ ràng | Hoàn thành |
| J2.1: Cải thiện adaptive_crawler_integration.py | Hoàn thành |
| J2.2: Thêm phương thức _deep_crawl_with_adaptive_crawler | Hoàn thành |
| J2.3: Thêm tham số cho deep_crawl | Hoàn thành |
| J2.4: Thêm xử lý lỗi | Hoàn thành |
| J2.5: Viết unit test cho tích hợp | Hoàn thành |
| J3.3: Cập nhật WebSearchAgentLocal để sử dụng CaptchaHandler mới | Hoàn thành |
| J3.4: Cập nhật AdaptiveCrawler để sử dụng CaptchaHandler mới | Hoàn thành |
| J3.5: Viết unit test cho CaptchaHandler | Hoàn thành |
| J3.1: Phân tích các module CAPTCHA hiện tại | Hoàn thành |
| J3.2: Tạo module CaptchaHandler dùng chung | Hoàn thành |
| J4.1: Phân tích các module User-Agent hiện tại | Hoàn thành |
| J4.2: Tạo module UserAgentManager dùng chung | Hoàn thành |
| J5.1: Phân tích các module xử lý file hiện tại | Hoàn thành |
| J5.2: Tạo module FileProcessor dùng chung | Hoàn thành |
| J6.1: Phân tích cách sử dụng Playwright hiện tại | Hoàn thành |
| J6.2: Tạo module PlaywrightHandler dùng chung | Hoàn thành |
| J7.1: Đo hiệu suất trước khi tích hợp | Hoàn thành |
| J8.1: Phân tích tham số cấu hình hiện tại | Hoàn thành |
| J8.2: Tạo module ConfigManager dùng chung | Hoàn thành |
| J9.1: Phân tích tính năng Deep Crawl hiện tại | Hoàn thành |
| J10.1: Phân tích tính năng xử lý đa ngôn ngữ hiện tại | Hoàn thành |
| J10.2: Tạo module LanguageHandler dùng chung | Hoàn thành |
| J11.1: Phân tích tính năng trích xuất cấu trúc trang web hiện tại | Hoàn thành |
| J11.2: Tạo module SiteStructureHandler dùng chung | Hoàn thành |
| J12.1: Phân tích tính năng xử lý phân trang hiện tại | Hoàn thành |
| J12.2: Tạo module PaginationHandler dùng chung | Hoàn thành |

### J1.1: Rà soát cấu trúc hiện tại (Hoàn thành)

#### Cấu trúc của AdaptiveCrawler

AdaptiveCrawler là một module chuyên dụng cho việc crawl trang web với các tính năng nâng cao:

1. **Cấu trúc chính**:
   - Lớp `AdaptiveCrawler` trong `src/deep_research_core/agents/adaptive_crawler.py`
   - Sử dụng `MemoryOptimizedCrawler` để tối ưu hóa bộ nhớ
   - Có khả năng tự động điều chỉnh chiến lược crawl dựa trên độ phức tạp của câu hỏi

2. **Các thành phần chính**:
   - `crawl()`: Phương thức chính để crawl URL
   - `_crawl_with_memory_optimization()`: Sử dụng MemoryOptimizedCrawler để tối ưu hóa bộ nhớ
   - `_crawl_standard()`: Phương thức crawl tiêu chuẩn
   - `_crawl_with_playwright()`: Sử dụng Playwright để xử lý JavaScript
   - `_extract_structured_data()`: Trích xuất dữ liệu có cấu trúc
   - `_handle_spa()`: Xử lý Single Page Application
   - `_handle_infinite_scroll()`: Xử lý infinite scroll
   - `_handle_ajax()`: Xử lý AJAX
   - `_is_captcha()`: Phát hiện CAPTCHA
   - `_solve_captcha()`: Giải quyết CAPTCHA

3. **Các module phụ thuộc**:
   - `MemoryOptimizedCrawler`: Tối ưu hóa bộ nhớ khi crawl
   - `SiteStructureHandler`: Xử lý cấu trúc trang web
   - `PaginationHandler`: Xử lý phân trang
   - `CaptchaHandler`: Xử lý CAPTCHA

#### Cấu trúc của WebSearchAgentLocal

WebSearchAgentLocal là một module tìm kiếm web tổng hợp với nhiều tính năng:

1. **Cấu trúc chính**:
   - Lớp `WebSearchAgentLocal` trong `src/deep_research_core/agents/web_search_agent_local.py`
   - Kế thừa từ `BaseSearchAgent`
   - Tích hợp nhiều công cụ tìm kiếm và trích xuất nội dung

2. **Các thành phần chính**:
   - `search()`: Phương thức chính để tìm kiếm
   - `extract_content()`: Trích xuất nội dung từ URL
   - `_extract_content_from_url()`: Phương thức cũ để trích xuất nội dung
   - `_deep_crawl()`: Crawl sâu vào trang web
   - `_perform_deep_crawl()`: Thực hiện deep crawl
   - `_fix_vietnamese_encoding()`: Sửa lỗi mã hóa tiếng Việt
   - `_is_vietnamese_text()`: Kiểm tra xem văn bản có phải là tiếng Việt không
   - `handle_captcha()`: Xử lý CAPTCHA

3. **Các module phụ thuộc**:
   - `CaptchaHandler`: Xử lý CAPTCHA
   - `async_extractor`: Trích xuất nội dung bất đồng bộ
   - `search_worker_pool`: Xử lý tìm kiếm đa luồng
   - `robots_parser`: Phân tích robots.txt
   - `language_detector`: Phát hiện ngôn ngữ

4. **Tích hợp với AdaptiveCrawler**:
   - Sử dụng `adaptive_crawler_integration.py` để tích hợp AdaptiveCrawler
   - Thêm phương thức `_deep_crawl_with_adaptive_crawler()` để sử dụng AdaptiveCrawler khi cần crawl sâu

#### Tích hợp hiện tại giữa hai module

1. **Tích hợp trong WebSearchAgentLocal**:
   ```python
   # Tích hợp AdaptiveCrawler
   try:
       from .adaptive_crawler_integration import integrate_adaptive_crawler
       integrate_adaptive_crawler(self, {
           "use_memory_optimization": True,
           "use_playwright": True,
           "verbose": verbose
       })
       logger.info("AdaptiveCrawler integrated successfully")
   except Exception as e:
       logger.warning(f"Failed to integrate AdaptiveCrawler: {str(e)}")
   ```

2. **Sử dụng AdaptiveCrawler trong _deep_crawl()**:
   ```python
   # Kiểm tra xem có AdaptiveCrawler không
   if hasattr(self, "_deep_crawl_with_adaptive_crawler") and hasattr(self, "_adaptive_crawler") and self._adaptive_crawler is not None:
       # Sử dụng AdaptiveCrawler nếu có
       logger.info("Using AdaptiveCrawler for deep crawling")
       return self._deep_crawl_with_adaptive_crawler(
           url=url,
           max_depth=max_depth,
           max_pages=max_pages,
           timeout=timeout,
           include_html=include_html,
           respect_robots=respect_robots,
           **kwargs
       )
   else:
       # Sử dụng phương thức cũ nếu không có AdaptiveCrawler
       from .deep_crawl_improved import deep_crawl
       logger.info("Using legacy deep crawl method")
       return deep_crawl(
           self,
           url=url,
           max_depth=max_depth,
           max_pages=max_pages,
           timeout=timeout,
           include_html=include_html,
           respect_robots=respect_robots,
           **kwargs
       )
   ```

### J1.2: Xác định ranh giới rõ ràng giữa hai module (Hoàn thành)

Sau khi phân tích cấu trúc hiện tại của cả hai module, có thể xác định ranh giới rõ ràng giữa chúng như sau:

### J3.1: Phân tích các module CAPTCHA hiện tại (Hoàn thành)

Sau khi phân tích các module xử lý CAPTCHA trong cả hai module, có thể thấy rằng:

#### CaptchaHandler trong WebSearchAgentLocal

1. **Cấu trúc**:
   - Lớp `CaptchaHandler` trong `src/deep_research_core/utils/captcha_handler.py`
   - Tích hợp thông qua `captcha_handler_integration.py`
   - Có phiên bản đặc biệt `VietnameseCaptchaHandler` cho tiếng Việt

2. **Các phương thức chính**:
   - `detect_captcha(html_content)`: Phát hiện CAPTCHA trong nội dung HTML
   - `solve_captcha(html_content, url, captcha_type)`: Giải quyết CAPTCHA
   - `handle_captcha(url, html_content)`: Xử lý CAPTCHA tổng thể

3. **Các chiến lược xử lý CAPTCHA**:
   - `_try_different_user_agent(url)`: Thử sử dụng User-Agent khác
   - `_try_delay_and_retry(url)`: Thử chờ và thử lại
   - `_try_alternative_search_method(url)`: Thử phương thức tìm kiếm khác
   - `_try_browser_emulation(url)`: Thử giả lập trình duyệt với Playwright

4. **Tính năng đặc biệt**:
   - Hỗ trợ nhiều loại CAPTCHA (reCAPTCHA, hCAPTCHA, image CAPTCHA)
   - Có thể sử dụng dịch vụ Anti-Captcha
   - Ghi nhận domain có CAPTCHA để tránh trong tương lai
   - Hỗ trợ xử lý CAPTCHA tiếng Việt

#### CaptchaHandler trong AdaptiveCrawler

1. **Cấu trúc**:
   - Phương thức `_is_captcha(content)` và `_solve_captcha(url, response)` trong AdaptiveCrawler
   - Sử dụng CaptchaHandler từ `src/deep_research_core/agents/CaptchaHandler`

2. **Các phương thức chính**:
   - `_is_captcha(content)`: Phát hiện CAPTCHA trong nội dung
   - `_solve_captcha(url, response)`: Giải quyết CAPTCHA

3. **Các chiến lược xử lý CAPTCHA**:
   - Phát hiện CAPTCHA dựa trên từ khóa và mẫu HTML
   - Xử lý reCAPTCHA, hCAPTCHA và image CAPTCHA

4. **Tính năng đặc biệt**:
   - Tích hợp đơn giản hơn so với WebSearchAgentLocal
   - Ít chiến lược xử lý CAPTCHA hơn
   - Không có hỗ trợ đặc biệt cho tiếng Việt

#### So sánh và đánh giá

1. **Điểm chung**:
   - Cả hai đều có khả năng phát hiện CAPTCHA
   - Cả hai đều hỗ trợ nhiều loại CAPTCHA
   - Cả hai đều có thể sử dụng dịch vụ Anti-Captcha

2. **Điểm khác biệt**:
   - WebSearchAgentLocal có nhiều chiến lược xử lý CAPTCHA hơn
   - WebSearchAgentLocal có hỗ trợ đặc biệt cho tiếng Việt
   - AdaptiveCrawler có cách tích hợp đơn giản hơn
   - WebSearchAgentLocal có khả năng ghi nhận domain có CAPTCHA

3. **Đánh giá**:
   - WebSearchAgentLocal có module xử lý CAPTCHA hoàn thiện hơn
   - AdaptiveCrawler có cách tích hợp đơn giản và hiệu quả hơn
   - Nên thống nhất một module CaptchaHandler dùng chung cho cả hai module

### J4.1: Phân tích các module User-Agent hiện tại (Hoàn thành)

Sau khi phân tích các module xử lý User-Agent trong cả hai module, có thể thấy rằng:

#### UserAgentManager trong WebSearchAgentLocal

1. **Cấu trúc**:
   - Lớp `UserAgentManager` trong `src/deep_research_core/agents/advanced_web_search_features.py`
   - Phương thức `_get_rotated_user_agent()` trong WebSearchAgentLocal

2. **Các phương thức chính**:
   - `get_user_agent()`: Lấy User-Agent theo chiến lược xoay vòng
   - `_generate_random_user_agent()`: Tạo User-Agent ngẫu nhiên
   - `_get_default_user_agents()`: Lấy danh sách User-Agent mặc định

3. **Các chiến lược xoay vòng**:
   - `sequential`: Xoay vòng tuần tự
   - `random`: Xoay vòng ngẫu nhiên
   - `weighted`: Tạo User-Agent ngẫu nhiên

4. **Tính năng đặc biệt**:
   - Tạo User-Agent ngẫu nhiên dựa trên trọng số trình duyệt và hệ điều hành
   - Xoay vòng User-Agent sau một số lần sử dụng nhất định
   - Hỗ trợ khóa đồng bộ để tránh xung đột

#### UserAgentRotationManager trong AdaptiveCrawler

1. **Cấu trúc**:
   - Lớp `UserAgentRotationManager` trong `src/deep_research_core/crawlers/user_agent_rotation_manager.py`
   - Phương thức `get_random_user_agent()` trong AdaptiveCrawler

2. **Các phương thức chính**:
   - `get_user_agent(strategy)`: Lấy User-Agent theo chiến lược xoay vòng
   - `add_user_agent(user_agent)`: Thêm User-Agent vào danh sách
   - `remove_user_agent(user_agent)`: Xóa User-Agent khỏi danh sách
   - `filter_user_agents()`: Lọc User-Agent theo loại thiết bị, trình duyệt và hệ điều hành

3. **Các chiến lược xoay vòng**:
   - `round_robin`: Xoay vòng tuần tự
   - `random`: Xoay vòng ngẫu nhiên
   - `weighted`: Xoay vòng theo trọng số

4. **Tính năng đặc biệt**:
   - Lọc User-Agent theo loại thiết bị, trình duyệt và hệ điều hành
   - Tạo User-Agent ngẫu nhiên với tỷ lệ nhất định
   - Theo dõi thống kê sử dụng User-Agent
   - Hỗ trợ tải User-Agent từ file

#### So sánh và đánh giá

1. **Điểm chung**:
   - Cả hai đều hỗ trợ xoay vòng User-Agent
   - Cả hai đều hỗ trợ nhiều chiến lược xoay vòng
   - Cả hai đều có thể tạo User-Agent ngẫu nhiên

2. **Điểm khác biệt**:
   - UserAgentRotationManager có nhiều tính năng hơn (lọc, thống kê, tải từ file)
   - UserAgentManager có cách tạo User-Agent ngẫu nhiên phức tạp hơn
   - UserAgentRotationManager có cách tích hợp đơn giản hơn
   - UserAgentManager tích hợp chặt chẽ hơn với WebSearchAgentLocal

3. **Đánh giá**:
   - UserAgentRotationManager có nhiều tính năng và linh hoạt hơn
   - UserAgentManager có cách tạo User-Agent ngẫu nhiên tốt hơn
   - Nên thống nhất một module UserAgentManager dùng chung cho cả hai module, kết hợp ưu điểm của cả hai

### J5.1: Phân tích các module xử lý file hiện tại (Hoàn thành)

Sau khi phân tích các module xử lý file trong cả hai module, có thể thấy rằng:

#### WebSearchFileProcessor trong WebSearchAgentLocal

1. **Cấu trúc**:
   - Lớp `WebSearchFileProcessor` trong `src/deep_research_core/utils/web_search_file_processor.py`
   - Tích hợp thông qua `file_processor_integration.py`
   - Sử dụng `DocumentExtractor` để trích xuất nội dung từ file

2. **Các phương thức chính**:
   - `download_file(url, output_path, use_playwright)`: Tải xuống file từ URL
   - `process_file(url, output_path, ...)`: Tải xuống và phân tích file
   - `is_file_url(url)`: Kiểm tra xem URL có phải là file không

3. **Các phương thức xử lý file**:
   - Sử dụng `DocumentExtractor` để trích xuất nội dung từ file
   - Hỗ trợ nhiều định dạng file: PDF, DOCX, PPTX, XLSX, v.v.
   - Có thể sử dụng Playwright để tải xuống file JavaScript

4. **Tính năng đặc biệt**:
   - Hỗ trợ tải xuống file từ trang web JavaScript
   - Trích xuất nội dung từ nhiều định dạng file
   - Lưu cache file đã tải xuống
   - Hỗ trợ giới hạn kích thước file

#### FileHandler trong AdaptiveCrawler

1. **Cấu trúc**:
   - Lớp `FileHandler` trong `src/deep_research_core/crawlers/file_handler.py`
   - Phương thức `_extract_files(url, content)` trong AdaptiveCrawler
   - Phương thức `_extract_file_content(file_path, mime_type)` trong AdaptiveCrawler

2. **Các phương thức chính**:
   - `extract_files(url, content)`: Trích xuất file từ nội dung HTML
   - `download_file(url, output_path)`: Tải xuống file từ URL
   - `extract_file_content(file_path, mime_type)`: Trích xuất nội dung từ file

3. **Các phương thức xử lý file**:
   - Sử dụng `DocumentExtractor` để trích xuất nội dung từ file
   - Hỗ trợ các định dạng file phổ biến: PDF, DOCX, PPTX, XLSX
   - Trích xuất file từ nội dung HTML

4. **Tính năng đặc biệt**:
   - Trích xuất file từ nội dung HTML
   - Hỗ trợ tải xuống file từ URL tương đối
   - Trích xuất nội dung từ file đã tải xuống

#### FileFormatHandler

1. **Cấu trúc**:
   - Lớp `FileFormatHandler` trong `src/deep_research_core/utils/file_format_handler.py`
   - Sử dụng trong cả WebSearchFileProcessor và FileHandler

2. **Các phương thức chính**:
   - `extract_content(file_path, **kwargs)`: Trích xuất nội dung từ file
   - `_extract_pdf_content(file_path, **kwargs)`: Trích xuất nội dung từ file PDF
   - `_extract_docx_content(file_path, **kwargs)`: Trích xuất nội dung từ file DOCX
   - `_extract_pptx_content(file_path, **kwargs)`: Trích xuất nội dung từ file PPTX

3. **Các phương thức xử lý file**:
   - Hỗ trợ nhiều định dạng file: PDF, DOCX, PPTX, XLSX, TXT, HTML, v.v.
   - Trích xuất nội dung, metadata, hình ảnh, bảng, v.v.
   - Phân loại file theo loại: document, spreadsheet, text, data, web, ebook, image, audio

4. **Tính năng đặc biệt**:
   - Kiểm tra các thư viện phụ thuộc
   - Hỗ trợ fallback khi thiếu thư viện
   - Trích xuất nội dung từ nhiều định dạng file
   - Trích xuất metadata, hình ảnh, bảng, v.v.

#### So sánh và đánh giá

1. **Điểm chung**:
   - Cả hai đều hỗ trợ tải xuống file từ URL
   - Cả hai đều hỗ trợ trích xuất nội dung từ file
   - Cả hai đều sử dụng DocumentExtractor hoặc FileFormatHandler

2. **Điểm khác biệt**:
   - WebSearchFileProcessor có nhiều tính năng hơn (cache, giới hạn kích thước, Playwright)
   - FileHandler có khả năng trích xuất file từ nội dung HTML
   - WebSearchFileProcessor có cách tích hợp phức tạp hơn
   - FileHandler tích hợp chặt chẽ hơn với AdaptiveCrawler

3. **Đánh giá**:
   - WebSearchFileProcessor có nhiều tính năng và linh hoạt hơn
   - FileHandler có khả năng trích xuất file từ nội dung HTML tốt hơn
   - Nên thống nhất một module FileProcessor dùng chung cho cả hai module, kết hợp ưu điểm của cả hai

### J6.1: Phân tích các module xử lý JavaScript hiện tại (Hoàn thành)

Sau khi phân tích các module xử lý JavaScript trong cả hai module, có thể thấy rằng:

#### JavaScriptHandler trong AdaptiveCrawler

1. **Cấu trúc**:
   - Lớp `JavaScriptHandler` trong `src/deep_research_core/crawlers/javascript_handler.py`
   - Phương thức `_handle_spa()`, `_handle_infinite_scroll()`, và `_handle_ajax()` trong AdaptiveCrawler

2. **Các phương thức chính**:
   - `detect_javascript(content)`: Phát hiện JavaScript trong nội dung HTML
   - `handle_spa(page)`: Xử lý Single Page Application
   - `handle_ajax(page)`: Xử lý AJAX
   - `handle_lazy_loading(page)`: Xử lý lazy loading
   - `handle_infinite_scroll(page)`: Xử lý infinite scroll

3. **Các chiến lược xử lý JavaScript**:
   - Phát hiện SPA dựa trên các framework phổ biến (React, Vue, Angular, v.v.)
   - Xử lý AJAX bằng cách chờ mạng ổn định
   - Xử lý lazy loading bằng cách cuộn trang
   - Xử lý infinite scroll bằng cách cuộn trang nhiều lần

4. **Tính năng đặc biệt**:
   - Hỗ trợ nhiều loại JavaScript (SPA, AJAX, lazy loading, infinite scroll)
   - Theo dõi thống kê về JavaScript
   - Tùy chỉnh thời gian chờ và số lần thử lại
   - Hỗ trợ cả Playwright và Selenium

#### DynamicContentHandler trong WebSearchAgentLocal

1. **Cấu trúc**:
   - Lớp `DynamicContentHandler` trong `src/deep_research_core/agents/dynamic_content_handler.py`
   - Phương thức `_try_browser_emulation()` trong WebSearchAgentLocal

2. **Các phương thức chính**:
   - `_handle_infinite_scroll(page, max_scrolls, wait_time)`: Xử lý infinite scroll
   - `_handle_spa(page)`: Xử lý Single Page Application
   - `_handle_ajax(page)`: Xử lý AJAX
   - `_simulate_human_behavior(page)`: Giả lập hành vi người dùng

3. **Các chiến lược xử lý JavaScript**:
   - Xử lý infinite scroll bằng cách cuộn trang nhiều lần
   - Xử lý SPA bằng cách chờ trang tải xong
   - Xử lý AJAX bằng cách chờ mạng ổn định
   - Giả lập hành vi người dùng để tránh phát hiện bot

4. **Tính năng đặc biệt**:
   - Tập trung vào việc trích xuất nội dung từ trang web động
   - Hỗ trợ Playwright để xử lý JavaScript
   - Giả lập hành vi người dùng để tránh phát hiện bot
   - Tích hợp với CaptchaHandler để xử lý CAPTCHA

#### So sánh và đánh giá

1. **Điểm chung**:
   - Cả hai đều hỗ trợ xử lý SPA, AJAX, và infinite scroll
   - Cả hai đều sử dụng Playwright để xử lý JavaScript
   - Cả hai đều có khả năng giả lập hành vi người dùng

2. **Điểm khác biệt**:
   - JavaScriptHandler có nhiều tính năng hơn (phát hiện JavaScript, theo dõi thống kê)
   - DynamicContentHandler tích hợp chặt chẽ hơn với WebSearchAgentLocal
   - JavaScriptHandler hỗ trợ cả Playwright và Selenium
   - DynamicContentHandler tập trung vào việc trích xuất nội dung

3. **Đánh giá**:
   - JavaScriptHandler có nhiều tính năng và linh hoạt hơn
   - DynamicContentHandler tích hợp tốt hơn với WebSearchAgentLocal
   - Nên thống nhất một module JavaScriptHandler dùng chung cho cả hai module, kết hợp ưu điểm của cả hai

### J7.1: Phân tích các module xử lý phân trang hiện tại (Hoàn thành)

Sau khi phân tích các module xử lý phân trang trong cả hai module, có thể thấy rằng:

#### PaginationHandler trong AdaptiveCrawler

1. **Cấu trúc**:
   - Lớp `PaginationHandler` trong `src/deep_research_core/crawlers/pagination_handler.py`
   - Tích hợp trong AdaptiveCrawler thông qua `pagination_handler` attribute

2. **Các phương thức chính**:
   - `detect_pagination(content, url)`: Phát hiện phân trang trên trang web
   - `get_next_page(url, content)`: Lấy URL của trang tiếp theo
   - `get_prev_page(url, content)`: Lấy URL của trang trước
   - `get_page_numbers(url, content)`: Lấy danh sách số trang
   - `extract_paginated_content(url, content_selector, ...)`: Trích xuất nội dung từ trang phân trang

3. **Các chiến lược phát hiện phân trang**:
   - Phát hiện phân trang dựa trên CSS selector
   - Phát hiện phân trang dựa trên tham số URL
   - Phát hiện phân trang dựa trên mẫu đường dẫn URL
   - Phát hiện cuộn vô hạn (infinite scroll)
   - Phát hiện nút "Tải thêm" (load more)
   - Phát hiện phân trang AJAX

4. **Tính năng đặc biệt**:
   - Hỗ trợ nhiều loại phân trang (standard, url_param, url_path, infinite_scroll, load_more, ajax_pagination)
   - Theo dõi thống kê về phân trang
   - Tùy chỉnh số lượng trang tối đa và độ sâu tối đa
   - Hỗ trợ cả Playwright và Selenium để trích xuất nội dung từ trang phân trang

#### Xử lý phân trang trong WebSearchAgentLocal

1. **Cấu trúc**:
   - Không có module riêng biệt cho xử lý phân trang
   - Xử lý phân trang thông qua các phương thức tìm kiếm như `_search_searxng_crawlee()` và `_deep_crawl()`
   - Sử dụng AdaptiveCrawler để xử lý phân trang khi cần thiết

2. **Các phương thức chính**:
   - `_search_searxng_crawlee(query, num_results, ...)`: Tìm kiếm với SearXNG-Crawlee integration
   - `_search_with_advanced_crawlee(query, num_results, ...)`: Tìm kiếm với Advanced Crawlee
   - `_deep_crawl(url, max_depth, max_pages, ...)`: Crawl sâu vào trang web

3. **Các chiến lược xử lý phân trang**:
   - Sử dụng SearXNG-Crawlee để tìm kiếm và xử lý phân trang
   - Sử dụng Advanced Crawlee để tìm kiếm và xử lý phân trang
   - Sử dụng AdaptiveCrawler để crawl sâu và xử lý phân trang

4. **Tính năng đặc biệt**:
   - Tích hợp với SearXNG-Crawlee và Advanced Crawlee
   - Sử dụng AdaptiveCrawler để xử lý phân trang khi cần thiết
   - Hỗ trợ tìm kiếm với nhiều phương thức khác nhau

#### So sánh và đánh giá

1. **Điểm chung**:
   - Cả hai đều có khả năng xử lý phân trang
   - Cả hai đều sử dụng Playwright hoặc Selenium để xử lý phân trang
   - Cả hai đều hỗ trợ nhiều loại phân trang

2. **Điểm khác biệt**:
   - PaginationHandler có nhiều tính năng hơn (phát hiện phân trang, theo dõi thống kê)
   - WebSearchAgentLocal không có module riêng biệt cho xử lý phân trang
   - PaginationHandler hỗ trợ nhiều loại phân trang hơn
   - WebSearchAgentLocal tích hợp với SearXNG-Crawlee và Advanced Crawlee

3. **Đánh giá**:
   - PaginationHandler có nhiều tính năng và linh hoạt hơn
   - WebSearchAgentLocal có cách tích hợp đơn giản hơn
   - Nên thống nhất một module PaginationHandler dùng chung cho cả hai module, kết hợp ưu điểm của cả hai

### J8.1: Phân tích các module xử lý cấu trúc trang web hiện tại (Hoàn thành)

Sau khi phân tích các module xử lý cấu trúc trang web trong cả hai module, có thể thấy rằng:

#### SiteStructureHandler trong AdaptiveCrawler

1. **Cấu trúc**:
   - Lớp `SiteStructureHandler` trong `src/deep_research_core/crawlers/site_structure_handler.py`
   - Tích hợp trong AdaptiveCrawler thông qua các phương thức như `_is_allowed_by_robots_txt()`

2. **Các phương thức chính**:
   - `parse_robots_txt(url)`: Phân tích robots.txt
   - `parse_sitemap(url)`: Phân tích sitemap
   - `build_site_structure(start_url, max_depth)`: Xây dựng cấu trúc trang web
   - `prioritize_urls(urls, start_url)`: Ưu tiên hóa URL
   - `is_allowed_by_robots(url)`: Kiểm tra xem URL có được phép bởi robots.txt hay không

3. **Các chiến lược xử lý cấu trúc trang web**:
   - Phân tích robots.txt để xác định các đường dẫn được phép và không được phép
   - Phân tích sitemap để lấy danh sách URL và thông tin ưu tiên
   - Xây dựng cấu trúc trang web bằng cách crawl từ URL bắt đầu
   - Ưu tiên hóa URL dựa trên các mẫu và thông tin từ sitemap

4. **Tính năng đặc biệt**:
   - Hỗ trợ phân tích robots.txt và sitemap
   - Theo dõi thống kê về cấu trúc trang web
   - Tùy chỉnh độ sâu tối đa và số lượng URL tối đa
   - Hỗ trợ lọc URL dựa trên các mẫu

#### Xử lý cấu trúc trang web trong WebSearchAgentLocal

1. **Cấu trúc**:
   - Không có module riêng biệt cho xử lý cấu trúc trang web
   - Xử lý cấu trúc trang web thông qua các phương thức như `_extract_content_from_url()` và `_deep_crawl()`
   - Sử dụng AdaptiveCrawler để xử lý cấu trúc trang web khi cần thiết

2. **Các phương thức chính**:
   - `_extract_content_from_url(url)`: Trích xuất nội dung từ URL
   - `_deep_crawl(url, max_depth, max_pages, ...)`: Crawl sâu vào trang web
   - `_search_searxng_crawlee(query, num_results, ...)`: Tìm kiếm với SearXNG-Crawlee integration

3. **Các chiến lược xử lý cấu trúc trang web**:
   - Trích xuất nội dung từ trang web bằng cách tìm các container phổ biến
   - Sử dụng SearXNG-Crawlee để tìm kiếm và xử lý cấu trúc trang web
   - Sử dụng AdaptiveCrawler để crawl sâu và xử lý cấu trúc trang web

4. **Tính năng đặc biệt**:
   - Tích hợp với SearXNG-Crawlee và Advanced Crawlee
   - Sử dụng AdaptiveCrawler để xử lý cấu trúc trang web khi cần thiết
   - Hỗ trợ trích xuất nội dung từ các container phổ biến
   - Hỗ trợ trích xuất nội dung từ các container tiếng Việt

#### So sánh và đánh giá

1. **Điểm chung**:
   - Cả hai đều có khả năng xử lý cấu trúc trang web
   - Cả hai đều hỗ trợ robots.txt
   - Cả hai đều có khả năng trích xuất nội dung từ trang web

2. **Điểm khác biệt**:
   - SiteStructureHandler có nhiều tính năng hơn (phân tích sitemap, ưu tiên hóa URL)
   - WebSearchAgentLocal không có module riêng biệt cho xử lý cấu trúc trang web
   - SiteStructureHandler hỗ trợ xây dựng cấu trúc trang web
   - WebSearchAgentLocal tập trung vào việc trích xuất nội dung

3. **Đánh giá**:
   - SiteStructureHandler có nhiều tính năng và linh hoạt hơn
   - WebSearchAgentLocal có cách tích hợp đơn giản hơn
   - Nên thống nhất một module SiteStructureHandler dùng chung cho cả hai module, kết hợp ưu điểm của cả hai

### J9.1: Phân tích các module xử lý ngôn ngữ tiếng Việt hiện tại (Hoàn thành)

Sau khi phân tích các module xử lý ngôn ngữ tiếng Việt trong cả hai module, có thể thấy rằng:

#### Xử lý tiếng Việt trong WebSearchAgentLocal

1. **Cấu trúc**:
   - Các phương thức `_fix_vietnamese_encoding()` và `_is_vietnamese_text()` trong WebSearchAgentLocal
   - Module `vietnamese_search_methods.py` chứa các phương thức tìm kiếm tiếng Việt
   - Module `vietnamese_search_integration.py` và `vietnamese_search_integration_improved.py` để tích hợp tìm kiếm tiếng Việt

2. **Các phương thức chính**:
   - `_fix_vietnamese_encoding(text)`: Sửa lỗi mã hóa tiếng Việt
   - `_is_vietnamese_text(text)`: Kiểm tra xem văn bản có phải là tiếng Việt không
   - `search_vietnamese(query, num_results, engine)`: Tìm kiếm tiếng Việt
   - `search_coccoc(query, num_results, language)`: Tìm kiếm với Cốc Cốc
   - `search_wikitiengviet(query, num_results)`: Tìm kiếm với Wiki tiếng Việt
   - `search_baomoi(query, num_results)`: Tìm kiếm với Báo Mới

3. **Các chiến lược xử lý tiếng Việt**:
   - Sửa lỗi mã hóa tiếng Việt bằng cách thay thế các ký tự bị mã hóa sai
   - Phát hiện tiếng Việt dựa trên các ký tự đặc biệt và từ khóa tiếng Việt
   - Tìm kiếm tiếng Việt với các công cụ tìm kiếm Việt Nam
   - Trích xuất nội dung từ các trang web tiếng Việt

4. **Tính năng đặc biệt**:
   - Hỗ trợ tìm kiếm với Cốc Cốc, Wiki tiếng Việt, và Báo Mới
   - Tự động phát hiện ngôn ngữ tiếng Việt
   - Sửa lỗi mã hóa tiếng Việt
   - Tối ưu hóa truy vấn tìm kiếm tiếng Việt

#### Xử lý tiếng Việt trong AdaptiveCrawler

1. **Cấu trúc**:
   - Không có module riêng biệt cho xử lý tiếng Việt
   - Sử dụng các module chung như `language_detector` và `text_processor`

2. **Các phương thức chính**:
   - `_detect_language(text)`: Phát hiện ngôn ngữ của văn bản
   - `_process_text(text, language)`: Xử lý văn bản theo ngôn ngữ

3. **Các chiến lược xử lý tiếng Việt**:
   - Phát hiện ngôn ngữ tiếng Việt
   - Xử lý văn bản tiếng Việt
   - Trích xuất nội dung từ các trang web tiếng Việt

4. **Tính năng đặc biệt**:
   - Hỗ trợ phát hiện ngôn ngữ tiếng Việt
   - Xử lý văn bản tiếng Việt
   - Trích xuất nội dung từ các trang web tiếng Việt

#### Các module tiếng Việt khác

1. **VietnameseDiacriticProcessor**:
   - Xử lý dấu thanh tiếng Việt
   - Loại bỏ dấu thanh tiếng Việt
   - Chuẩn hóa dấu thanh tiếng Việt

2. **VietnameseDialectProcessor**:
   - Phát hiện phương ngữ tiếng Việt
   - Chuyển đổi giữa các phương ngữ tiếng Việt
   - Chuẩn hóa phương ngữ tiếng Việt

3. **VietnameseNLP**:
   - Phân đoạn từ tiếng Việt
   - Phân tích cú pháp tiếng Việt
   - Trích xuất thực thể tiếng Việt

4. **AdvancedVietnameseSemantic**:
   - Tối ưu hóa truy vấn tìm kiếm tiếng Việt
   - Phân tích ngữ nghĩa tiếng Việt
   - Tạo embedding tiếng Việt

#### So sánh và đánh giá

1. **Điểm chung**:
   - Cả hai đều có khả năng phát hiện ngôn ngữ tiếng Việt
   - Cả hai đều có khả năng xử lý văn bản tiếng Việt
   - Cả hai đều có khả năng trích xuất nội dung từ các trang web tiếng Việt

2. **Điểm khác biệt**:
   - WebSearchAgentLocal có nhiều tính năng hơn (tìm kiếm tiếng Việt, sửa lỗi mã hóa)
   - AdaptiveCrawler không có module riêng biệt cho xử lý tiếng Việt
   - WebSearchAgentLocal có tích hợp với các công cụ tìm kiếm Việt Nam
   - AdaptiveCrawler sử dụng các module chung cho xử lý ngôn ngữ

3. **Đánh giá**:
   - WebSearchAgentLocal có nhiều tính năng và linh hoạt hơn
   - AdaptiveCrawler có cách tích hợp đơn giản hơn
   - Nên thống nhất một module VietnameseLanguageHandler dùng chung cho cả hai module, kết hợp ưu điểm của cả hai

### J10.1: Tổng hợp kết quả phân tích và đề xuất mô hình tích hợp (Hoàn thành)

Sau khi phân tích chi tiết cả hai module, có thể tổng hợp kết quả và đề xuất mô hình tích hợp như sau:

#### Tổng hợp kết quả phân tích

1. **Phân tích module CAPTCHA**:
   - WebSearchAgentLocal có module xử lý CAPTCHA hoàn thiện hơn
   - AdaptiveCrawler có cách tích hợp đơn giản và hiệu quả hơn
   - Nên thống nhất một module CaptchaHandler dùng chung

2. **Phân tích module User-Agent**:
   - UserAgentRotationManager có nhiều tính năng và linh hoạt hơn
   - UserAgentManager có cách tạo User-Agent ngẫu nhiên tốt hơn
   - Nên thống nhất một module UserAgentManager dùng chung

3. **Phân tích module xử lý file**:
   - WebSearchFileProcessor có nhiều tính năng và linh hoạt hơn
   - FileHandler có khả năng trích xuất file từ nội dung HTML tốt hơn
   - Nên thống nhất một module FileProcessor dùng chung

4. **Phân tích module xử lý JavaScript**:
   - JavaScriptHandler có nhiều tính năng và linh hoạt hơn
   - DynamicContentHandler tích hợp tốt hơn với WebSearchAgentLocal
   - Nên thống nhất một module JavaScriptHandler dùng chung

5. **Phân tích module xử lý phân trang**:
   - PaginationHandler có nhiều tính năng và linh hoạt hơn
   - WebSearchAgentLocal có cách tích hợp đơn giản hơn
   - Nên thống nhất một module PaginationHandler dùng chung

6. **Phân tích module xử lý cấu trúc trang web**:
   - SiteStructureHandler có nhiều tính năng và linh hoạt hơn
   - WebSearchAgentLocal có cách tích hợp đơn giản hơn
   - Nên thống nhất một module SiteStructureHandler dùng chung

7. **Phân tích module xử lý ngôn ngữ tiếng Việt**:
   - WebSearchAgentLocal có nhiều tính năng và linh hoạt hơn
   - AdaptiveCrawler có cách tích hợp đơn giản hơn
   - Nên thống nhất một module VietnameseLanguageHandler dùng chung

#### Đề xuất mô hình tích hợp

Dựa trên kết quả phân tích, đề xuất mô hình tích hợp như sau:

1. **Mô hình tổng thể**:
   - Tách các module dùng chung thành các module riêng biệt
   - WebSearchAgentLocal tập trung vào tìm kiếm
   - AdaptiveCrawler tập trung vào crawl
   - Cả hai module sử dụng các module dùng chung

2. **Các module dùng chung**:
   - `CaptchaHandler`: Xử lý CAPTCHA
   - `UserAgentManager`: Quản lý User-Agent
   - `FileProcessor`: Xử lý file
   - `JavaScriptHandler`: Xử lý JavaScript
   - `PaginationHandler`: Xử lý phân trang
   - `SiteStructureHandler`: Xử lý cấu trúc trang web
   - `VietnameseLanguageHandler`: Xử lý ngôn ngữ tiếng Việt
   - `ConfigManager`: Quản lý cấu hình
   - `LanguageHandler`: Xử lý đa ngôn ngữ

3. **Cách tích hợp**:
   - Sử dụng dependency injection để tích hợp các module dùng chung
   - Sử dụng factory pattern để tạo các module dùng chung
   - Sử dụng adapter pattern để tích hợp các module dùng chung vào các module chính
   - Sử dụng strategy pattern để chọn chiến lược xử lý phù hợp

4. **Luồng xử lý**:
   - WebSearchAgentLocal sử dụng AdaptiveCrawler khi cần crawl sâu
   - AdaptiveCrawler sử dụng các module dùng chung
   - WebSearchAgentLocal sử dụng các module dùng chung
   - Cả hai module không trùng lặp chức năng

#### WebSearchAgentLocal (Tìm kiếm)

**Trách nhiệm chính**:
1. Tìm kiếm thông tin từ nhiều nguồn khác nhau (SearXNG, CocCoc, WikiTiengViet, BaoMoi)
2. Trích xuất nội dung từ các URL kết quả tìm kiếm
3. Xử lý ngôn ngữ đặc biệt (tiếng Việt)
4. Cache kết quả tìm kiếm và trích xuất
5. Đánh giá chất lượng kết quả tìm kiếm

**Ranh giới**:
- Không nên thực hiện crawl sâu (nên ủy quyền cho AdaptiveCrawler)
- Không nên xử lý các trang web phức tạp (SPA, infinite scroll)
- Không nên trích xuất cấu trúc trang web phức tạp

#### AdaptiveCrawler (Crawl)

**Trách nhiệm chính**:
1. Crawl sâu vào các trang web
2. Xử lý các trang web phức tạp (SPA, infinite scroll, AJAX)
3. Trích xuất cấu trúc trang web
4. Xử lý phân trang
5. Tối ưu hóa bộ nhớ khi crawl nhiều trang

**Ranh giới**:
- Không nên thực hiện tìm kiếm (nên ủy quyền cho WebSearchAgentLocal)
- Không nên xử lý ngôn ngữ đặc biệt (nên ủy quyền cho WebSearchAgentLocal)
- Không nên cache kết quả tìm kiếm

#### Các module dùng chung

Các module sau nên được tách ra thành module dùng chung cho cả hai module chính:

1. **CaptchaHandler**: Xử lý CAPTCHA
2. **UserAgentManager**: Quản lý User-Agent
3. **FileProcessor**: Xử lý file
4. **PlaywrightHandler**: Xử lý JavaScript, SPA
5. **ConfigManager**: Quản lý cấu hình
6. **LanguageHandler**: Xử lý đa ngôn ngữ và tiếng Việt
7. **SiteStructureHandler**: Trích xuất cấu trúc trang web
8. **PaginationHandler**: Xử lý phân trang

#### Mô hình tích hợp

Mô hình tích hợp giữa hai module nên như sau:

1. WebSearchAgentLocal sử dụng AdaptiveCrawler khi cần crawl sâu
2. AdaptiveCrawler sử dụng các module dùng chung
3. WebSearchAgentLocal sử dụng các module dùng chung
4. Cả hai module không nên trùng lặp chức năng

## Cách theo dõi tiến độ

1. Cập nhật trạng thái của từng nhiệm vụ trong bảng trên
2. Đánh dấu các nhiệm vụ đã hoàn thành
3. Thêm ghi chú về các vấn đề gặp phải hoặc giải pháp đã triển khai
4. Cập nhật kế hoạch triển khai nếu cần
