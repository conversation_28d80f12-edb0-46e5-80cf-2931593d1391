# Thiết kế module Playwright<PERSON>andler dùng chung

Dựa trên phân tích cách sử dụng Playwright trong cả WebSearchAgentLocal và AdaptiveCrawler, tôi đề xuất thiết kế module PlaywrightHandler dùng chung như sau:

## Cấu trúc module

```python
import os
import re
import time
import random
import logging
import tempfile
import asyncio
from typing import Dict, List, Optional, Any, Tuple, Union, Set, Callable
from urllib.parse import urlparse, urljoin, parse_qs, urlencode

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

# Ki<PERSON><PERSON> tra Playwright
try:
    from playwright.sync_api import sync_playwright, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erContext, TimeoutError as PlaywrightTimeoutError
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logger.warning("Playwright không khả dụng. Cài đặt với: pip install playwright")

class PlaywrightHandler:
    """
    Module xử lý Playwright dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """

    def __init__(
        self,
        headless: bool = True,
        browser_type: str = "chromium",
        user_agent: Optional[str] = None,
        user_agent_manager: Optional[Any] = None,
        viewport_width: int = 1280,
        viewport_height: int = 720,
        locale: str = "en-US",
        geolocation: Optional[Dict[str, float]] = None,
        timezone_id: Optional[str] = None,
        permissions: Optional[List[str]] = None,
        proxy: Optional[Dict[str, str]] = None,
        persistent_context_dir: Optional[str] = None,
        device_scale_factor: float = 1.0,
        is_mobile: bool = False,
        has_touch: bool = False,
        download_dir: Optional[str] = None,
        ignore_https_errors: bool = False,
        java_script_enabled: bool = True,
        bypass_csp: bool = False,
        timeout: int = 30000,
        slow_mo: int = 0,
        stealth_mode: bool = True,
        handle_captcha: bool = True,
        captcha_handler: Optional[Any] = None,
        handle_spa: bool = True,
        handle_infinite_scroll: bool = True,
        handle_forms: bool = True,
        handle_pagination: bool = True,
        pagination_handler: Optional[Any] = None,
        form_handler: Optional[Any] = None,
        max_wait_time: int = 60000,
        wait_for_selectors: Optional[List[str]] = None,
        wait_for_load_state: str = "networkidle",
        wait_for_url: Optional[str] = None,
        wait_for_function: Optional[str] = None,
        wait_for_timeout: int = 5000,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        max_concurrent_pages: int = 5,
        max_browser_instances: int = 1,
        memory_optimization: bool = True,
        memory_threshold: float = 80.0,
        memory_check_interval: int = 10,
        cache_enabled: bool = True,
        cache_ttl: int = 3600 * 24,  # 1 day
        cache_size: int = 1000,
        verbose: bool = False,
        **kwargs
    ):
        """
        Khởi tạo PlaywrightHandler.

        Args:
            headless: Có chạy ở chế độ headless không
            browser_type: Loại trình duyệt (chromium, firefox, webkit)
            user_agent: User-Agent để sử dụng
            user_agent_manager: Đối tượng UserAgentManager để lấy User-Agent
            viewport_width: Chiều rộng viewport
            viewport_height: Chiều cao viewport
            locale: Ngôn ngữ và vùng
            geolocation: Vị trí địa lý
            timezone_id: ID múi giờ
            permissions: Danh sách quyền
            proxy: Cấu hình proxy
            persistent_context_dir: Thư mục lưu context
            device_scale_factor: Tỷ lệ thiết bị
            is_mobile: Có giả lập thiết bị di động không
            has_touch: Có hỗ trợ cảm ứng không
            download_dir: Thư mục tải xuống
            ignore_https_errors: Có bỏ qua lỗi HTTPS không
            java_script_enabled: Có bật JavaScript không
            bypass_csp: Có bỏ qua CSP không
            timeout: Thời gian chờ tối đa (ms)
            slow_mo: Thời gian chờ giữa các thao tác (ms)
            stealth_mode: Có sử dụng chế độ ẩn danh không
            handle_captcha: Có xử lý CAPTCHA không
            captcha_handler: Đối tượng CaptchaHandler để xử lý CAPTCHA
            handle_spa: Có xử lý SPA không
            handle_infinite_scroll: Có xử lý infinite scroll không
            handle_forms: Có xử lý form không
            handle_pagination: Có xử lý phân trang không
            pagination_handler: Đối tượng PaginationHandler để xử lý phân trang
            form_handler: Đối tượng FormHandler để xử lý form
            max_wait_time: Thời gian chờ tối đa (ms)
            wait_for_selectors: Danh sách selector để chờ
            wait_for_load_state: Trạng thái tải để chờ
            wait_for_url: URL để chờ
            wait_for_function: Hàm JavaScript để chờ
            wait_for_timeout: Thời gian chờ (ms)
            max_retries: Số lần thử lại tối đa
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            max_concurrent_pages: Số lượng trang đồng thời tối đa
            max_browser_instances: Số lượng instance trình duyệt tối đa
            memory_optimization: Có tối ưu hóa bộ nhớ không
            memory_threshold: Ngưỡng bộ nhớ để tối ưu hóa (%)
            memory_check_interval: Khoảng thời gian kiểm tra bộ nhớ (giây)
            cache_enabled: Có bật cache không
            cache_ttl: Thời gian sống của cache (giây)
            cache_size: Kích thước cache
            verbose: Có hiển thị thông tin chi tiết không
            **kwargs: Các tham số bổ sung
        """
        # Kiểm tra Playwright
        if not PLAYWRIGHT_AVAILABLE:
            raise ImportError("Playwright không khả dụng. Cài đặt với: pip install playwright")

        # Khởi tạo các thuộc tính
        self.headless = headless
        self.browser_type = browser_type
        self.user_agent = user_agent
        self.user_agent_manager = user_agent_manager
        self.viewport_width = viewport_width
        self.viewport_height = viewport_height
        self.locale = locale
        self.geolocation = geolocation
        self.timezone_id = timezone_id
        self.permissions = permissions
        self.proxy = proxy
        self.persistent_context_dir = persistent_context_dir
        self.device_scale_factor = device_scale_factor
        self.is_mobile = is_mobile
        self.has_touch = has_touch
        self.download_dir = download_dir or os.path.join(tempfile.gettempdir(), "playwright_downloads")
        self.ignore_https_errors = ignore_https_errors
        self.java_script_enabled = java_script_enabled
        self.bypass_csp = bypass_csp
        self.timeout = timeout
        self.slow_mo = slow_mo
        self.stealth_mode = stealth_mode
        self.handle_captcha = handle_captcha
        self.captcha_handler = captcha_handler
        self.handle_spa = handle_spa
        self.handle_infinite_scroll = handle_infinite_scroll
        self.handle_forms = handle_forms
        self.handle_pagination = handle_pagination
        self.pagination_handler = pagination_handler
        self.form_handler = form_handler
        self.max_wait_time = max_wait_time
        self.wait_for_selectors = wait_for_selectors or []
        self.wait_for_load_state = wait_for_load_state
        self.wait_for_url = wait_for_url
        self.wait_for_function = wait_for_function
        self.wait_for_timeout = wait_for_timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.max_concurrent_pages = max_concurrent_pages
        self.max_browser_instances = max_browser_instances
        self.memory_optimization = memory_optimization
        self.memory_threshold = memory_threshold
        self.memory_check_interval = memory_check_interval
        self.cache_enabled = cache_enabled
        self.cache_ttl = cache_ttl
        self.cache_size = cache_size
        self.verbose = verbose

        # Khởi tạo các thuộc tính bổ sung
        self.playwright = None
        self.browser = None
        self.context = None
        self.page_pool = []
        self.browser_instances = []
        self.cache = {}
        self.last_memory_check = time.time()
        self.stats = {
            "pages_visited": 0,
            "successful_visits": 0,
            "failed_visits": 0,
            "captchas_detected": 0,
            "captchas_solved": 0,
            "forms_filled": 0,
            "downloads": 0,
            "screenshots": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "total_time": 0,
        }

        # Tạo thư mục tải xuống nếu chưa tồn tại
        os.makedirs(self.download_dir, exist_ok=True)

        # Khởi tạo Playwright
        self._initialize_playwright()

        if self.verbose:
            logger.info(f"PlaywrightHandler đã được khởi tạo với trình duyệt {self.browser_type}")
```

## Các phương thức chính

1. **extract_content(url, wait_for_selectors=None, wait_time=0, scroll_count=0)**: Trích xuất nội dung từ URL
2. **handle_spa(page, url)**: Xử lý Single Page Application
3. **handle_infinite_scroll(page, url, max_scrolls=10, scroll_delay=1.0)**: Xử lý Infinite Scroll
4. **handle_captcha(page, url)**: Xử lý CAPTCHA
5. **download_file(url, output_path=None)**: Tải xuống file
6. **search(query, num_results=10, language="en")**: Tìm kiếm với Playwright
7. **take_screenshot(url, output_path=None, full_page=True)**: Chụp ảnh màn hình
8. **fill_form(url, form_data, submit=True)**: Điền form
9. **execute_javascript(url, script)**: Thực thi JavaScript

## Các phương thức hỗ trợ

1. **_initialize_playwright()**: Khởi tạo Playwright
2. **_get_browser_context_options()**: Lấy tùy chọn cho browser context
3. **_get_user_agent()**: Lấy User-Agent
4. **_get_cache_key(url)**: Lấy khóa cache
5. **_get_from_cache(key)**: Lấy kết quả từ cache
6. **_save_to_cache(key, result)**: Lưu kết quả vào cache
7. **_clean_cache()**: Dọn dẹp cache
8. **_apply_stealth_mode(page)**: Áp dụng chế độ ẩn danh
9. **_wait_for_page_load(page)**: Chờ trang tải xong
10. **_handle_navigation(page, url)**: Xử lý điều hướng
11. **_handle_errors(page)**: Xử lý lỗi
12. **_handle_dialogs(page)**: Xử lý hộp thoại
13. **_handle_authentication(page, url)**: Xử lý xác thực
14. **_handle_cookies(page, url)**: Xử lý cookie
15. **_handle_storage(page, url)**: Xử lý storage
16. **_handle_network(page)**: Xử lý mạng
17. **_handle_requests(page)**: Xử lý request
18. **_handle_responses(page)**: Xử lý response
19. **_handle_console(page)**: Xử lý console
20. **_handle_dialog(page)**: Xử lý dialog

## Các tính năng đặc biệt

1. **Xử lý SPA**: Hỗ trợ xử lý các trang Single Page Application
2. **Xử lý Infinite Scroll**: Hỗ trợ xử lý các trang có cuộn vô hạn
3. **Xử lý CAPTCHA**: Tích hợp với CaptchaHandler để xử lý CAPTCHA
4. **Xử lý Form**: Tích hợp với FormHandler để xử lý form
5. **Xử lý Phân trang**: Tích hợp với PaginationHandler để xử lý phân trang
6. **Chế độ ẩn danh**: Giúp tránh bị phát hiện là bot
7. **Cache kết quả**: Lưu cache kết quả để tránh tải lại
8. **Xử lý lỗi**: Tự động xử lý các lỗi phổ biến
9. **Xử lý hộp thoại**: Tự động xử lý các hộp thoại
10. **Xử lý xác thực**: Hỗ trợ xác thực HTTP và xác thực form
11. **Xử lý cookie**: Hỗ trợ quản lý cookie
12. **Xử lý storage**: Hỗ trợ quản lý localStorage và sessionStorage
13. **Xử lý mạng**: Hỗ trợ chặn và sửa đổi request/response
14. **Xử lý console**: Hỗ trợ ghi log console
15. **Xử lý dialog**: Hỗ trợ xử lý dialog

## Cách tích hợp

### Tích hợp vào WebSearchAgentLocal

```python
from ..utils.shared.playwright_handler import PlaywrightHandler
from ..utils.shared.integration import integrate_playwright_handler

def integrate_playwright_handler(agent, config=None):
    """
    Tích hợp PlaywrightHandler vào WebSearchAgentLocal.
    """
    # Cấu hình mặc định
    default_config = {
        "headless": True,
        "browser_type": "chromium",
        "stealth_mode": True,
        "handle_captcha": True,
        "handle_spa": True,
        "handle_infinite_scroll": True,
        "handle_forms": True,
        "handle_pagination": True,
        "cache_enabled": True,
        "verbose": agent.verbose
    }

    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    playwright_config = {**default_config, **(config or {})}

    # Sử dụng CaptchaHandler nếu có
    if hasattr(agent, "_captcha_handler") and agent._captcha_handler:
        playwright_config["handle_captcha"] = True
        playwright_config["captcha_handler"] = agent._captcha_handler

    # Khởi tạo PlaywrightHandler
    agent._playwright_handler = PlaywrightHandler(**playwright_config)

    # Thêm phương thức extract_content vào agent
    agent.extract_content = lambda url, **kwargs: agent._playwright_handler.extract_content(url, **kwargs)

    # Thêm phương thức search vào agent
    agent._search_with_playwright = lambda query, num_results=10, language="en": agent._playwright_handler.search(query, num_results, language)

    # Đánh dấu là đã tích hợp
    agent.playwright_integrated = True

    logger.info("PlaywrightHandler đã được tích hợp thành công vào WebSearchAgentLocal")

    return agent
```

### Tích hợp vào AdaptiveCrawler

```python
from ..utils.shared.playwright_handler import PlaywrightHandler
from ..utils.shared.integration import integrate_playwright_handler

def integrate_playwright_handler(crawler, config=None):
    """
    Tích hợp PlaywrightHandler vào AdaptiveCrawler.
    """
    # Cấu hình mặc định
    default_config = {
        "headless": True,
        "browser_type": "chromium",
        "stealth_mode": True,
        "handle_captcha": True,
        "handle_spa": True,
        "handle_infinite_scroll": True,
        "handle_forms": True,
        "handle_pagination": True,
        "cache_enabled": True,
        "verbose": crawler.verbose
    }

    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    playwright_config = {**default_config, **(config or {})}

    # Sử dụng CaptchaHandler nếu có
    if hasattr(crawler, "captcha_handler") and crawler.captcha_handler:
        playwright_config["handle_captcha"] = True
        playwright_config["captcha_handler"] = crawler.captcha_handler

    # Sử dụng PaginationHandler nếu có
    if hasattr(crawler, "pagination_handler") and crawler.pagination_handler:
        playwright_config["handle_pagination"] = True
        playwright_config["pagination_handler"] = crawler.pagination_handler

    # Sử dụng FormHandler nếu có
    if hasattr(crawler, "form_handler") and crawler.form_handler:
        playwright_config["handle_forms"] = True
        playwright_config["form_handler"] = crawler.form_handler

    # Khởi tạo PlaywrightHandler
    crawler.playwright_handler = PlaywrightHandler(**playwright_config)

    # Thay thế phương thức _crawl_with_playwright
    crawler._crawl_with_playwright = lambda url, **kwargs: crawler.playwright_handler.extract_content(url, **kwargs)

    # Đánh dấu là đã tích hợp
    crawler.playwright_integrated = True

    logger.info("PlaywrightHandler đã được tích hợp thành công vào AdaptiveCrawler")

    return crawler
```
