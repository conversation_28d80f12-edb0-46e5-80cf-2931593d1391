# Thiết kế module SiteStructureHandler dùng chung

Dựa trên phân tích các module xử lý cấu trúc trang web hiện tại, tôi đề xuất thiết kế module SiteStructureHandler dùng chung như sau:

## Cấu trúc module

```python
import os
import re
import json
import time
import logging
import threading
from typing import Dict, List, Optional, Any, Tuple, Union, Set
from urllib.parse import urlparse, urljoin
from collections import defaultdict
import requests
from bs4 import BeautifulSoup

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

class SiteStructureHandler:
    """
    Module xử lý cấu trúc trang web dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """

    def __init__(
        self,
        respect_robots: bool = True,
        use_sitemap: bool = True,
        max_depth: int = 3,
        max_urls: int = 1000,
        max_urls_per_domain: int = 100,
        timeout: float = 10.0,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        user_agent: Optional[str] = None,
        user_agent_manager: Optional[Any] = None,
        verify_ssl: bool = True,
        proxies: Optional[Dict[str, str]] = None,
        cookies: Optional[Dict[str, str]] = None,
        headers: Optional[Dict[str, str]] = None,
        excluded_extensions: Optional[List[str]] = None,
        excluded_patterns: Optional[List[str]] = None,
        included_patterns: Optional[List[str]] = None,
        prioritize_patterns: Optional[List[str]] = None,
        extract_metadata: bool = True,
        extract_links: bool = True,
        extract_images: bool = False,
        extract_files: bool = False,
        extract_structured_data: bool = False,
        max_concurrent_requests: int = 5,
        cache_enabled: bool = True,
        cache_ttl: int = 3600 * 24,  # 1 day
        verbose: bool = False,
        **kwargs
    ):
        """
        Khởi tạo SiteStructureHandler.

        Args:
            respect_robots: Tuân thủ robots.txt hay không
            use_sitemap: Sử dụng sitemap hay không
            max_depth: Độ sâu tối đa khi xây dựng cấu trúc
            max_urls: Số lượng URL tối đa
            max_urls_per_domain: Số lượng URL tối đa cho mỗi domain
            timeout: Thời gian timeout cho mỗi request (giây)
            max_retries: Số lần thử lại tối đa khi request thất bại
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            user_agent: User-Agent header
            user_agent_manager: Đối tượng UserAgentManager để lấy User-Agent
            verify_ssl: Xác minh chứng chỉ SSL hay không
            proxies: Danh sách proxy
            cookies: Cookies cho request
            headers: Headers cho request
            excluded_extensions: Danh sách các phần mở rộng file bị loại trừ
            excluded_patterns: Danh sách các mẫu URL bị loại trừ
            included_patterns: Danh sách các mẫu URL được bao gồm
            prioritize_patterns: Danh sách các mẫu URL được ưu tiên
            extract_metadata: Trích xuất metadata hay không
            extract_links: Trích xuất links hay không
            extract_images: Trích xuất images hay không
            extract_files: Trích xuất files hay không
            extract_structured_data: Trích xuất dữ liệu có cấu trúc hay không
            max_concurrent_requests: Số lượng request đồng thời tối đa
            cache_enabled: Bật cache hay không
            cache_ttl: Thời gian sống của cache (giây)
            verbose: Ghi log chi tiết
            **kwargs: Các tham số bổ sung
        """
        # Khởi tạo các thuộc tính
        self.respect_robots = respect_robots
        self.use_sitemap = use_sitemap
        self.max_depth = max_depth
        self.max_urls = max_urls
        self.max_urls_per_domain = max_urls_per_domain
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.user_agent = user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
        self.user_agent_manager = user_agent_manager
        self.verify_ssl = verify_ssl
        self.proxies = proxies or {}
        self.cookies = cookies or {}
        self.headers = headers or {}
        self.excluded_extensions = excluded_extensions or self._get_default_excluded_extensions()
        self.excluded_patterns = excluded_patterns or []
        self.included_patterns = included_patterns or []
        self.prioritize_patterns = prioritize_patterns or []
        self.extract_metadata = extract_metadata
        self.extract_links = extract_links
        self.extract_images = extract_images
        self.extract_files = extract_files
        self.extract_structured_data = extract_structured_data
        self.max_concurrent_requests = max_concurrent_requests
        self.cache_enabled = cache_enabled
        self.cache_ttl = cache_ttl
        self.verbose = verbose

        # Khởi tạo các thuộc tính bổ sung
        self.robots_cache = {}
        self.sitemap_cache = {}
        self.url_priorities = {}
        self.visited_urls = set()
        self.domain_counts = defaultdict(int)
        self.cache = {}
        self.cache_lock = threading.Lock()
        self.request_semaphore = threading.Semaphore(self.max_concurrent_requests)

        # Khởi tạo session
        self.session = requests.Session()
        self.session.headers.update({"User-Agent": self.user_agent})
        if self.proxies:
            self.session.proxies.update(self.proxies)
        if self.cookies:
            self.session.cookies.update(self.cookies)
        if self.headers:
            self.session.headers.update(self.headers)

        # Khởi tạo thống kê
        self.stats = {
            "robots_parsed": 0,
            "sitemaps_parsed": 0,
            "urls_found": 0,
            "urls_visited": 0,
            "urls_excluded": 0,
            "urls_included": 0,
            "urls_prioritized": 0,
            "domains_found": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "request_errors": 0,
            "request_retries": 0,
        }
```

## Các phương thức chính

1. **parse_robots_txt(url)**: Phân tích robots.txt
2. **parse_sitemap(url)**: Phân tích sitemap
3. **build_site_structure(start_url, max_depth)**: Xây dựng cấu trúc trang web
4. **prioritize_urls(urls, start_url)**: Ưu tiên hóa URL
5. **is_allowed_by_robots(url)**: Kiểm tra xem URL có được phép bởi robots.txt hay không
6. **extract_links(url, content)**: Trích xuất links từ nội dung
7. **extract_metadata(url, content)**: Trích xuất metadata từ nội dung
8. **extract_structured_data(url, content)**: Trích xuất dữ liệu có cấu trúc từ nội dung
9. **extract_site_structure(url, max_depth, max_urls)**: Trích xuất cấu trúc trang web
10. **detect_page_type(url, content)**: Phát hiện loại trang (trang chủ, trang danh mục, trang chi tiết, v.v.)
11. **detect_site_type(url, content)**: Phát hiện loại trang web (blog, thương mại điện tử, tin tức, v.v.)
12. **extract_navigation(url, content)**: Trích xuất menu điều hướng
13. **extract_breadcrumbs(url, content)**: Trích xuất breadcrumb
14. **extract_pagination(url, content)**: Trích xuất phân trang
15. **extract_forms(url, content)**: Trích xuất form
16. **extract_images(url, content)**: Trích xuất hình ảnh
17. **extract_tables(url, content)**: Trích xuất bảng
18. **extract_lists(url, content)**: Trích xuất danh sách
19. **extract_headings(url, content)**: Trích xuất tiêu đề

## Các phương thức hỗ trợ

1. **_get_default_excluded_extensions()**: Lấy danh sách các phần mở rộng file bị loại trừ mặc định
2. **_is_valid_url(url, domain)**: Kiểm tra xem URL có hợp lệ hay không
3. **_get_cache_key(url)**: Lấy khóa cache cho URL
4. **_get_from_cache(url)**: Lấy kết quả từ cache
5. **_save_to_cache(url, result)**: Lưu kết quả vào cache
6. **_get_user_agent()**: Lấy User-Agent
7. **_make_request(url)**: Thực hiện request
8. **_normalize_url(url)**: Chuẩn hóa URL
9. **_extract_domain(url)**: Trích xuất domain từ URL
10. **_is_same_domain(url1, url2)**: Kiểm tra xem hai URL có cùng domain hay không

## Các tính năng đặc biệt

1. **Phân tích robots.txt**: Phân tích robots.txt để xác định các đường dẫn được phép và không được phép
2. **Phân tích sitemap**: Phân tích sitemap để lấy danh sách URL và thông tin ưu tiên
3. **Xây dựng cấu trúc trang web**: Xây dựng cấu trúc trang web từ URL bắt đầu
4. **Ưu tiên hóa URL**: Ưu tiên hóa URL dựa trên các mẫu và thông tin từ sitemap
5. **Phát hiện loại trang**: Phát hiện loại trang (trang chủ, trang danh mục, trang chi tiết, v.v.)
6. **Phát hiện loại trang web**: Phát hiện loại trang web (blog, thương mại điện tử, tin tức, v.v.)
7. **Trích xuất menu điều hướng**: Trích xuất menu điều hướng từ trang web
8. **Trích xuất breadcrumb**: Trích xuất breadcrumb từ trang web
9. **Trích xuất phân trang**: Trích xuất phân trang từ trang web
10. **Trích xuất form**: Trích xuất form từ trang web
11. **Trích xuất dữ liệu có cấu trúc**: Trích xuất dữ liệu có cấu trúc từ nội dung (JSON-LD, Microdata, RDFa)
12. **Trích xuất metadata**: Trích xuất metadata từ trang web
13. **Trích xuất liên kết**: Trích xuất liên kết từ trang web
14. **Trích xuất hình ảnh**: Trích xuất hình ảnh từ trang web
15. **Trích xuất bảng**: Trích xuất bảng từ trang web
16. **Trích xuất danh sách**: Trích xuất danh sách từ trang web
17. **Trích xuất tiêu đề**: Trích xuất tiêu đề từ trang web
18. **Cache kết quả**: Lưu cache kết quả để tránh truy cập lại
19. **Xử lý đồng thời**: Hỗ trợ xử lý đồng thời để tăng hiệu suất
20. **Thống kê**: Theo dõi thống kê về việc sử dụng

## Cách tích hợp

### Tích hợp vào WebSearchAgentLocal

```python
from ..utils.shared.site_structure_handler import SiteStructureHandler
from ..utils.shared.integration import integrate_site_structure_handler

def integrate_site_structure_handler(agent, config=None):
    """
    Tích hợp SiteStructureHandler vào WebSearchAgentLocal.
    """
    # Cấu hình mặc định
    default_config = {
        "respect_robots": True,
        "use_sitemap": True,
        "max_depth": 3,
        "max_urls": 1000,
        "max_urls_per_domain": 100,
        "extract_metadata": True,
        "extract_links": True,
        "extract_images": False,
        "extract_files": False,
        "extract_structured_data": True,
        "extract_breadcrumbs": True,
        "extract_navigation": True,
        "extract_pagination": True,
        "extract_forms": False,
        "detect_page_type": True,
        "detect_site_type": True,
        "detect_language": True,
        "cache_enabled": True,
        "verbose": agent.verbose
    }

    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    site_structure_config = {**default_config, **(config or {})}

    # Sử dụng UserAgentManager nếu có
    if hasattr(agent, "_user_agent_manager") and agent._user_agent_manager:
        site_structure_config["user_agent_manager"] = agent._user_agent_manager
    elif hasattr(agent, "user_agent_manager") and agent.user_agent_manager:
        site_structure_config["user_agent_manager"] = agent.user_agent_manager

    # Sử dụng PlaywrightHandler nếu có
    if hasattr(agent, "_playwright_handler") and agent._playwright_handler:
        site_structure_config["use_playwright"] = True
        site_structure_config["playwright_handler"] = agent._playwright_handler
    elif hasattr(agent, "playwright_handler") and agent.playwright_handler:
        site_structure_config["use_playwright"] = True
        site_structure_config["playwright_handler"] = agent.playwright_handler

    # Khởi tạo SiteStructureHandler
    agent._site_structure_handler = SiteStructureHandler(**site_structure_config)

    # Thêm phương thức is_allowed_by_robots vào agent
    agent._is_allowed_by_robots = lambda url: agent._site_structure_handler.is_allowed_by_robots(url)

    # Thêm phương thức extract_site_structure vào agent
    agent.extract_site_structure = lambda url, max_depth=None, max_urls=None: agent._site_structure_handler.extract_site_structure(url, max_depth, max_urls)

    # Thêm phương thức detect_page_type vào agent
    agent.detect_page_type = lambda url, content=None: agent._site_structure_handler.detect_page_type(url, content)

    # Thêm phương thức detect_site_type vào agent
    agent.detect_site_type = lambda url, content=None: agent._site_structure_handler.detect_site_type(url, content)

    # Thêm phương thức extract_navigation vào agent
    agent.extract_navigation = lambda url, content=None: agent._site_structure_handler.extract_navigation(url, content)

    # Thêm phương thức extract_breadcrumbs vào agent
    agent.extract_breadcrumbs = lambda url, content=None: agent._site_structure_handler.extract_breadcrumbs(url, content)

    # Thêm phương thức extract_pagination vào agent
    agent.extract_pagination = lambda url, content=None: agent._site_structure_handler.extract_pagination(url, content)

    # Đánh dấu là đã tích hợp
    agent.site_structure_handler_integrated = True

    logger.info("SiteStructureHandler đã được tích hợp thành công vào WebSearchAgentLocal")

    return agent
```

### Tích hợp vào AdaptiveCrawler

```python
from ..utils.shared.site_structure_handler import SiteStructureHandler
from ..utils.shared.integration import integrate_site_structure_handler

def integrate_site_structure_handler(crawler, config=None):
    """
    Tích hợp SiteStructureHandler vào AdaptiveCrawler.
    """
    # Cấu hình mặc định
    default_config = {
        "respect_robots": getattr(crawler, "respect_robots_txt", True),
        "use_sitemap": getattr(crawler, "site_map_enabled", True),
        "max_depth": getattr(crawler, "max_depth", 3),
        "max_urls": getattr(crawler, "max_total_urls", 1000),
        "max_urls_per_domain": getattr(crawler, "max_urls_per_domain", 100),
        "timeout": getattr(crawler, "timeout", 30.0),
        "max_retries": getattr(crawler, "max_retries", 3),
        "retry_delay": getattr(crawler, "retry_delay", 2.0),
        "user_agent": getattr(crawler, "user_agent", None),
        "verify_ssl": getattr(crawler, "verify_ssl", True),
        "proxies": getattr(crawler, "proxies", None),
        "cookies": getattr(crawler, "cookies", None),
        "headers": getattr(crawler, "headers", None),
        "excluded_extensions": getattr(crawler, "excluded_extensions", None),
        "extract_metadata": True,
        "extract_links": True,
        "extract_images": getattr(crawler, "extract_images", False),
        "extract_files": getattr(crawler, "extract_files", False),
        "extract_structured_data": getattr(crawler, "extract_structured_data", True),
        "extract_breadcrumbs": True,
        "extract_navigation": True,
        "extract_pagination": True,
        "extract_forms": getattr(crawler, "extract_forms", False),
        "detect_page_type": True,
        "detect_site_type": True,
        "detect_language": getattr(crawler, "detect_language", True),
        "max_concurrent_requests": getattr(crawler, "max_threads", 5),
        "cache_enabled": True,
        "verbose": getattr(crawler, "verbose", False)
    }

    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    site_structure_config = {**default_config, **(config or {})}

    # Sử dụng UserAgentManager nếu có
    if hasattr(crawler, "user_agent_manager") and crawler.user_agent_manager:
        site_structure_config["user_agent_manager"] = crawler.user_agent_manager

    # Sử dụng PlaywrightHandler nếu có
    if hasattr(crawler, "playwright_handler") and crawler.playwright_handler:
        site_structure_config["use_playwright"] = True
        site_structure_config["playwright_handler"] = crawler.playwright_handler

    # Khởi tạo SiteStructureHandler
    crawler.site_structure_handler = SiteStructureHandler(**site_structure_config)

    # Thay thế phương thức _is_allowed_by_robots_txt nếu có
    if hasattr(crawler, "_is_allowed_by_robots_txt"):
        crawler._is_allowed_by_robots_txt = lambda url: crawler.site_structure_handler.is_allowed_by_robots(url)

    # Thêm phương thức extract_site_structure vào crawler
    crawler.extract_site_structure = lambda url, max_depth=None, max_urls=None: crawler.site_structure_handler.extract_site_structure(url, max_depth, max_urls)

    # Thêm phương thức detect_page_type vào crawler
    crawler.detect_page_type = lambda url, content=None: crawler.site_structure_handler.detect_page_type(url, content)

    # Thêm phương thức detect_site_type vào crawler
    crawler.detect_site_type = lambda url, content=None: crawler.site_structure_handler.detect_site_type(url, content)

    # Thêm phương thức extract_navigation vào crawler
    crawler.extract_navigation = lambda url, content=None: crawler.site_structure_handler.extract_navigation(url, content)

    # Thêm phương thức extract_breadcrumbs vào crawler
    crawler.extract_breadcrumbs = lambda url, content=None: crawler.site_structure_handler.extract_breadcrumbs(url, content)

    # Thêm phương thức extract_pagination vào crawler
    crawler.extract_pagination = lambda url, content=None: crawler.site_structure_handler.extract_pagination(url, content)

    # Đánh dấu là đã tích hợp
    crawler.site_structure_handler_integrated = True

    logger.info("SiteStructureHandler đã được tích hợp thành công vào AdaptiveCrawler")

    return crawler
```
