# AdaptiveCrawler - C<PERSON><PERSON> cụ thu thập thông tin web linh hoạt

AdaptiveCrawler là một công cụ crawl thông minh có khả năng thích ứng với nhiều loại trang web khác nhau. Công cụ được thiết kế để thu thập thông tin một cách hiệu quả, xử lý các trang động, và trích xuất nội dung có cấu trúc.

## Tính năng chính

- **Thu thập thích ứng**: Tự động điều chỉnh chiến lược dựa trên loại trang web
- **Xử lý JavaScript**: Hỗ trợ crawl các trang sử dụng JavaScript với Playwright
- **Trích xuất thông minh**: Trích xuất nội dung chính, metadata và các liên kết
- **Crawl sâu**: <PERSON><PERSON><PERSON> năng thực hiện crawl theo độ sâu từ URL ban đầu
- **Tôn trọng robots.txt**: <PERSON><PERSON> thủ các quy tắc trong tệp robots.txt
- **Xử lý rate limiting**: Tránh quá tải server bằng cách điều chỉnh tốc độ crawl
- **Hỗ trợ proxy**: Khả năng sử dụng và xoay vòng proxy

## Cách sử dụng

### Khởi tạo crawler

```python
from deep_research_core.agents.adaptive_crawler_integration import AdaptiveCrawler

# Khởi tạo với các tùy chọn mặc định
crawler = AdaptiveCrawler()

# Khởi tạo với tùy chọn
crawler = AdaptiveCrawler(
    timeout=30,
    max_retries=3,
    use_playwright=True,
    respect_robots=True,
    delay=1.0
)
```

### Crawl một URL

```python
result = crawler._crawl_url("https://example.com")
print(f"Tiêu đề: {result.get('title')}")
print(f"Nội dung: {result.get('content')[:100]}...")
```

### Trích xuất nội dung từ URL

```python
content = crawler._extract_content_from_url("https://example.com")
print(f"Tiêu đề: {content.get('title')}")
print(f"Mô tả: {content.get('description')}")
```

### Trích xuất liên kết từ URL

```python
links = crawler._extract_links_from_url("https://example.com")
for link in links:
    print(f"URL: {link['url']}, Text: {link['text']}")
```

### Crawl sâu từ một URL

```python
deep_results = crawler._deep_crawl_with_adaptive_crawler(
    "https://example.com",
    max_depth=2,
    max_pages=10,
    same_domain_only=True
)
print(f"Số trang đã crawl: {deep_results['stats']['total_pages']}")
```

### Thực hiện nghiên cứu sâu về một chủ đề

```python
research_results = crawler.deep_research("trí tuệ nhân tạo", max_depth=2, max_pages=5)
```

## Tùy chọn cấu hình

| Tham số | Mô tả | Giá trị mặc định |
|---------|-------|-----------------|
| `timeout` | Thời gian chờ tối đa (giây) | 30 |
| `max_retries` | Số lần thử lại tối đa | 3 |
| `use_playwright` | Sử dụng Playwright để render JS | False |
| `proxy` | Proxy để sử dụng | None |
| `respect_robots` | Tôn trọng robots.txt | True |
| `delay` | Thời gian chờ giữa các request | 1.0 |

## Các phương thức chính

- `_crawl_url(url, **kwargs)`: Crawl một URL đơn lẻ
- `_crawl_urls(urls, **kwargs)`: Crawl nhiều URL cùng lúc
- `_extract_content_from_url(url, html=None, **kwargs)`: Trích xuất nội dung từ URL
- `_extract_links_from_url(url, html=None, **kwargs)`: Trích xuất liên kết từ URL
- `_extract_metadata_from_url(url, soup=None, html=None)`: Trích xuất metadata từ URL
- `_deep_crawl_with_adaptive_crawler(start_url, max_depth=2, max_pages=20, same_domain_only=True, **kwargs)`: Thực hiện crawl sâu
- `deep_research(query, **kwargs)`: Thực hiện nghiên cứu sâu về một chủ đề

## Các lớp và module phụ thuộc

- `UserAgentManager`: Quản lý user agent
- `PaginationHandler`: Xử lý phân trang
- `PlaywrightHandler`: Xử lý session Playwright
- `LanguageHandler`: Xử lý ngôn ngữ
- `FileProcessor`: Xử lý tệp
- `SiteStructureHandler`: Xử lý cấu trúc trang web

## Yêu cầu

- Python 3.7+
- requests
- beautifulsoup4
- playwright (tùy chọn, cho JavaScript rendering)

## Tích hợp với WebSearchAgentLocalMerged

AdaptiveCrawler được tích hợp chặt chẽ với `WebSearchAgentLocalMerged` để cung cấp khả năng tìm kiếm và nghiên cứu web toàn diện. Phương thức `deep_research` sử dụng agent tìm kiếm để lấy các URL liên quan, sau đó thực hiện crawl sâu trên mỗi URL để thu thập thông tin chi tiết.

## Cải tiến trong tương lai

- Hỗ trợ nhiều định dạng dữ liệu (PDF, JSON, XML)
- Cải thiện khả năng phát hiện CAPTCHA và xử lý
- Cơ chế lưu trữ và cache dữ liệu hiệu quả hơn
- Phân tích ngữ nghĩa nâng cao của nội dung đã thu thập
- Trích xuất nội dung đa phương tiện (hình ảnh, video, âm thanh) 