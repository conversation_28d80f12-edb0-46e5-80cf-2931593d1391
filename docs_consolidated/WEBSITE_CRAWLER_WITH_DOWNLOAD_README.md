# Website Crawler with File Download

Công cụ để crawl toàn bộ một website và tải xuống các file sử dụng AdaptiveCrawler từ module deepresearch.

## Tính năng

- Crawl toàn bộ một website từ một URL bắt đầu
- Giới hạn số trang tối đa và độ sâu tối đa
- Tùy chọn tôn trọng hoặc bỏ qua robots.txt
- Sử dụng Playwright để xử lý các trang web động với JavaScript
- Lưu kết quả dưới dạng Markdown và JSON
- Lọc các trang cùng tên miền với URL bắt đầu
- Trích xuất nội dung, tiêu đề, liên kết từ các trang web
- **Tải xuống các file** từ các liên kết trong quá trình crawl
- Ph<PERSON><PERSON> hiện các liên kết đến file dựa trên phần mở rộng và tham số URL
- Lư<PERSON> thông tin về các file đã tải xuống

## Cài đặt

Đảm bảo bạn đã cài đặt các thư viện cần thiết:

```bash
pip install requests beautifulsoup4 playwright
```

Nếu bạn muốn sử dụng Playwright, bạn cần cài đặt các trình duyệt:

```bash
playwright install
```

## Sử dụng

### Dòng lệnh

```bash
python website_crawler_with_download.py https://example.com --output results.md --download-files --download-dir downloads
```

### Các tham số

- `url`: URL bắt đầu (bắt buộc)
- `--output`, `-o`: Đường dẫn file để lưu kết quả (mặc định: crawl_results.md)
- `--download-files`, `-df`: Tải xuống các file (mặc định: False)
- `--download-dir`, `-dd`: Thư mục để lưu các file tải xuống (mặc định: downloads)
- `--max-pages`, `-p`: Số trang tối đa để crawl (mặc định: 100)
- `--max-depth`, `-d`: Độ sâu tối đa để crawl (mặc định: 3)
- `--timeout`, `-t`: Thời gian chờ tối đa cho mỗi request (giây) (mặc định: 30)
- `--respect-robots`, `-r`: Tôn trọng robots.txt (mặc định: False)
- `--no-playwright`, `-n`: Không sử dụng Playwright (mặc định: False)

### Ví dụ

1. Crawl một website và tải xuống các file:

```bash
python website_crawler_with_download.py https://example.com --download-files
```

2. Crawl một website với số trang tối đa là 50, độ sâu tối đa là 2 và tải xuống các file:

```bash
python website_crawler_with_download.py https://example.com --max-pages 50 --max-depth 2 --download-files
```

3. Crawl một website, tải xuống các file và lưu vào thư mục cụ thể:

```bash
python website_crawler_with_download.py https://example.com --download-files --download-dir my_downloads
```

4. Crawl một website, tôn trọng robots.txt và tải xuống các file:

```bash
python website_crawler_with_download.py https://example.com --respect-robots --download-files
```

## Kết quả

Kết quả sẽ được lưu vào hai file:

1. File Markdown (mặc định: crawl_results.md): Chứa thông tin về quá trình crawl, danh sách các file đã tải xuống và danh sách các trang đã crawl dưới dạng dễ đọc.
2. File JSON (tên file Markdown với phần mở rộng .json): Chứa dữ liệu thô dưới dạng JSON.

### Cấu trúc file Markdown

```
# Kết quả crawl website: https://example.com
Thời gian: 2023-05-15 12:34:56 - 2023-05-15 12:35:30
Thời gian crawl: 34.50 giây
Tổng số trang: 50
Số trang cùng tên miền: 45
Độ sâu tối đa: 3
Tôn trọng robots.txt: False
Sử dụng Playwright: True

## Các file đã tải xuống
Tổng số file đã tải xuống: 5

### 1. document.pdf
URL: https://example.com/documents/document.pdf
Đường dẫn: downloads/document.pdf
Loại nội dung: application/pdf
Kích thước: 1234567 bytes
Thời gian tải xuống: 2023-05-15 12:35:00

### 2. report.docx
...

## Danh sách các trang đã crawl

### 1. Trang chủ
URL: https://example.com/
Độ sâu: 0
Số ký tự nội dung: 1234
Số liên kết: 20
Nguồn: playwright

#### Nội dung (trích đoạn)
```
Nội dung trang web...
```

### 2. Giới thiệu
...
```

### Cấu trúc file JSON

```json
{
  "crawl_info": {
    "url": "https://example.com",
    "domain": "example.com",
    "start_time": "2023-05-15 12:34:56",
    "end_time": "2023-05-15 12:35:30",
    "duration": 34.5,
    "total_pages": 50,
    "same_domain_pages": 45,
    "max_pages": 100,
    "max_depth": 3,
    "respect_robots_txt": false,
    "use_playwright": true,
    "download_files": true,
    "total_downloaded_files": 5
  },
  "pages": [
    {
      "url": "https://example.com/",
      "title": "Trang chủ",
      "content": "Nội dung trang web...",
      "links": ["https://example.com/about", "https://example.com/contact", ...],
      "depth": 0,
      "source": "playwright"
    },
    ...
  ],
  "downloaded_files": [
    {
      "url": "https://example.com/documents/document.pdf",
      "file_name": "document.pdf",
      "file_path": "downloads/document.pdf",
      "content_type": "application/pdf",
      "size": 1234567,
      "download_time": "2023-05-15 12:35:00"
    },
    ...
  ]
}
```

## Sử dụng trong mã Python

Bạn cũng có thể sử dụng hàm `crawl_website` trong mã Python của bạn:

```python
from website_crawler_with_download import crawl_website

crawl_info = crawl_website(
    url="https://example.com",
    output_file="results.md",
    download_files=True,
    download_dir="downloads",
    max_pages=100,
    max_depth=3,
    timeout=30,
    respect_robots_txt=False,
    use_playwright=True
)

print(f"Đã crawl {crawl_info['total_pages']} trang trong {crawl_info['duration']:.2f} giây")
print(f"Đã tải xuống {crawl_info['total_downloaded_files']} file")
```

## Cách phát hiện file

Công cụ này phát hiện các liên kết đến file dựa trên:

1. **Phần mở rộng của URL**: Kiểm tra xem URL có kết thúc bằng các phần mở rộng file phổ biến như .pdf, .doc, .docx, .xls, .xlsx, v.v.
2. **Tham số query**: Kiểm tra xem URL có chứa các tham số như 'download' hoặc 'file' hay không.
3. **Content-Type**: Khi tải xuống, kiểm tra Content-Type của response để xác định loại file.

## Lưu ý

- Crawl một website lớn có thể mất nhiều thời gian và tài nguyên.
- Đảm bảo bạn có quyền crawl website đó và không vi phạm các điều khoản dịch vụ.
- Sử dụng tùy chọn `--respect-robots` để tôn trọng robots.txt nếu bạn không chắc chắn.
- Sử dụng tùy chọn `--max-pages` và `--max-depth` để giới hạn số lượng trang và độ sâu crawl.
- Sử dụng Playwright để xử lý các trang web động với JavaScript, nhưng nó có thể chậm hơn so với requests.
- Tải xuống các file có thể tốn nhiều băng thông và dung lượng lưu trữ, đặc biệt là với các website có nhiều file lớn.
