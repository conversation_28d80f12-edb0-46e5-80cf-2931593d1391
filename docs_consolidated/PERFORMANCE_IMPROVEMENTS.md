# Cải thiện hiệu suất cho Deep Research Core

Tài liệu này mô tả các cải tiến hiệu suất đã được thực hiện cho Deep Research Core, đặc biệt là cho module Crawlee và xử lý web search.

## 1. Tối ưu hóa hiệu suất khi sử dụng Crawlee với số lượng lớn trang web

### 1.1. Quản lý tài nguyên thông minh

Đã thêm lớp `ResourceManager` để quản lý tài nguyên hệ thống khi crawl nhiều trang web:

- Giám sát và giới hạn sử dụng bộ nhớ
- Giám sát và giới hạn sử dụng CPU
- Tự động điều chỉnh số lượng tiến trình đồng thời
- Phát hiện và xử lý rò rỉ tài nguyên

```python
# Khởi tạo resource manager
resource_manager = ResourceManager(
    memory_limit_mb=1024,  # Giới hạn bộ nhớ 1GB
    max_concurrent_processes=5,  # Tối đa 5 tiến trình đồng thời
    cpu_threshold=0.8  # Giảm số tiến trình khi CPU > 80%
)
```

### 1.2. Xử lý batch thông minh

Đã thêm lớp `MemoryOptimizedCrawler` để xử lý batch URL, giúp giảm sử dụng bộ nhớ và tăng hiệu suất:

- Xử lý batch để giảm sử dụng bộ nhớ
- Tự động dọn dẹp bộ nhớ sau mỗi batch
- Tự động điều chỉnh kích thước batch dựa trên tài nguyên
- Xử lý lỗi và tự động thử lại

```python
# Sử dụng MemoryOptimizedCrawler
crawler = MemoryOptimizedCrawler(
    resource_manager=resource_manager,
    batch_size=10,  # Số URL tối đa trong một batch
    max_retries=3,  # Số lần thử lại tối đa
    adaptive_batch_size=True  # Tự động điều chỉnh kích thước batch
)

# Crawl URLs
results = crawler.crawl_urls(urls=urls, max_depth=2)
```

### 1.3. Cải thiện xử lý đồng thời và giới hạn tốc độ

- Sử dụng `subprocess.Popen` thay vì `subprocess.run` để kiểm soát tốt hơn các tiến trình
- Thêm cơ chế giới hạn tốc độ thông minh để tránh bị chặn bởi các trang web
- Thêm cơ chế xử lý timeout và kill tiến trình khi cần thiết

### 1.4. Cải thiện xử lý lỗi và khôi phục

- Thêm cơ chế xử lý lỗi chi tiết hơn
- Thêm cơ chế khôi phục thông minh khi gặp lỗi
- Thêm cơ chế ghi log chi tiết hơn để dễ dàng debug

## 2. Cải thiện hiệu suất khi xử lý lượng lớn dữ liệu cho RAG

### 2.1. Tối ưu hóa bộ nhớ

- Sử dụng `gc.collect()` để dọn dẹp bộ nhớ định kỳ
- Sử dụng `--max-old-space-size` để giới hạn bộ nhớ cho Node.js
- Sử dụng tempfile để tránh rò rỉ bộ nhớ

### 2.2. Xử lý đồng thời thông minh

- Sử dụng threading để xử lý đồng thời nhiều tác vụ
- Sử dụng lock để đảm bảo thread safety
- Sử dụng Event để đồng bộ hóa giữa các thread

## 3. Thêm caching thông minh cho các phương thức tìm kiếm mới

### 3.1. Cải thiện cơ chế cache

- Thêm cơ chế cache thông minh dựa trên nội dung
- Thêm cơ chế cache thông minh dựa trên ngữ cảnh
- Thêm cơ chế cache thông minh dựa trên thời gian

## Hướng dẫn sử dụng

### Sử dụng tính năng tối ưu hóa bộ nhớ

```python
from deep_research_core.agents.advanced_crawlee import search_with_advanced_crawlee

# Tìm kiếm với tối ưu hóa bộ nhớ
results = search_with_advanced_crawlee(
    query="Kinh tế Việt Nam 2023",
    num_results=20,
    max_depth=2,
    max_pages=30,
    timeout=60,
    detailed_scraping=True,
    language="vi",
    use_memory_optimization=True,  # Bật tính năng tối ưu hóa bộ nhớ
    batch_size=5  # Kích thước batch
)
```

### Sử dụng ResourceManager và MemoryOptimizedCrawler trực tiếp

```python
from deep_research_core.agents.advanced_crawlee import ResourceManager, MemoryOptimizedCrawler

# Khởi tạo ResourceManager
resource_manager = ResourceManager(
    memory_limit_mb=2048,  # 2GB
    max_concurrent_processes=8,
    cpu_threshold=0.7
)

# Khởi tạo MemoryOptimizedCrawler
crawler = MemoryOptimizedCrawler(
    resource_manager=resource_manager,
    batch_size=10,
    max_retries=5,
    timeout=120,
    adaptive_batch_size=True
)

# Crawl URLs
results = crawler.crawl_urls(
    urls=urls,
    max_depth=3,
    detailed_scraping=True,
    proxy="http://proxy.example.com:8080"
)
```

## Yêu cầu hệ thống

- Python 3.8+
- Node.js 14+
- Playwright
- psutil
- Các thư viện khác trong requirements.txt

## Cài đặt

```bash
pip install -r requirements.txt
playwright install chromium
```

## Tài liệu API

Xem tài liệu API đầy đủ trong thư mục `docs/`.
