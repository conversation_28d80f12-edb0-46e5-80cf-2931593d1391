# Cải thiện WebSearchAgentLocal (Phần 2)

Tiếp tục từ phần 1, tài liệu này mô tả các cải tiến cần thiết cho WebSearchAgentLocal để nâng cao khả năng tìm kiếm mà không cần sử dụng API bên ngoài.

## 4. Triển khai đầy đủ QuestionComplexityEvaluator

### Mô tả
Triển khai đầy đủ QuestionComplexityEvaluator để đánh giá độ phức tạp của câu hỏi và điều chỉnh chiến lược tìm kiếm phù hợp.

### Nhiệm vụ
1. **Tích hợp QuestionComplexityEvaluator vào phương thức search**
   - Sử dụng QuestionComplexityEvaluator để đánh giá độ phức tạp của câu hỏi
   - Điều chỉnh chiến lược tìm kiếm dựa trên độ phức tạp

2. **<PERSON><PERSON><PERSON> khai phương thức evaluate_question_complexity**
   - <PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON><PERSON> thức `evaluate_question_complexity()` để đánh giá độ phức tạp của câu hỏi
   - Sử dụng các tiêu chí đánh giá độ phức tạp

### Triển khai

```python
def evaluate_question_complexity(self, query: str) -> Dict[str, Any]:
    """
    Đánh giá độ phức tạp của câu hỏi.

    Args:
        query: Câu hỏi cần đánh giá

    Returns:
        Từ điển chứa thông tin đánh giá
    """
    if not hasattr(self, 'question_complexity_evaluator'):
        # Trả về đánh giá mặc định nếu không có evaluator
        return {
            "complexity_level": "medium",
            "complexity_score": 0.5,
            "recommended_strategy": {
                "search_method": "auto",
                "max_depth": 2,
                "max_pages": 3
            }
        }

    try:
        # Sử dụng QuestionComplexityEvaluator để đánh giá
        evaluation = self.question_complexity_evaluator.evaluate_complexity(query)
        return evaluation
    except Exception as e:
        logger.error(f"Error evaluating question complexity: {str(e)}")
        # Trả về đánh giá mặc định nếu có lỗi
        return {
            "complexity_level": "medium",
            "complexity_score": 0.5,
            "recommended_strategy": {
                "search_method": "auto",
                "max_depth": 2,
                "max_pages": 3
            }
        }
```

Trong phương thức `search()`, thêm đoạn code sau để sử dụng QuestionComplexityEvaluator:

```python
# Đánh giá độ phức tạp của câu hỏi nếu được yêu cầu
question_evaluation = None
if evaluate_question and hasattr(self, 'question_complexity_evaluator'):
    question_evaluation = self.evaluate_question_complexity(query)
    
    # Điều chỉnh chiến lược tìm kiếm dựa trên độ phức tạp
    if question_evaluation:
        complexity_level = question_evaluation.get("complexity_level")
        recommended_strategy = question_evaluation.get("recommended_strategy", {})
        
        # Điều chỉnh phương thức tìm kiếm
        if "search_method" in recommended_strategy and not method:
            search_method = recommended_strategy["search_method"]
        
        # Điều chỉnh các tham số khác
        if "max_depth" in recommended_strategy:
            kwargs["max_depth"] = recommended_strategy["max_depth"]
        
        if "max_pages" in recommended_strategy:
            kwargs["max_pages"] = recommended_strategy["max_pages"]
        
        logger.info(f"Adjusted search strategy based on question complexity: {complexity_level}")
```

## 5. Triển khai đầy đủ AnswerQualityEvaluator

### Mô tả
Triển khai đầy đủ AnswerQualityEvaluator để đánh giá chất lượng câu trả lời và quyết định có cần tìm kiếm thêm hay không.

### Nhiệm vụ
1. **Tích hợp AnswerQualityEvaluator vào phương thức search**
   - Sử dụng AnswerQualityEvaluator để đánh giá chất lượng câu trả lời
   - Quyết định có cần tìm kiếm thêm hay không dựa trên chất lượng câu trả lời

2. **Triển khai phương thức evaluate_answer_quality**
   - Tạo phương thức `evaluate_answer_quality()` để đánh giá chất lượng câu trả lời
   - Sử dụng các tiêu chí đánh giá chất lượng

### Triển khai

```python
def evaluate_answer_quality(
    self,
    query: str,
    answer: str,
    documents: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Đánh giá chất lượng câu trả lời.

    Args:
        query: Câu hỏi
        answer: Câu trả lời
        documents: Danh sách tài liệu được sử dụng

    Returns:
        Từ điển chứa thông tin đánh giá
    """
    if not hasattr(self, 'answer_quality_evaluator'):
        # Trả về đánh giá mặc định nếu không có evaluator
        return {
            "overall_score": 7.0,
            "quality_level": "good",
            "need_more_search": False,
            "criteria_scores": {
                "relevance": 7.0,
                "completeness": 7.0,
                "accuracy": 7.0
            }
        }

    try:
        # Sử dụng AnswerQualityEvaluator để đánh giá
        evaluation = self.answer_quality_evaluator.evaluate_answer(
            question=query,
            answer=answer,
            documents=documents
        )
        return evaluation
    except Exception as e:
        logger.error(f"Error evaluating answer quality: {str(e)}")
        # Trả về đánh giá mặc định nếu có lỗi
        return {
            "overall_score": 7.0,
            "quality_level": "good",
            "need_more_search": False,
            "criteria_scores": {
                "relevance": 7.0,
                "completeness": 7.0,
                "accuracy": 7.0
            }
        }
```

Trong phương thức `search()`, thêm đoạn code sau để sử dụng AnswerQualityEvaluator:

```python
# Đánh giá chất lượng câu trả lời nếu được yêu cầu
if evaluate_answer and results.get("success") and results.get("results"):
    try:
        # Tạo câu trả lời tổng hợp từ kết quả tìm kiếm
        answer = ""
        if "optimized_text" in results:
            answer = results["optimized_text"]
        else:
            for result in results.get("results", []):
                if "content" in result and result["content"]:
                    answer += f"\n\n{result.get('title', '')}\n{result['content'][:500]}..."

        # Đánh giá chất lượng câu trả lời
        answer_evaluation = self.evaluate_answer_quality(
            query=original_query,
            answer=answer,
            documents=results.get("results", []),
        )

        # Thêm thông tin đánh giá vào kết quả
        results["answer_evaluation"] = answer_evaluation

        # Nếu cần tìm kiếm thêm và auto_deep_crawl được bật
        if answer_evaluation.get("need_more_search", False) and auto_deep_crawl:
            logger.info("Answer quality is low, performing deep crawl")
            
            # Lấy URL từ kết quả tìm kiếm
            urls = [result.get("url") for result in results.get("results", []) if "url" in result]
            
            if urls:
                # Xác định mức độ phức tạp cho crawl
                complexity_level = "medium"
                if question_evaluation and "complexity_level" in question_evaluation:
                    complexity_level = question_evaluation["complexity_level"]

                # Crawl các URL với AdaptiveCrawler
                deep_results = self._perform_deep_crawl(
                    query=original_query,
                    urls=urls[:5],  # Giới hạn số URL để tránh quá tải
                    complexity_level=complexity_level,
                    **kwargs
                )
                
                # Kết hợp kết quả
                if deep_results.get("success") and deep_results.get("results"):
                    # Thêm kết quả mới vào kết quả hiện tại
                    results["results"].extend(deep_results.get("results", []))
                    
                    # Đánh dấu đã thực hiện deep crawl
                    results["deep_crawled"] = True
                    results["deep_crawl_stats"] = deep_results.get("stats", {})
    except Exception as e:
        logger.error(f"Error evaluating answer quality: {str(e)}")
```

## 6. Triển khai đầy đủ AdaptiveCrawler

### Mô tả
Triển khai đầy đủ AdaptiveCrawler để crawl các trang web với chiến lược thích ứng dựa trên độ phức tạp của câu hỏi và chất lượng câu trả lời.

### Nhiệm vụ
1. **Tích hợp AdaptiveCrawler vào phương thức search**
   - Sử dụng AdaptiveCrawler để crawl các trang web
   - Điều chỉnh chiến lược crawl dựa trên độ phức tạp của câu hỏi

2. **Triển khai phương thức _perform_deep_crawl**
   - Tạo phương thức `_perform_deep_crawl()` để thực hiện deep crawl
   - Sử dụng AdaptiveCrawler để crawl các trang web

### Triển khai

```python
def _perform_deep_crawl(
    self,
    query: str,
    urls: List[str],
    complexity_level: str = "medium",
    **kwargs
) -> Dict[str, Any]:
    """
    Thực hiện deep crawl với AdaptiveCrawler.

    Args:
        query: Câu hỏi
        urls: Danh sách URL cần crawl
        complexity_level: Mức độ phức tạp của câu hỏi
        **kwargs: Các tham số bổ sung

    Returns:
        Từ điển chứa kết quả crawl
    """
    if not hasattr(self, 'adaptive_crawler'):
        logger.warning("AdaptiveCrawler not available, skipping deep crawl")
        return {
            "success": False,
            "error": "AdaptiveCrawler not available",
            "results": []
        }

    try:
        # Lấy các tham số crawl
        max_depth = kwargs.get("max_depth", 2)
        max_pages = kwargs.get("max_pages", 3)
        timeout = kwargs.get("timeout", 30)
        
        # Điều chỉnh tham số dựa trên mức độ phức tạp
        if complexity_level == "high":
            max_depth = max(max_depth, 3)
            max_pages = max(max_pages, 5)
        elif complexity_level == "low":
            max_depth = min(max_depth, 1)
            max_pages = min(max_pages, 2)
        
        # Thực hiện crawl với AdaptiveCrawler
        crawl_results = self.adaptive_crawler.crawl(
            urls=urls,
            complexity_level=complexity_level,
            max_depth=max_depth,
            max_pages=max_pages,
            timeout=timeout,
            detailed_scraping=True
        )
        
        # Xử lý kết quả crawl
        if crawl_results.get("success") and crawl_results.get("results"):
            # Tối ưu hóa kết quả cho LLM nếu cần
            if kwargs.get("optimize_for_llm", False):
                from .web_search_agent_local_helpers import optimize_results_for_llm
                crawl_results = optimize_results_for_llm(
                    crawl_results,
                    max_content_length=kwargs.get("max_content_length", 5000)
                )
            
            return crawl_results
        else:
            logger.warning("Deep crawl returned no results")
            return {
                "success": False,
                "error": "Deep crawl returned no results",
                "results": []
            }
    except Exception as e:
        logger.error(f"Error performing deep crawl: {str(e)}")
        return {
            "success": False,
            "error": f"Error performing deep crawl: {str(e)}",
            "results": []
        }
```
