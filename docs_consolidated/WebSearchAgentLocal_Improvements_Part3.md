# Cải thiện WebSearchAgentLocal (Phần 3)

Tiế<PERSON> tục từ phần 2, tài liệu này mô tả các cải tiến cần thiết cho WebSearchAgentLocal để nâng cao khả năng tìm kiếm mà không cần sử dụng API bên ngoài.

## 7. <PERSON><PERSON><PERSON> thiện EnhancedWebSearchCache

### Mô tả
Triển khai đầy đủ EnhancedWebSearchCache để cải thiện hiệu suất cache và giảm số lượng yêu cầu tìm kiếm.

### Nhiệm vụ
1. **Tích hợp EnhancedWebSearchCache vào phương thức search**
   - Sử dụng EnhancedWebSearchCache để cache kết quả tìm kiếm
   - Kiểm tra cache trước khi thực hiện tìm kiếm

2. **<PERSON><PERSON><PERSON> khai các phương thức cache**
   - Tạ<PERSON> phương thức `_get_from_cache()` để lấy kết quả từ cache
   - T<PERSON><PERSON> ph<PERSON>ng th<PERSON>c `_save_to_cache()` để lưu kết quả vào cache

### Triển khai

```python
def _get_from_cache(
    self,
    query: str,
    search_method: str,
    language: str,
    **kwargs
) -> Optional[Dict[str, Any]]:
    """
    Lấy kết quả từ cache.

    Args:
        query: Truy vấn tìm kiếm
        search_method: Phương thức tìm kiếm
        language: Ngôn ngữ tìm kiếm
        **kwargs: Các tham số bổ sung

    Returns:
        Kết quả tìm kiếm từ cache hoặc None nếu không tìm thấy
    """
    if not hasattr(self, 'smart_cache') or not self.use_smart_cache:
        return None

    try:
        # Tạo cache key
        cache_key = f"{search_method}:{query}:{language}"
        
        # Thêm các tham số quan trọng vào cache key
        if "num_results" in kwargs:
            cache_key += f":num_results={kwargs['num_results']}"
        
        if "get_content" in kwargs:
            cache_key += f":get_content={kwargs['get_content']}"
        
        # Lấy kết quả từ cache
        cached_result = self.smart_cache.get(
            key=cache_key,
            use_semantic=False  # Không sử dụng tìm kiếm ngữ nghĩa để đơn giản hóa
        )
        
        if cached_result:
            logger.info(f"Cache hit for query: {query}")
            return cached_result
        
        return None
    except Exception as e:
        logger.error(f"Error getting from cache: {str(e)}")
        return None

def _save_to_cache(
    self,
    query: str,
    search_method: str,
    language: str,
    result: Dict[str, Any],
    **kwargs
) -> bool:
    """
    Lưu kết quả vào cache.

    Args:
        query: Truy vấn tìm kiếm
        search_method: Phương thức tìm kiếm
        language: Ngôn ngữ tìm kiếm
        result: Kết quả tìm kiếm
        **kwargs: Các tham số bổ sung

    Returns:
        True nếu lưu thành công, False nếu không
    """
    if not hasattr(self, 'smart_cache') or not self.use_smart_cache:
        return False

    try:
        # Tạo cache key
        cache_key = f"{search_method}:{query}:{language}"
        
        # Thêm các tham số quan trọng vào cache key
        if "num_results" in kwargs:
            cache_key += f":num_results={kwargs['num_results']}"
        
        if "get_content" in kwargs:
            cache_key += f":get_content={kwargs['get_content']}"
        
        # Lưu kết quả vào cache
        self.smart_cache.set(
            key=cache_key,
            value=result,
            ttl=None,  # Sử dụng TTL mặc định
            metadata={
                "query": query,
                "search_method": search_method,
                "language": language,
                "timestamp": time.time()
            }
        )
        
        logger.info(f"Saved to cache: {query}")
        return True
    except Exception as e:
        logger.error(f"Error saving to cache: {str(e)}")
        return False
```

Trong phương thức `search()`, thêm đoạn code sau để sử dụng EnhancedWebSearchCache:

```python
# Kiểm tra cache nếu không force_refresh
if not force_refresh:
    cached_result = self._get_from_cache(
        query=query,
        search_method=search_method,
        language=language,
        num_results=num_results,
        get_content=get_content,
        **kwargs
    )
    
    if cached_result:
        # Thêm thông tin cache vào kết quả
        cached_result["from_cache"] = True
        
        # Thêm thông tin đánh giá câu hỏi nếu có
        if question_evaluation:
            cached_result["question_evaluation"] = question_evaluation
        
        return cached_result

# Sau khi thực hiện tìm kiếm thành công
if results.get("success"):
    # Lưu kết quả vào cache
    self._save_to_cache(
        query=query,
        search_method=search_method,
        language=language,
        result=results,
        num_results=num_results,
        get_content=get_content,
        **kwargs
    )
```

## 8. Hệ thống Plugin

### Mô tả
Thêm hệ thống plugin để mở rộng tính năng của WebSearchAgentLocal mà không cần sửa đổi code gốc.

### Nhiệm vụ
1. **Thêm cơ chế quản lý plugin**
   - Tạo phương thức `register_plugin()` để đăng ký plugin
   - Tạo phương thức `call_plugin_hook()` để gọi các hook của plugin

2. **Tạo giao diện plugin**
   - Tạo lớp `PluginInterface` để định nghĩa giao diện cho plugin
   - Định nghĩa các hook chuẩn cho plugin

### Triển khai

```python
class PluginInterface:
    """
    Giao diện cho plugin.
    
    Tất cả các plugin phải kế thừa từ lớp này.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo plugin.
        
        Args:
            config: Cấu hình plugin
        """
        self.config = config or {}
        self.name = self.__class__.__name__
        self.version = "1.0.0"
        self.description = "Plugin cho WebSearchAgentLocal"
        self.author = "Unknown"
    
    def initialize(self, agent: Any) -> bool:
        """
        Khởi tạo plugin với agent.
        
        Args:
            agent: WebSearchAgentLocal
            
        Returns:
            bool: True nếu thành công, False nếu thất bại
        """
        return True
    
    def pre_search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Hook trước khi tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        return {"query": query, "modified": False}
    
    def post_search(self, query: str, results: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook sau khi tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            results: Kết quả tìm kiếm
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        return results
    
    def pre_extract_content(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Hook trước khi trích xuất nội dung.
        
        Args:
            url: URL cần trích xuất
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        return {"url": url, "modified": False}
    
    def post_extract_content(self, url: str, content: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Hook sau khi trích xuất nội dung.
        
        Args:
            url: URL đã trích xuất
            content: Nội dung đã trích xuất
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        return content
```

Thêm các phương thức quản lý plugin vào lớp WebSearchAgentLocal:

```python
def register_plugin(self, plugin: PluginInterface) -> bool:
    """
    Đăng ký plugin.
    
    Args:
        plugin: Plugin cần đăng ký
        
    Returns:
        bool: True nếu thành công, False nếu thất bại
    """
    if not hasattr(self, 'plugins'):
        self.plugins = {}
    
    try:
        # Khởi tạo plugin với agent
        if plugin.initialize(self):
            # Đăng ký plugin
            self.plugins[plugin.name] = plugin
            logger.info(f"Registered plugin: {plugin.name} v{plugin.version}")
            return True
        else:
            logger.warning(f"Failed to initialize plugin: {plugin.name}")
            return False
    except Exception as e:
        logger.error(f"Error registering plugin: {str(e)}")
        return False

def call_plugin_hook(self, hook_name: str, *args, **kwargs) -> Any:
    """
    Gọi hook của plugin.
    
    Args:
        hook_name: Tên hook
        *args: Tham số vị trí
        **kwargs: Tham số từ khóa
        
    Returns:
        Any: Kết quả từ hook
    """
    if not hasattr(self, 'plugins'):
        return args[0] if args else None
    
    result = args[0] if args else None
    
    for plugin_name, plugin in self.plugins.items():
        try:
            if hasattr(plugin, hook_name):
                hook_method = getattr(plugin, hook_name)
                result = hook_method(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error calling plugin hook {hook_name} on {plugin_name}: {str(e)}")
    
    return result
```

Trong phương thức `search()`, thêm đoạn code sau để sử dụng plugin:

```python
# Gọi hook pre_search của plugin
if hasattr(self, 'plugins'):
    modified_query = self.call_plugin_hook("pre_search", query, **kwargs)
    if isinstance(modified_query, dict) and modified_query.get("modified", False):
        query = modified_query.get("query", query)
        logger.info(f"Query modified by plugin: {query}")

# Sau khi thực hiện tìm kiếm thành công
if results.get("success") and hasattr(self, 'plugins'):
    # Gọi hook post_search của plugin
    modified_results = self.call_plugin_hook("post_search", query, results, **kwargs)
    if modified_results:
        results = modified_results
```

## Ví dụ Plugin

Dưới đây là một ví dụ về plugin đơn giản để lọc kết quả tìm kiếm:

```python
class ContentFilterPlugin(PluginInterface):
    """
    Plugin lọc nội dung không phù hợp.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Khởi tạo plugin.
        
        Args:
            config: Cấu hình plugin
        """
        super().__init__(config)
        self.name = "ContentFilterPlugin"
        self.version = "1.0.0"
        self.description = "Lọc nội dung không phù hợp từ kết quả tìm kiếm"
        self.author = "Your Name"
        
        # Danh sách từ khóa cần lọc
        self.filter_keywords = self.config.get("filter_keywords", [
            "adult", "xxx", "gambling", "bet", "casino"
        ])
    
    def post_search(self, query: str, results: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Lọc kết quả tìm kiếm.
        
        Args:
            query: Truy vấn tìm kiếm
            results: Kết quả tìm kiếm
            **kwargs: Tham số khác
            
        Returns:
            Dict[str, Any]: Kết quả đã lọc
        """
        if not results.get("success") or not results.get("results"):
            return results
        
        # Lọc kết quả
        filtered_results = []
        for result in results.get("results", []):
            # Kiểm tra tiêu đề và snippet
            title = result.get("title", "").lower()
            snippet = result.get("snippet", "").lower()
            url = result.get("url", "").lower()
            
            # Kiểm tra từ khóa cần lọc
            should_filter = False
            for keyword in self.filter_keywords:
                if keyword in title or keyword in snippet or keyword in url:
                    should_filter = True
                    break
            
            # Thêm vào kết quả nếu không cần lọc
            if not should_filter:
                filtered_results.append(result)
        
        # Cập nhật kết quả
        results["results"] = filtered_results
        results["filtered"] = True
        results["original_count"] = len(results.get("results", []))
        results["filtered_count"] = len(filtered_results)
        
        return results
```
