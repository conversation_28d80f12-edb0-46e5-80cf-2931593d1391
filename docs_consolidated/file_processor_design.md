# Thiết kế module FileProcessor dùng chung

Dựa trên phân tích các module xử lý file hiện tại trong cả WebSearchAgentLocal và AdaptiveCrawler, tôi đề xuất thiết kế module FileProcessor dùng chung như sau:

## Cấu trúc module

```python
import os
import re
import time
import logging
import mimetypes
import hashlib
import urllib.request
import urllib.parse
import tempfile
import shutil
from typing import Dict, List, Optional, Any, Tuple, Union, BinaryIO, Set
from urllib.parse import urlparse, urljoin
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

# Kiểm tra các thư viện phụ thuộc
try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    logger.warning("PyPDF2 không khả dụng. Không thể xử lý file PDF.")

try:
    import docx
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    logger.warning("python-docx không khả dụng. Không thể xử lý file DOCX.")

try:
    from openpyxl import load_workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    logger.warning("openpyxl không khả dụng. Không thể xử lý file XLSX.")

try:
    from pptx import Presentation
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False
    logger.warning("python-pptx không khả dụng. Không thể xử lý file PPTX.")

try:
    from playwright.sync_api import sync_playwright, Page, Browser, BrowserContext, TimeoutError as PlaywrightTimeoutError
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logger.warning("Playwright không khả dụng. Sẽ sử dụng phương thức tải xuống thay thế.")

class FileProcessor:
    """
    Module xử lý file dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """

    # Các phần mở rộng file được hỗ trợ
    SUPPORTED_EXTENSIONS = {
        # Văn bản
        "pdf": "application/pdf",
        "doc": "application/msword",
        "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "txt": "text/plain",
        "rtf": "application/rtf",
        "md": "text/markdown",
        "markdown": "text/markdown",

        # Bảng tính
        "xls": "application/vnd.ms-excel",
        "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "csv": "text/csv",

        # Trình chiếu
        "ppt": "application/vnd.ms-powerpoint",
        "pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",

        # Dữ liệu
        "json": "application/json",
        "xml": "application/xml",
        "yaml": "application/yaml",
        "yml": "application/yaml",

        # Nén
        "zip": "application/zip",
        "rar": "application/x-rar-compressed",
        "7z": "application/x-7z-compressed",
        "tar": "application/x-tar",
        "gz": "application/gzip",

        # Hình ảnh
        "jpg": "image/jpeg",
        "jpeg": "image/jpeg",
        "png": "image/png",
        "gif": "image/gif",
        "bmp": "image/bmp",
        "svg": "image/svg+xml",

        # Âm thanh
        "mp3": "audio/mpeg",
        "wav": "audio/wav",
        "ogg": "audio/ogg",

        # Video
        "mp4": "video/mp4",
        "avi": "video/x-msvideo",
        "mkv": "video/x-matroska",
        "webm": "video/webm",
    }

    def __init__(
        self,
        download_dir: Optional[str] = None,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        allowed_extensions: Optional[List[str]] = None,
        allowed_mime_types: Optional[List[str]] = None,
        organize_by_type: bool = True,
        skip_existing: bool = True,
        verify_ssl: bool = True,
        timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        max_workers: int = 5,
        use_playwright: bool = True,
        use_user_agent_manager: bool = False,
        user_agent_manager: Optional[Any] = None,
        cache_enabled: bool = True,
        cache_ttl: int = 3600 * 24,  # 1 day
        cache_size: int = 1000,
        verbose: bool = False,
        **kwargs
    ):
        """
        Khởi tạo FileProcessor.

        Args:
            download_dir: Thư mục tải xuống
            max_file_size: Kích thước tối đa của file (bytes)
            allowed_extensions: Danh sách phần mở rộng được phép
            allowed_mime_types: Danh sách kiểu MIME được phép
            organize_by_type: Tổ chức file theo loại
            skip_existing: Bỏ qua file đã tồn tại
            verify_ssl: Xác thực chứng chỉ SSL
            timeout: Thời gian chờ tối đa (giây)
            max_retries: Số lần thử lại tối đa
            retry_delay: Thời gian chờ giữa các lần thử lại (giây)
            max_workers: Số lượng worker tối đa
            use_playwright: Sử dụng Playwright để tải xuống
            use_user_agent_manager: Sử dụng UserAgentManager
            user_agent_manager: Đối tượng UserAgentManager
            cache_enabled: Bật cache
            cache_ttl: Thời gian sống của cache (giây)
            cache_size: Kích thước cache
            verbose: Ghi log chi tiết
            **kwargs: Các tham số bổ sung
        """
        # Khởi tạo các thuộc tính
        self.download_dir = download_dir or os.path.join(os.getcwd(), "downloads")
        self.max_file_size = max_file_size
        self.allowed_extensions = allowed_extensions or list(self.SUPPORTED_EXTENSIONS.keys())
        self.allowed_mime_types = allowed_mime_types or list(self.SUPPORTED_EXTENSIONS.values())
        self.organize_by_type = organize_by_type
        self.skip_existing = skip_existing
        self.verify_ssl = verify_ssl
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.max_workers = max_workers
        self.use_playwright = use_playwright and PLAYWRIGHT_AVAILABLE
        self.use_user_agent_manager = use_user_agent_manager
        self.user_agent_manager = user_agent_manager
        self.cache_enabled = cache_enabled
        self.cache_ttl = cache_ttl
        self.cache_size = cache_size
        self.verbose = verbose

        # Khởi tạo các thuộc tính bổ sung
        self.stats = {
            "downloads": 0,
            "successful_downloads": 0,
            "failed_downloads": 0,
            "skipped_downloads": 0,
            "total_download_size": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "extraction_success": 0,
            "extraction_failure": 0,
        }

        # Khởi tạo cache
        self.cache = {}

        # Khởi tạo executor
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)

        # Khởi tạo Playwright
        self.playwright = None
        self.browser = None

        # Tạo thư mục tải xuống nếu chưa tồn tại
        os.makedirs(self.download_dir, exist_ok=True)

        # Khởi tạo document extractor
        self._initialize_document_extractor()

        if self.verbose:
            logger.info(f"FileProcessor đã được khởi tạo với thư mục tải xuống: {self.download_dir}")
```

## Các phương thức chính

1. **download_file(url, output_path=None, use_playwright=None)**: Tải xuống file từ URL
2. **process_file(url, output_path=None, force_refresh=False, extract_text=True, extract_metadata=True, ...)**: Tải xuống và phân tích file
3. **is_file_url(url)**: Kiểm tra xem URL có phải là file không
4. **extract_file_content(file_path, mime_type=None)**: Trích xuất nội dung từ file
5. **extract_files_from_html(url, html_content)**: Trích xuất danh sách file từ nội dung HTML
6. **batch_download_files(urls, output_dir=None)**: Tải xuống nhiều file cùng lúc
7. **get_file_info(url)**: Lấy thông tin về file từ URL
8. **get_file_stats()**: Lấy thống kê về việc tải xuống và xử lý file

## Các phương thức hỗ trợ

1. **_initialize_document_extractor()**: Khởi tạo document extractor
2. **_initialize_playwright()**: Khởi tạo Playwright
3. **_download_with_playwright(url, output_path)**: Tải xuống file bằng Playwright
4. **_download_with_urllib(url, output_path)**: Tải xuống file bằng urllib
5. **_extract_text_from_pdf(file_path)**: Trích xuất văn bản từ file PDF
6. **_extract_text_from_docx(file_path)**: Trích xuất văn bản từ file DOCX
7. **_extract_text_from_xlsx(file_path)**: Trích xuất văn bản từ file XLSX
8. **_extract_text_from_pptx(file_path)**: Trích xuất văn bản từ file PPTX
9. **_extract_text_from_text(file_path)**: Trích xuất văn bản từ file văn bản
10. **_extract_metadata(file_path, mime_type)**: Trích xuất metadata từ file
11. **_get_mime_type(file_path)**: Lấy kiểu MIME của file
12. **_get_file_extension(file_path)**: Lấy phần mở rộng của file
13. **_get_file_name_from_url(url)**: Lấy tên file từ URL
14. **_get_user_agent()**: Lấy User-Agent
15. **_get_cache_key(url)**: Lấy khóa cache
16. **_get_from_cache(key)**: Lấy kết quả từ cache
17. **_save_to_cache(key, result)**: Lưu kết quả vào cache
18. **_clean_cache()**: Dọn dẹp cache

## Các tính năng đặc biệt

1. **Hỗ trợ nhiều định dạng file**: Hỗ trợ nhiều định dạng file như PDF, DOCX, XLSX, PPTX, TXT, CSV, JSON, XML, v.v.
2. **Tải xuống bằng Playwright**: Sử dụng Playwright để tải xuống file, hỗ trợ JavaScript và xử lý CAPTCHA
3. **Cache kết quả**: Lưu cache kết quả để tránh tải xuống và phân tích lại
4. **Xử lý đồng thời**: Sử dụng ThreadPoolExecutor để xử lý nhiều file cùng lúc
5. **Tổ chức file theo loại**: Tổ chức file theo loại để dễ quản lý
6. **Trích xuất file từ HTML**: Trích xuất danh sách file từ nội dung HTML
7. **Tích hợp với UserAgentManager**: Sử dụng UserAgentManager để xoay vòng User-Agent
8. **Thống kê**: Theo dõi thống kê về việc tải xuống và xử lý file

## Cách tích hợp

### Tích hợp vào WebSearchAgentLocal

```python
from ..utils.shared.file_processor import FileProcessor
from ..utils.shared.integration import integrate_file_processor

def integrate_file_processor(agent, config=None):
    """
    Tích hợp FileProcessor vào WebSearchAgentLocal.
    """
    # Cấu hình mặc định
    default_config = {
        "download_dir": "downloads",
        "max_file_size": 10 * 1024 * 1024,  # 10MB
        "allowed_extensions": ["pdf", "docx", "xlsx", "pptx", "txt", "csv", "json", "xml"],
        "organize_by_type": True,
        "skip_existing": True,
        "verify_ssl": True,
        "timeout": 30.0,
        "max_retries": 3,
        "retry_delay": 1.0,
        "max_workers": 5,
        "use_playwright": True,
        "use_user_agent_manager": True,
        "cache_enabled": True,
        "cache_ttl": 3600 * 24,  # 1 day
        "cache_size": 1000,
        "verbose": agent.verbose
    }

    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    file_processor_config = {**default_config, **(config or {})}

    # Sử dụng UserAgentManager nếu có
    if hasattr(agent, "_user_agent_manager") and agent._user_agent_manager:
        file_processor_config["use_user_agent_manager"] = True
        file_processor_config["user_agent_manager"] = agent._user_agent_manager

    # Khởi tạo FileProcessor
    agent._file_processor = FileProcessor(**file_processor_config)

    # Thêm phương thức download_file vào agent
    agent.download_file = lambda url, output_path=None, use_playwright=None: agent._file_processor.download_file(url, output_path, use_playwright)

    # Thêm phương thức process_file vào agent
    agent.process_file = lambda url, output_path=None, **kwargs: agent._file_processor.process_file(url, output_path, **kwargs)

    # Thêm phương thức is_file_url vào agent
    agent.is_file_url = lambda url: agent._file_processor.is_file_url(url)

    # Thêm phương thức extract_file_content vào agent
    agent.extract_file_content = lambda file_path, mime_type=None: agent._file_processor.extract_file_content(file_path, mime_type)

    # Thêm phương thức extract_files_from_html vào agent
    agent.extract_files_from_html = lambda url, html_content: agent._file_processor.extract_files_from_html(url, html_content)

    # Thêm phương thức batch_download_files vào agent
    agent.batch_download_files = lambda urls, output_dir=None: agent._file_processor.batch_download_files(urls, output_dir)

    # Thêm phương thức get_file_info vào agent
    agent.get_file_info = lambda url: agent._file_processor.get_file_info(url)

    # Thêm phương thức get_file_stats vào agent
    agent.get_file_stats = lambda: agent._file_processor.get_file_stats()

    # Đánh dấu là đã tích hợp
    agent.file_processor_integrated = True

    logger.info("FileProcessor đã được tích hợp thành công vào WebSearchAgentLocal")

    return agent
```

### Tích hợp vào AdaptiveCrawler

```python
from ..utils.shared.file_processor import FileProcessor
from ..utils.shared.integration import integrate_file_processor

def integrate_file_processor(crawler, config=None):
    """
    Tích hợp FileProcessor vào AdaptiveCrawler.
    """
    # Cấu hình mặc định
    default_config = {
        "download_dir": "downloads",
        "max_file_size": 10 * 1024 * 1024,  # 10MB
        "allowed_extensions": ["pdf", "docx", "xlsx", "pptx", "txt", "csv", "json", "xml"],
        "organize_by_type": True,
        "skip_existing": True,
        "verify_ssl": True,
        "timeout": 30.0,
        "max_retries": 3,
        "retry_delay": 1.0,
        "max_workers": 5,
        "use_playwright": True,
        "use_user_agent_manager": True,
        "cache_enabled": True,
        "cache_ttl": 3600 * 24,  # 1 day
        "cache_size": 1000,
        "verbose": crawler.verbose
    }

    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    file_processor_config = {**default_config, **(config or {})}

    # Sử dụng UserAgentManager nếu có
    if hasattr(crawler, "user_agent_manager") and crawler.user_agent_manager:
        file_processor_config["use_user_agent_manager"] = True
        file_processor_config["user_agent_manager"] = crawler.user_agent_manager

    # Khởi tạo FileProcessor
    crawler.file_processor = FileProcessor(**file_processor_config)

    # Thay thế phương thức _extract_files
    crawler._extract_files = lambda url, content: crawler.file_processor.extract_files_from_html(url, content)

    # Thay thế phương thức _extract_file_content
    crawler._extract_file_content = lambda file_path, mime_type=None: crawler.file_processor.extract_file_content(file_path, mime_type)

    # Đánh dấu là đã tích hợp
    crawler.file_processor_integrated = True

    logger.info("FileProcessor đã được tích hợp thành công vào AdaptiveCrawler")

    return crawler
```
