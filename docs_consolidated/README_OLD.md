# Deep Research Core

## G<PERSON>ớ<PERSON> thiệu

Deep Research Core là thư viện cung cấp các công cụ tìm kiếm, thu thập và phân tích thông tin nâng cao.

## Tính năng chính

- Tì<PERSON> kiếm web với phân tích độ tin cậy
- Crawling thích ứng với nhiều loại trang web
- Trích xuất nội dung thông minh
- Phân tích độ đáng tin cậy của nguồn
- Phát hiện tin giả
- Phân tích ngữ nghĩa
- Tích hợp LLM

## Phiên bản WebSearchAgentLocal

Dự án hiện có 3 phiên bản của WebSearchAgentLocal:

1. **WebSearchAgentLocalMerged** (`src/deep_research_core/agents/web_search_agent_local_merged.py`)
   - Phiên b<PERSON>n đ<PERSON>y đủ, hợ<PERSON> nhất tất cả tính năng hay nhất từ các phiên bản khác
   - <PERSON><PERSON> gồ<PERSON> xử lý tiếng Việt nâng cao
   - <PERSON><PERSON> gồ<PERSON> tính năng phân tích độ tin cậy
   - Tích hợp với LLM và các công cụ phân tích ngữ nghĩa

2. **WebSearchAgentLocalConsolidated** (`src/deep_research_core/agents/web_search_agent_local_consolidated.py`)
   - Phiên bản trung gian với các tính năng chính
   - Dễ tích hợp với hệ thống khác

3. **WebSearchAgentLocalMinimal** (`src/deep_research_core/agents/web_search_agent_local_minimal.py`)
   - Phiên bản tối giản với ít phụ thuộc
   - Hiệu suất cao, thích hợp cho môi trường hạn chế tài nguyên

## Hướng dẫn sử dụng

### Cài đặt

```bash
pip install -e .
```

### Sử dụng cơ bản

```python
from deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged

# Khởi tạo agent
agent = WebSearchAgentLocalMerged()

# Tìm kiếm
results = agent.search(
    query="Ảnh hưởng của biến đổi khí hậu đến Việt Nam",
    num_results=5,
    language="vi"
)

# In kết quả
for result in results['results']:
    print(f"Title: {result['title']}")
    print(f"URL: {result['url']}")
    print(f"Snippet: {result['snippet']}")
    print(f"Credibility Score: {result.get('credibility_score', 'N/A')}")
    print("---")

# Tạo câu trả lời tổng hợp
answer = agent.search(
    query="Ảnh hưởng của biến đổi khí hậu đến Việt Nam",
    num_results=10,
    language="vi",
    extract_content=True,
    evaluate_credibility=True
)['simple_answer']

print(f"Answer: {answer}")
```

## Các tùy chọn cấu hình

WebSearchAgentLocalMerged hỗ trợ nhiều tùy chọn cấu hình:

```python
agent = WebSearchAgentLocalMerged(
    # Cấu hình cơ bản
    data_dir="/path/to/data",                  # Thư mục dữ liệu
    enable_credibility_evaluation=True,        # Bật đánh giá độ tin cậy
    enable_query_enhancement=True,             # Bật tăng cường truy vấn
    use_advanced_extraction=True,              # Sử dụng trích xuất nâng cao
    
    # Tùy chọn độ tin cậy
    filter_unreliable_sources=True,            # Lọc nguồn không đáng tin cậy
    rerank_by_credibility=True,                # Sắp xếp lại kết quả theo độ tin cậy
    min_credibility_score=0.5,                 # Điểm tin cậy tối thiểu
    
    # Tùy chọn LLM
    use_local_llm=False,                       # Sử dụng LLM cục bộ
    llm_model="gpt-3.5-turbo",                 # Tên mô hình LLM
    
    # Tùy chọn phân tích
    fake_news_detection=True,                  # Bật phát hiện tin giả
    semantic_analysis=True,                    # Bật phân tích ngữ nghĩa
    
    # Tùy chọn request
    parallel_requests=5,                       # Số lượng request song song tối đa
    timeout=30,                                # Thời gian chờ request (giây)
    respect_robots_txt=True,                   # Tuân thủ robots.txt
    user_agent="Custom User Agent",            # User-agent tùy chỉnh
    
    # Tùy chọn cache
    cache_ttl=86400,                           # Thời gian cache (giây)
    
    # Hỗ trợ ngôn ngữ
    language_support=["en", "vi"]              # Danh sách ngôn ngữ hỗ trợ
)
```

## Giấy phép

© 2023-2024 Deep Research Team 