# Hướng dẫn cài đặt và sử dụng Deep Research Core

## Y<PERSON><PERSON> cầu hệ thống

- Python 3.8+
- Node.js 14+
- <PERSON><PERSON> (tố<PERSON> thiểu 4GB, khuyến nghị 8GB+)
- <PERSON><PERSON> dung lượng ổ cứng (tố<PERSON> thiểu 2GB)

## Cài đặt

### 1. Clone repository

```bash
git clone https://github.com/yourusername/deep_research_core.git
cd deep_research_core
```

### 2. Cài đặt các gói phụ thuộc Python

```bash
pip install -r requirements.txt
```

### 3. <PERSON><PERSON><PERSON> đặt Playwright

```bash
playwright install chromium
```

### 4. Cài đặt các gói phụ thuộc Node.js

```bash
npm install crawlee playwright
```

### 5. Cài đặt các gói ngôn ngữ cho spaCy và NLTK (tùy chọn)

```bash
# Cài đặt mô hình tiếng Việt cho spaCy
python -m spacy download vi_core_news_lg

# Cài đặt dữ liệu cho NLTK
python -m nltk.downloader punkt stopwords wordnet
```

## Kiểm tra cài đặt

Chạy script kiểm tra để đảm bảo mọi thứ đã được cài đặt đúng:

```bash
python tests/test_installation.py
```

## Sử dụng cơ bản

### 1. Tìm kiếm web với tối ưu hóa bộ nhớ

```python
from deep_research_core.agents.advanced_crawlee import search_with_advanced_crawlee

# Tìm kiếm với tối ưu hóa bộ nhớ
results = search_with_advanced_crawlee(
    query="Kinh tế Việt Nam 2023",
    num_results=20,
    max_depth=2,
    max_pages=30,
    timeout=60,
    detailed_scraping=True,
    language="vi",
    use_memory_optimization=True,  # Bật tính năng tối ưu hóa bộ nhớ
    batch_size=5  # Kích thước batch
)

# In kết quả
print(f"Số kết quả: {results.get('count', 0)}")
for i, result in enumerate(results.get('results', [])):
    print(f"{i+1}. {result.get('title', 'No title')}")
    print(f"   URL: {result.get('url', 'No URL')}")
    print(f"   Nội dung: {result.get('content', 'No content')[:100]}...")
```

### 2. Crawl và scrape với tối ưu hóa bộ nhớ

```python
from deep_research_core.agents.advanced_crawlee import crawl_and_scrape

# Danh sách URL cần crawl
urls = [
    "https://vnexpress.net/",
    "https://tuoitre.vn/",
    "https://dantri.com.vn/",
    "https://vietnamnet.vn/",
    "https://vi.wikipedia.org/"
]

# Crawl và scrape với tối ưu hóa bộ nhớ
results = crawl_and_scrape(
    start_urls=urls,
    max_depth=2,
    max_pages=50,
    max_results=20,
    timeout=120,
    detailed_scraping=True,
    use_memory_optimization=True,
    batch_size=3
)

# In kết quả
print(f"Số kết quả: {results.get('count', 0)}")
for i, result in enumerate(results.get('results', [])):
    print(f"{i+1}. {result.get('title', 'No title')}")
    print(f"   URL: {result.get('url', 'No URL')}")
    print(f"   Nội dung: {result.get('content', 'No content')[:100]}...")
```

### 3. Sử dụng ResourceManager và MemoryOptimizedCrawler trực tiếp

```python
from deep_research_core.agents.advanced_crawlee import ResourceManager, MemoryOptimizedCrawler

# Khởi tạo ResourceManager
resource_manager = ResourceManager(
    memory_limit_mb=2048,  # 2GB
    max_concurrent_processes=8,
    cpu_threshold=0.7
)

# Khởi tạo MemoryOptimizedCrawler
crawler = MemoryOptimizedCrawler(
    resource_manager=resource_manager,
    batch_size=10,
    max_retries=5,
    timeout=120,
    adaptive_batch_size=True
)

# Danh sách URL cần crawl
urls = [
    "https://vnexpress.net/",
    "https://tuoitre.vn/",
    "https://dantri.com.vn/",
    "https://vietnamnet.vn/",
    "https://vi.wikipedia.org/"
]

# Crawl URLs
results = crawler.crawl_urls(
    urls=urls,
    max_depth=3,
    detailed_scraping=True
)

# In kết quả
print(f"Số kết quả: {results.get('count', 0)}")
for i, result in enumerate(results.get('results', [])):
    print(f"{i+1}. {result.get('title', 'No title')}")
    print(f"   URL: {result.get('url', 'No URL')}")
    print(f"   Nội dung: {result.get('content', 'No content')[:100]}...")
```

## Ví dụ nâng cao

Xem thêm các ví dụ nâng cao trong thư mục `examples/`:

```bash
# Chạy ví dụ về tối ưu hóa bộ nhớ
python examples/optimized_crawlee_example.py

# Chạy ví dụ về tìm kiếm
python examples/optimized_crawlee_example.py --mode search --query "Kinh tế Việt Nam 2023"

# So sánh hiệu suất
python examples/optimized_crawlee_example.py --mode compare
```

## Xử lý lỗi phổ biến

### 1. Lỗi "Node.js or Crawlee not installed"

Đảm bảo bạn đã cài đặt Node.js và Crawlee:

```bash
# Kiểm tra phiên bản Node.js
node --version

# Cài đặt Crawlee
npm install crawlee
```

### 2. Lỗi "Playwright không sẵn sàng"

Đảm bảo bạn đã cài đặt Playwright và trình duyệt Chromium:

```bash
# Cài đặt Playwright
pip install playwright

# Cài đặt trình duyệt Chromium
playwright install chromium
```

### 3. Lỗi "Memory limit exceeded"

Tăng giới hạn bộ nhớ hoặc giảm kích thước batch:

```python
# Tăng giới hạn bộ nhớ
resource_manager = ResourceManager(memory_limit_mb=4096)  # 4GB

# Hoặc giảm kích thước batch
crawler = MemoryOptimizedCrawler(batch_size=3)
```

## Tài liệu API

Xem tài liệu API đầy đủ trong thư mục `docs/`.
