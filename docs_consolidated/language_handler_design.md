# Thiết kế module LanguageHandler dùng chung

Dựa trên phân tích các module xử lý ngôn ngữ tiếng Việt hiện tại, tôi đề xuất thiết kế module LanguageHandler dùng chung như sau:

## Cấu trúc module

```python
import re
import logging
import os
import sys
import time
import random
from typing import Dict, List, Optional, Any, Tuple, Union, Set
from dataclasses import dataclass, field
import numpy as np

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

class LanguageHandler:
    """
    Module xử lý ngôn ngữ dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    
    # Các ngôn ngữ được hỗ trợ
    SUPPORTED_LANGUAGES = {
        "vi": "Vietnamese",
        "en": "English",
        "zh": "Chinese",
        "ja": "Japanese",
        "ko": "Korean",
        "fr": "French",
        "de": "German",
        "es": "Spanish",
        "it": "Italian",
        "ru": "Russian",
        "pt": "Portuguese",
        "ar": "Arabic",
        "hi": "Hindi",
        "th": "Thai",
        "id": "Indonesian",
        "ms": "Malay",
        "auto": "Auto-detect"
    }
    
    # Từ khóa tiếng Việt
    VIETNAMESE_KEYWORDS = [
        "việt", "nam", "hà nội", "sài gòn", "hồ chí minh", "đà nẵng", "huế", 
        "cần thơ", "vũng tàu", "nha trang", "đà lạt", "hải phòng", "quảng ninh",
        "tiếng việt", "người việt", "việt nam", "dân tộc", "quốc gia", "tổ quốc",
        "đất nước", "quê hương", "dân tộc", "lịch sử", "văn hóa", "truyền thống",
        "phong tục", "tập quán", "ẩm thực", "món ăn", "đặc sản", "du lịch",
        "làng nghề", "làng quê", "nông thôn", "thành thị", "đô thị", "nông dân",
        "công nhân", "trí thức", "học sinh", "sinh viên", "giáo viên", "bác sĩ",
        "kỹ sư", "công chức", "viên chức", "doanh nhân", "kinh doanh", "thương mại",
        "xuất khẩu", "nhập khẩu", "nông nghiệp", "công nghiệp", "dịch vụ", "du lịch",
        "giáo dục", "y tế", "văn hóa", "thể thao", "giải trí", "âm nhạc", "điện ảnh",
        "hội họa", "kiến trúc", "văn học", "thơ ca", "tiểu thuyết", "truyện ngắn"
    ]
    
    # Từ dừng tiếng Việt
    VIETNAMESE_STOPWORDS = [
        "và", "của", "cho", "là", "để", "trong", "được", "với", "có", "không",
        "những", "các", "một", "về", "như", "đã", "từ", "khi", "đến", "này",
        "theo", "tại", "vì", "còn", "bị", "nên", "đó", "nếu", "trên", "dưới",
        "phải", "vào", "ra", "tôi", "bạn", "anh", "chị", "họ", "chúng", "mình",
        "thì", "mà", "lại", "cũng", "nữa", "đang", "sẽ", "rất", "nhiều", "cùng",
        "đều", "chỉ", "vẫn", "làm", "biết", "nói", "thấy", "qua", "lên", "xuống"
    ]
    
    # Mẫu phương ngữ tiếng Việt
    DIALECT_PATTERNS = {
        "northern": [
            r'\btao\b', r'\bmày\b', r'\bchúng mày\b', r'\bchúng tao\b',
            r'\bấy\b', r'\bthế\b', r'\bcái này\b', r'\bcái kia\b',
            r'\bđấy\b', r'\bthế\b', r'\bvâng\b', r'\bvới\b', r'\bcơm\b'
        ],
        "central": [
            r'\bmi\b', r'\bbây\b', r'\btui\b', r'\bbọn tui\b', r'\bbọn mi\b',
            r'\brứa\b', r'\bmô\b', r'\bni\b', r'\bchừ\b', r'\bđó chừ\b',
            r'\brăng\b', r'\bcơm\b'
        ],
        "southern": [
            r'\btui\b', r'\bbạn\b', r'\bqua\b', r'\bđó\b', r'\bnè\b',
            r'\bđâu\b', r'\bvậy\b', r'\bvậy đó\b', r'\bđó\b', r'\bđó nha\b',
            r'\bdạ\b', r'\bủa\b', r'\bcơm\b'
        ]
    }
    
    def __init__(
        self,
        default_language: str = "auto",
        enable_vietnamese_support: bool = True,
        enable_dialect_detection: bool = True,
        enable_language_detection: bool = True,
        enable_translation: bool = False,
        enable_text_normalization: bool = True,
        enable_keyword_extraction: bool = True,
        enable_compound_word_processing: bool = True,
        vietnamese_nlp_config: Optional[Dict[str, Any]] = None,
        translation_config: Optional[Dict[str, Any]] = None,
        cache_enabled: bool = True,
        cache_size: int = 1000,
        verbose: bool = False,
        **kwargs
    ):
        """
        Khởi tạo LanguageHandler.
        
        Args:
            default_language: Ngôn ngữ mặc định
            enable_vietnamese_support: Bật hỗ trợ tiếng Việt
            enable_dialect_detection: Bật phát hiện phương ngữ
            enable_language_detection: Bật phát hiện ngôn ngữ
            enable_translation: Bật dịch thuật
            enable_text_normalization: Bật chuẩn hóa văn bản
            enable_keyword_extraction: Bật trích xuất từ khóa
            enable_compound_word_processing: Bật xử lý từ ghép
            vietnamese_nlp_config: Cấu hình NLP tiếng Việt
            translation_config: Cấu hình dịch thuật
            cache_enabled: Bật cache
            cache_size: Kích thước cache
            verbose: Ghi log chi tiết
            **kwargs: Các tham số bổ sung
        """
        # Khởi tạo các thuộc tính
        self.default_language = default_language
        self.enable_vietnamese_support = enable_vietnamese_support
        self.enable_dialect_detection = enable_dialect_detection
        self.enable_language_detection = enable_language_detection
        self.enable_translation = enable_translation
        self.enable_text_normalization = enable_text_normalization
        self.enable_keyword_extraction = enable_keyword_extraction
        self.enable_compound_word_processing = enable_compound_word_processing
        self.vietnamese_nlp_config = vietnamese_nlp_config or {}
        self.translation_config = translation_config or {}
        self.cache_enabled = cache_enabled
        self.cache_size = cache_size
        self.verbose = verbose
        
        # Khởi tạo cache
        self.cache = {}
        
        # Khởi tạo các thành phần
        self._initialize_components()
        
        if self.verbose:
            logger.info("LanguageHandler đã được khởi tạo thành công")
```

## Các phương thức chính

1. **detect_language(text)**: Phát hiện ngôn ngữ của văn bản
2. **is_vietnamese(text)**: Kiểm tra xem văn bản có phải là tiếng Việt không
3. **detect_vietnamese_dialect(text)**: Phát hiện phương ngữ tiếng Việt
4. **normalize_text(text, language=None)**: Chuẩn hóa văn bản
5. **extract_keywords(text, language=None, num_keywords=5)**: Trích xuất từ khóa
6. **segment_words(text, language=None)**: Tách từ
7. **translate(text, source_language=None, target_language=None)**: Dịch văn bản
8. **optimize_search_query(query, language=None)**: Tối ưu hóa truy vấn tìm kiếm
9. **adapt_prompt_for_language(prompt, language=None)**: Điều chỉnh prompt cho phù hợp với ngôn ngữ

## Các phương thức hỗ trợ

1. **_initialize_components()**: Khởi tạo các thành phần
2. **_get_cache_key(text, operation)**: Lấy khóa cache
3. **_get_from_cache(key)**: Lấy kết quả từ cache
4. **_save_to_cache(key, result)**: Lưu kết quả vào cache
5. **_detect_language_with_langdetect(text)**: Phát hiện ngôn ngữ bằng langdetect
6. **_detect_language_with_fasttext(text)**: Phát hiện ngôn ngữ bằng fasttext
7. **_detect_language_with_cld3(text)**: Phát hiện ngôn ngữ bằng cld3
8. **_normalize_vietnamese_text(text)**: Chuẩn hóa văn bản tiếng Việt
9. **_extract_vietnamese_keywords(text, num_keywords=5)**: Trích xuất từ khóa tiếng Việt
10. **_segment_vietnamese_words(text)**: Tách từ tiếng Việt

## Các tính năng đặc biệt

1. **Hỗ trợ tiếng Việt**: Hỗ trợ đặc biệt cho tiếng Việt
2. **Phát hiện phương ngữ**: Phát hiện phương ngữ tiếng Việt (Bắc, Trung, Nam)
3. **Tối ưu hóa truy vấn tìm kiếm**: Tối ưu hóa truy vấn tìm kiếm cho từng ngôn ngữ
4. **Xử lý từ ghép**: Xử lý từ ghép tiếng Việt
5. **Dịch thuật**: Dịch văn bản giữa các ngôn ngữ
6. **Cache kết quả**: Lưu cache kết quả để tránh tính toán lại
7. **Hỗ trợ nhiều ngôn ngữ**: Hỗ trợ nhiều ngôn ngữ khác nhau
8. **Điều chỉnh prompt**: Điều chỉnh prompt cho phù hợp với ngôn ngữ

## Cách tích hợp

### Tích hợp vào WebSearchAgentLocal

```python
from ..utils.language_handler import LanguageHandler

def integrate_language_handler(agent, config=None):
    """
    Tích hợp LanguageHandler vào WebSearchAgentLocal.
    """
    # Cấu hình mặc định
    default_config = {
        "default_language": "auto",
        "enable_vietnamese_support": True,
        "enable_dialect_detection": True,
        "enable_language_detection": True,
        "enable_translation": False,
        "enable_text_normalization": True,
        "enable_keyword_extraction": True,
        "enable_compound_word_processing": True,
        "cache_enabled": True,
        "verbose": False
    }
    
    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    language_config = {**default_config, **(config or {})}
    
    # Khởi tạo LanguageHandler
    agent._language_handler = LanguageHandler(**language_config)
    
    # Thêm phương thức detect_language vào agent
    agent.detect_language = lambda text: agent._language_handler.detect_language(text)
    
    # Thêm phương thức is_vietnamese vào agent
    agent.is_vietnamese = lambda text: agent._language_handler.is_vietnamese(text)
    
    # Thêm phương thức optimize_search_query vào agent
    agent.optimize_search_query = lambda query, language=None: agent._language_handler.optimize_search_query(query, language)
    
    # Đánh dấu là đã tích hợp
    agent.language_handler_integrated = True
    
    logger.info("LanguageHandler đã được tích hợp thành công vào WebSearchAgentLocal")
```

### Tích hợp vào AdaptiveCrawler

```python
from ..utils.language_handler import LanguageHandler

def integrate_language_handler(crawler, config=None):
    """
    Tích hợp LanguageHandler vào AdaptiveCrawler.
    """
    # Cấu hình mặc định
    default_config = {
        "default_language": "auto",
        "enable_vietnamese_support": True,
        "enable_dialect_detection": True,
        "enable_language_detection": True,
        "enable_translation": False,
        "enable_text_normalization": True,
        "enable_keyword_extraction": True,
        "enable_compound_word_processing": True,
        "cache_enabled": True,
        "verbose": crawler.verbose
    }
    
    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    language_config = {**default_config, **(config or {})}
    
    # Khởi tạo LanguageHandler
    crawler.language_handler = LanguageHandler(**language_config)
    
    # Thay thế phương thức _detect_language
    crawler._detect_language = lambda text: crawler.language_handler.detect_language(text)
    
    # Đánh dấu là đã tích hợp
    crawler.language_handler_integrated = True
    
    logger.info("LanguageHandler đã được tích hợp thành công vào AdaptiveCrawler")
```
