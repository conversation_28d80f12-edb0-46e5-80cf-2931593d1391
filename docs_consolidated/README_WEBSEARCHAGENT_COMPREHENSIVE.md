# 🚀 WebSearchAgentLocalMerged - Comprehensive Documentation

## 📋 Overview

**WebSearchAgentLocalMerged** is an enterprise-grade web search agent with advanced features including real search functionality, multi-engine support, smart caching, and comprehensive performance optimization.

### ✨ Key Features

- **🔍 Real Search Engines**: SearXNG (local priority), DuckDuckGo, Bing API, Google Custom Search
- **🧠 Smart Caching**: TTL-based caching with performance optimization
- **🌍 Multi-language Support**: Vietnamese and international language processing
- **⚡ Performance Optimization**: Async processing, connection pooling, adaptive timeouts
- **🕷️ Advanced Crawling**: JavaScript support, SPA crawling, infinite scroll handling
- **📊 Analytics**: Performance stats, credibility evaluation, answer quality assessment
- **🔧 Resource Management**: Automatic cleanup, memory optimization
- **🎯 Query Optimization**: Intent detection, query enhancement, alternative generation

## 🏗️ Architecture

### Core Components

1. **Search Engine Layer**
   - SearXNG Local (Priority #1)
   - SearXNG Public Instances (Fallback)
   - DuckDuckGo HTML Parsing
   - Bing Search API
   - Google Custom Search API

2. **Processing Layer**
   - Query Optimization
   - Multi-language Processing
   - Content Analysis
   - Credibility Evaluation

3. **Performance Layer**
   - Smart Caching
   - Connection Pooling
   - Async Processing
   - Resource Management

4. **Integration Layer**
   - LLM Integration
   - Plugin System
   - Feedback System
   - Analytics

## 🚀 Quick Start

### Installation

```bash
# Clone repository
git clone <repository-url>
cd deep_research_core

# Install dependencies
pip install -r requirements.txt

# Optional: Setup SearXNG local
docker run -d -p 8080:8080 searxng/searxng
```

### Basic Usage

```python
from deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged

# Initialize agent
agent = WebSearchAgentLocalMerged(verbose=True)

# Basic search
results = agent.search(
    query="Python programming tutorial",
    num_results=10,
    get_content=True
)

print(f"Found {len(results['results'])} results")
for result in results['results']:
    print(f"- {result['title']}: {result['url']}")
```

### Advanced Usage

```python
# Advanced search with all features
results = agent.search(
    query="Machine learning algorithms",
    num_results=15,
    get_content=True,
    evaluate_question=True,
    evaluate_answer=True,
    use_deep_research=True
)

# Access metadata
metadata = results['metadata']
print(f"Search time: {metadata['search_time']:.3f}s")
print(f"From cache: {metadata.get('from_cache', False)}")
print(f"Sources used: {metadata.get('sources_used', [])}")

# Access quality evaluation
if 'question_evaluation' in results:
    complexity = results['question_evaluation']['complexity_score']
    print(f"Question complexity: {complexity}")

if 'answer_evaluation' in results:
    quality = results['answer_evaluation']['quality_score']
    print(f"Answer quality: {quality}")
```

## 🔧 Configuration

### Environment Variables

```bash
# SearXNG Configuration
export SEARXNG_LOCAL_URL="http://localhost:8080"

# API Keys (Optional)
export BING_SEARCH_API_KEY="your_bing_api_key"
export GOOGLE_SEARCH_API_KEY="your_google_api_key"
export GOOGLE_CSE_ID="your_google_cse_id"

# LLM Configuration (Optional)
export OPENAI_API_KEY="your_openai_api_key"
export ANTHROPIC_API_KEY="your_anthropic_api_key"
```

### Agent Configuration

```python
agent = WebSearchAgentLocalMerged(
    # Basic settings
    verbose=True,
    timeout=30,
    max_retries=3,
    
    # Cache settings
    use_cache=True,
    cache_ttl=3600,  # 1 hour
    
    # Performance settings
    enable_performance_optimization=True,
    max_concurrent_requests=5,
    
    # Feature toggles
    enable_credibility_evaluation=True,
    enable_llm_integration=False,
    enable_deep_research=True
)
```

## 📚 API Reference

### Core Methods

#### `search(query, **kwargs)`

Main search method with comprehensive options.

**Parameters:**
- `query` (str): Search query
- `num_results` (int): Number of results (default: 10)
- `get_content` (bool): Extract full content (default: False)
- `evaluate_question` (bool): Evaluate question complexity (default: False)
- `evaluate_answer` (bool): Evaluate answer quality (default: False)
- `use_deep_research` (bool): Use deep research mode (default: False)

**Returns:**
- `dict`: Search results with metadata

#### `get_performance_stats()`

Get performance statistics.

**Returns:**
- `dict`: Performance metrics including uptime, request counts, success rate

#### `optimize_performance(**kwargs)`

Optimize agent performance settings.

**Parameters:**
- `enable_compression` (bool): Enable response compression
- `enable_keep_alive` (bool): Enable connection keep-alive
- `max_pool_connections` (int): Maximum pool connections

### Advanced Methods

#### `batch_process_urls(urls, **kwargs)`

Process multiple URLs in batches.

**Parameters:**
- `urls` (List[str]): List of URLs to process
- `batch_size` (int): Batch size (default: 5)
- `delay_between_batches` (float): Delay between batches (default: 1.0)

#### `crawl_with_javascript_support(url, **kwargs)`

Crawl websites with JavaScript support.

**Parameters:**
- `url` (str): URL to crawl
- `wait_for_selector` (str): CSS selector to wait for
- `timeout` (int): Timeout in seconds

#### `detect_content_language(content)`

Detect content language.

**Parameters:**
- `content` (str): Text content

**Returns:**
- `str`: Detected language code

#### `optimize_query(query)`

Optimize search query.

**Parameters:**
- `query` (str): Original query

**Returns:**
- `str`: Optimized query

## 🎯 Use Cases

### 1. Academic Research

```python
# Research assistant for academic papers
agent = WebSearchAgentLocalMerged(
    enable_credibility_evaluation=True,
    enable_llm_integration=True
)

results = agent.search(
    query="quantum computing algorithms 2024",
    num_results=20,
    get_content=True,
    evaluate_question=True
)

# Filter high-credibility sources
high_quality = [r for r in results['results'] 
                if r.get('credibility_score', 0) > 0.8]
```

### 2. News Monitoring

```python
# Real-time news monitoring
agent = WebSearchAgentLocalMerged(
    use_cache=False,  # Always get fresh results
    timeout=10
)

news_results = agent.search(
    query="breaking news technology",
    num_results=50,
    get_content=True
)

# Process recent news
for result in news_results['results']:
    if 'published_date' in result:
        print(f"📰 {result['title']} - {result['published_date']}")
```

### 3. Competitive Intelligence

```python
# Market research and competitor analysis
agent = WebSearchAgentLocalMerged(
    enable_deep_research=True,
    max_concurrent_requests=10
)

competitor_data = agent.search(
    query="AI startup funding 2024",
    num_results=100,
    get_content=True,
    use_deep_research=True
)

# Analyze market trends
trends = agent.analyze_content_with_llm(
    content=competitor_data['simple_answer'],
    analysis_type="market_trends"
)
```

### 4. Vietnamese Content Search

```python
# Vietnamese language search
agent = WebSearchAgentLocalMerged(verbose=True)

vn_results = agent.search(
    query="lập trình Python cơ bản",
    num_results=15,
    get_content=True
)

# Process Vietnamese content
for result in vn_results['results']:
    language = agent.detect_content_language(result['content'])
    if language == 'vi':
        keywords = agent.extract_keywords_multilingual(
            result['content'], 'vi'
        )
        print(f"🇻🇳 {result['title']}: {keywords}")
```

## 🔍 Search Engine Priority

The agent uses a smart priority system:

1. **🥇 SearXNG Local** (localhost:8080, 4000, 8888)
   - Fastest response time
   - No rate limiting
   - Full privacy control

2. **🥈 SearXNG Public** (searx.be, search.sapti.me, etc.)
   - Reliable fallback
   - Multiple instances
   - Good performance

3. **🥉 DuckDuckGo** (HTML parsing)
   - No API key required
   - Privacy-focused
   - Consistent availability

4. **🏅 Bing/Google APIs** (if configured)
   - High-quality results
   - Rich metadata
   - Rate limited

## ⚡ Performance Optimization

### Caching Strategy

- **TTL-based caching**: Automatic expiration
- **Smart cache keys**: Query normalization
- **Cache statistics**: Hit/miss tracking
- **Memory management**: Automatic cleanup

### Connection Management

- **Connection pooling**: Reuse HTTP connections
- **Adaptive timeouts**: Dynamic timeout adjustment
- **Retry logic**: Exponential backoff
- **Resource cleanup**: Automatic resource management

### Async Processing

- **Parallel requests**: Multiple search engines
- **Batch processing**: Efficient URL handling
- **Thread pools**: Concurrent operations
- **Memory optimization**: Efficient data structures

## 🧪 Testing

### Running Tests

```bash
# Structure and functionality tests
python3 test_simple_functionality.py

# Performance benchmarks
python3 test_performance_benchmark.py

# Comprehensive test suite
python3 test_comprehensive_web_search_agent.py
```

### Test Results

- **Structure Tests**: 100% PASS
- **Method Coverage**: 29/29 methods (100%)
- **Performance**: 66.7% score (GOOD)
- **Search Engines**: SearXNG local working

## 🚨 Troubleshooting

### Common Issues

1. **SearXNG Local Not Found**
   ```bash
   # Setup SearXNG with Docker
   docker run -d -p 8080:8080 searxng/searxng
   ```

2. **Slow Performance**
   ```python
   # Enable performance optimization
   agent.optimize_performance(
       enable_compression=True,
       enable_keep_alive=True,
       max_pool_connections=20
   )
   ```

3. **Memory Issues**
   ```python
   # Regular cleanup
   agent.cleanup_resources()
   
   # Check memory usage
   stats = agent.get_performance_stats()
   print(f"Memory usage: {stats['memory_usage']}")
   ```

4. **Rate Limiting**
   ```python
   # Use local SearXNG to avoid rate limits
   os.environ['SEARXNG_LOCAL_URL'] = 'http://localhost:8080'
   ```

## 🔮 Future Enhancements

- **AI-powered query understanding**
- **Real-time result streaming**
- **Advanced content analysis**
- **Multi-modal search (images, videos)**
- **Distributed search architecture**
- **Enhanced Vietnamese language support**

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the test files for examples
