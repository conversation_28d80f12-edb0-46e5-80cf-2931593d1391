# Thiết kế module ConfigManager dùng chung

Dựa trên phân tích các tham số cấu hình hiện tại trong cả hai module, tôi đề xuất thiết kế module ConfigManager dùng chung như sau:

## Cấu trúc module

```python
import os
import json
import yaml
import logging
from typing import Dict, List, Optional, Any, Tuple, Union, Set
from copy import deepcopy

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

class ConfigManager:
    """
    Module quản lý cấu hình dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """
    
    # Cấu hình mặc định cho WebSearchAgentLocal
    DEFAULT_WEB_SEARCH_CONFIG = {
        # Cấu hình tìm kiếm
        "search": {
            "method": "auto",  # "searxng", "crawlee", hoặc "auto"
            "api_search": {
                "engine": "searx",
                "searx_url": "http://localhost:8080",
                "language": "auto",
                "time_range": "",
                "safe_search": False,
            },
            "crawlee_search": {
                "max_depth": 2,
                "max_pages_per_url": 3,
                "max_urls": 5,
                "timeout": 30,
            },
        },
        
        # Cấu hình trích xuất nội dung
        "content_extractor": {
            "extract_links": True,
            "extract_images": False,
            "max_content_length": 10000,
            "timeout": 15,
        },
        
        # Cấu hình cache
        "cache": {
            "enabled": True,
            "ttl": 3600,  # 1 giờ
            "size": 1000,
        },
        
        # Cấu hình rate limit
        "rate_limit": {
            "requests_per_minute": 20,
            "engine_limits": {
                "searxng": (200, 60),  # 200 requests per minute for local SearXNG
                "crawlee": (100, 60),  # 100 requests per minute for Crawlee
            },
        },
        
        # Cấu hình QueryDecomposer
        "query_decomposer": {
            "enabled": True,
            "provider": "openrouter",
            "model": "anthropic/claude-3-opus",
            "temperature": 0.3,
            "max_tokens": 1000,
            "language": "auto",
            "use_cache": True,
            "cache_size": 100,
        },
        
        # Cấu hình QuestionComplexityEvaluator
        "question_evaluator": {
            "enabled": True,
            "complexity_threshold_high": 0.7,
            "complexity_threshold_medium": 0.4,
            "use_tool_analyzer": True,
            "use_cot_optimization": True,
            "use_domain_knowledge": True,
        },
        
        # Cấu hình AnswerQualityEvaluator
        "answer_evaluator": {
            "enabled": True,
            "evaluation_thresholds": {
                "relevance": 6.0,
                "factual_accuracy": 7.0,
                "completeness": 6.0,
                "overall": 6.0,
                "need_more_search": 5.0,
            },
            "use_model_evaluation": False,
            "use_heuristics": True,
            "language": "auto",
        },
        
        # Cấu hình AdaptiveScraper
        "scraper": {
            "timeout": 10.0,
            "max_retries": 3,
            "retry_delay": 2.0,
            "cache_enabled": True,
            "cache_ttl": 3600,
            "cache_size": 1000,
            "extract_metadata": True,
            "extract_main_content": True,
            "extract_images": False,
            "extract_links": True,
            "extract_tables": False,
            "extract_lists": True,
            "extract_headings": True,
            "extract_structured_data": False,
            "detect_language": True,
            "detect_encoding": True,
            "detect_content_type": True,
            "detect_site_type": True,
            "clean_content": True,
            "remove_ads": True,
            "remove_navigation": True,
            "remove_sidebars": True,
            "remove_footers": True,
            "remove_comments": True,
        },
        
        # Cấu hình CaptchaHandler
        "captcha_handler": {
            "enabled": True,
            "auto_solve": False,
            "use_selenium": False,
            "use_playwright": True,
            "max_retries": 3,
            "retry_delay": 2.0,
            "vietnamese_support": True,
        },
        
        # Cấu hình khác
        "verbose": False,
        "enable_plugins": True,
        "enable_vietnamese_search": True,
        "enable_file_download": False,
        "enable_nlp": True,
        "enable_multimedia_search": True,
    }
    
    # Cấu hình mặc định cho AdaptiveCrawler
    DEFAULT_ADAPTIVE_CRAWLER_CONFIG = {
        # Cấu hình crawl
        "crawl": {
            "max_depth": 2,
            "max_urls_per_domain": 5,
            "max_total_urls": 100,
            "delay": 1.0,
            "timeout": 10.0,
            "respect_robots_txt": True,
            "follow_redirects": True,
            "max_redirects": 5,
            "max_retries": 3,
            "retry_delay": 2.0,
            "max_threads": 5,
            "verify_ssl": True,
            "cache_enabled": True,
            "cache_ttl": 3600,
            "cache_size": 1000,
            "extract_metadata": True,
            "extract_links": True,
            "extract_images": False,
            "extract_files": False,
            "extract_structured_data": False,
            "detect_language": True,
            "detect_encoding": True,
            "detect_content_type": True,
            "detect_site_type": True,
            "handle_javascript": False,
            "handle_captcha": False,
        },
        
        # Cấu hình crawl mode
        "crawl_mode": "basic",  # "basic", "full_site", "content_only", "media_only"
        "download_media": False,
        "download_path": None,
        "site_map_enabled": False,
        "max_media_size_mb": 10,
        "extract_file_content": False,
        
        # Cấu hình JavaScript
        "enable_javascript": True,
        "wait_for_selector": None,
        "wait_for_timeout": 1000,
        "handle_spa": False,
        "handle_infinite_scroll": False,
        "infinite_scroll_max_scrolls": 5,
        "infinite_scroll_timeout": 1000,
        "handle_ajax": False,
        "ajax_wait_time": 2000,
        "ajax_request_patterns": None,
        
        # Cấu hình memory optimization
        "use_memory_optimization": True,
        "batch_size": 5,
        "memory_threshold": 80.0,
        "memory_cleanup_interval": 5,
        
        # Cấu hình Playwright
        "use_playwright": True,
        
        # Cấu hình khác
        "verbose": False,
    }
    
    def __init__(
        self,
        config_path: Optional[str] = None,
        config_dict: Optional[Dict[str, Any]] = None,
        load_from_env: bool = True,
        verbose: bool = False,
        **kwargs
    ):
        """
        Khởi tạo ConfigManager.
        
        Args:
            config_path: Đường dẫn đến file cấu hình (JSON hoặc YAML)
            config_dict: Dictionary chứa cấu hình
            load_from_env: Tải cấu hình từ biến môi trường
            verbose: Ghi log chi tiết
            **kwargs: Các tham số cấu hình bổ sung
        """
        # Khởi tạo các thuộc tính
        self.config_path = config_path
        self.verbose = verbose
        
        # Khởi tạo cấu hình mặc định
        self.config = {}
        self.config.update(deepcopy(self.DEFAULT_WEB_SEARCH_CONFIG))
        self.config.update(deepcopy(self.DEFAULT_ADAPTIVE_CRAWLER_CONFIG))
        
        # Tải cấu hình từ file nếu có
        if config_path and os.path.exists(config_path):
            self._load_config_from_file(config_path)
        
        # Tải cấu hình từ dictionary nếu có
        if config_dict:
            self._update_config_recursive(self.config, config_dict)
        
        # Tải cấu hình từ biến môi trường nếu cần
        if load_from_env:
            self._load_config_from_env()
        
        # Cập nhật cấu hình từ kwargs
        if kwargs:
            self._update_config_recursive(self.config, kwargs)
        
        # Xác thực cấu hình
        self._validate_config()
        
        if self.verbose:
            logger.info("ConfigManager đã được khởi tạo thành công")
```

## Các phương thức chính

1. **get_web_search_config()**: Lấy cấu hình cho WebSearchAgentLocal
2. **get_adaptive_crawler_config()**: Lấy cấu hình cho AdaptiveCrawler
3. **get_config(key, default=None)**: Lấy giá trị cấu hình theo khóa
4. **set_config(key, value)**: Đặt giá trị cấu hình theo khóa
5. **update_config(config_dict)**: Cập nhật cấu hình từ dictionary
6. **save_config(config_path=None)**: Lưu cấu hình vào file
7. **load_config(config_path)**: Tải cấu hình từ file
8. **reset_config()**: Đặt lại cấu hình về mặc định

## Các phương thức hỗ trợ

1. **_load_config_from_file(config_path)**: Tải cấu hình từ file
2. **_load_config_from_env()**: Tải cấu hình từ biến môi trường
3. **_update_config_recursive(config, update_dict)**: Cập nhật cấu hình đệ quy
4. **_validate_config()**: Xác thực cấu hình
5. **_get_nested_config(config, key_path)**: Lấy giá trị cấu hình lồng nhau
6. **_set_nested_config(config, key_path, value)**: Đặt giá trị cấu hình lồng nhau
7. **_merge_configs(config1, config2)**: Kết hợp hai cấu hình

## Các tính năng đặc biệt

1. **Hỗ trợ cấu hình lồng nhau**: Hỗ trợ cấu hình lồng nhau với nhiều cấp
2. **Hỗ trợ nhiều định dạng file**: Hỗ trợ cả JSON và YAML
3. **Tải cấu hình từ biến môi trường**: Tự động tải cấu hình từ biến môi trường
4. **Xác thực cấu hình**: Tự động xác thực cấu hình để đảm bảo tính hợp lệ
5. **Cấu hình mặc định**: Cung cấp cấu hình mặc định cho cả hai module
6. **Kết hợp cấu hình**: Kết hợp cấu hình từ nhiều nguồn (file, dictionary, biến môi trường, kwargs)
7. **Lưu cấu hình**: Lưu cấu hình vào file để sử dụng lại

## Cách tích hợp

### Tích hợp vào WebSearchAgentLocal

```python
from ..utils.config_manager import ConfigManager

def integrate_config_manager(agent, config=None):
    """
    Tích hợp ConfigManager vào WebSearchAgentLocal.
    """
    # Khởi tạo ConfigManager
    agent._config_manager = ConfigManager(config_dict=config)
    
    # Lấy cấu hình cho WebSearchAgentLocal
    web_search_config = agent._config_manager.get_web_search_config()
    
    # Cập nhật các thuộc tính của agent từ cấu hình
    agent.search_method = web_search_config["search"]["method"]
    agent.api_search_config = web_search_config["search"]["api_search"]
    agent.crawlee_search_config = web_search_config["search"]["crawlee_search"]
    agent.content_extractor_config = web_search_config["content_extractor"]
    agent.cache_ttl = web_search_config["cache"]["ttl"]
    agent.rate_limit = web_search_config["rate_limit"]["requests_per_minute"]
    agent.verbose = web_search_config["verbose"]
    
    # Đánh dấu là đã tích hợp
    agent.config_manager_integrated = True
    
    logger.info("ConfigManager đã được tích hợp thành công vào WebSearchAgentLocal")
```

### Tích hợp vào AdaptiveCrawler

```python
from ..utils.config_manager import ConfigManager

def integrate_config_manager(crawler, config=None):
    """
    Tích hợp ConfigManager vào AdaptiveCrawler.
    """
    # Khởi tạo ConfigManager
    crawler._config_manager = ConfigManager(config_dict=config)
    
    # Lấy cấu hình cho AdaptiveCrawler
    adaptive_crawler_config = crawler._config_manager.get_adaptive_crawler_config()
    
    # Cập nhật các thuộc tính của crawler từ cấu hình
    crawler.max_depth = adaptive_crawler_config["crawl"]["max_depth"]
    crawler.max_urls_per_domain = adaptive_crawler_config["crawl"]["max_urls_per_domain"]
    crawler.max_total_urls = adaptive_crawler_config["crawl"]["max_total_urls"]
    crawler.delay = adaptive_crawler_config["crawl"]["delay"]
    crawler.timeout = adaptive_crawler_config["crawl"]["timeout"]
    crawler.respect_robots_txt = adaptive_crawler_config["crawl"]["respect_robots_txt"]
    crawler.follow_redirects = adaptive_crawler_config["crawl"]["follow_redirects"]
    crawler.max_redirects = adaptive_crawler_config["crawl"]["max_redirects"]
    crawler.max_retries = adaptive_crawler_config["crawl"]["max_retries"]
    crawler.retry_delay = adaptive_crawler_config["crawl"]["retry_delay"]
    crawler.max_threads = adaptive_crawler_config["crawl"]["max_threads"]
    crawler.verify_ssl = adaptive_crawler_config["crawl"]["verify_ssl"]
    crawler.cache_enabled = adaptive_crawler_config["crawl"]["cache_enabled"]
    crawler.cache_ttl = adaptive_crawler_config["crawl"]["cache_ttl"]
    crawler.cache_size = adaptive_crawler_config["crawl"]["cache_size"]
    crawler.extract_metadata = adaptive_crawler_config["crawl"]["extract_metadata"]
    crawler.extract_links = adaptive_crawler_config["crawl"]["extract_links"]
    crawler.extract_images = adaptive_crawler_config["crawl"]["extract_images"]
    crawler.extract_files = adaptive_crawler_config["crawl"]["extract_files"]
    crawler.extract_structured_data = adaptive_crawler_config["crawl"]["extract_structured_data"]
    crawler.detect_language = adaptive_crawler_config["crawl"]["detect_language"]
    crawler.detect_encoding = adaptive_crawler_config["crawl"]["detect_encoding"]
    crawler.detect_content_type = adaptive_crawler_config["crawl"]["detect_content_type"]
    crawler.detect_site_type = adaptive_crawler_config["crawl"]["detect_site_type"]
    crawler.handle_javascript = adaptive_crawler_config["crawl"]["handle_javascript"]
    crawler.handle_captcha = adaptive_crawler_config["crawl"]["handle_captcha"]
    crawler.crawl_mode = adaptive_crawler_config["crawl_mode"]
    crawler.download_media = adaptive_crawler_config["download_media"]
    crawler.download_path = adaptive_crawler_config["download_path"]
    crawler.site_map_enabled = adaptive_crawler_config["site_map_enabled"]
    crawler.max_media_size_mb = adaptive_crawler_config["max_media_size_mb"]
    crawler.extract_file_content = adaptive_crawler_config["extract_file_content"]
    crawler.enable_javascript = adaptive_crawler_config["enable_javascript"]
    crawler.wait_for_selector = adaptive_crawler_config["wait_for_selector"]
    crawler.wait_for_timeout = adaptive_crawler_config["wait_for_timeout"]
    crawler.handle_spa = adaptive_crawler_config["handle_spa"]
    crawler.handle_infinite_scroll = adaptive_crawler_config["handle_infinite_scroll"]
    crawler.infinite_scroll_max_scrolls = adaptive_crawler_config["infinite_scroll_max_scrolls"]
    crawler.infinite_scroll_timeout = adaptive_crawler_config["infinite_scroll_timeout"]
    crawler.handle_ajax = adaptive_crawler_config["handle_ajax"]
    crawler.ajax_wait_time = adaptive_crawler_config["ajax_wait_time"]
    crawler.ajax_request_patterns = adaptive_crawler_config["ajax_request_patterns"]
    crawler.use_memory_optimization = adaptive_crawler_config["use_memory_optimization"]
    crawler.use_playwright = adaptive_crawler_config["use_playwright"]
    crawler.verbose = adaptive_crawler_config["verbose"]
    
    # Đánh dấu là đã tích hợp
    crawler.config_manager_integrated = True
    
    logger.info("ConfigManager đã được tích hợp thành công vào AdaptiveCrawler")
```
