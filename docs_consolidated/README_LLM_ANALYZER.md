# Bộ Phân Tích Nội Dung Sử Dụng Mô Hình Ngôn Ngữ Lớn (LLM)

Tài liệu này mô tả cách sử dụng và tích hợp bộ phân tích nội dung LLM (Large Language Model) để nâng cao khả năng phát hiện tin giả và đánh giá độ tin cậy nguồn thông tin.

## Tổng quan

Bộ phân tích nội dung LLM sử dụng các mô hình ngôn ngữ lớn để phân tích sâu nội dung và xác định mức độ tin cậy, giúp cải thiện đáng kể độ chính xác của việc phát hiện tin giả. Hệ thống này hoạt động dựa trên các nguyên tắc:

1. Phân tích ngữ nghĩa nâng cao bằng mô hình ngôn ngữ lớn
2. <PERSON>ân loại zero-shot để nhận diện các đặc điểm của nội dung đáng ngờ
3. Kết hợp kết quả phân tích LLM với các phương pháp phát hiện tin giả truyền thống
4. Cung cấp phân tích chi tiết về lý do đánh giá và các vấn đề được phát hiện

## Cài đặt

Bộ phân tích nội dung LLM yêu cầu các thư viện Python sau để sử dụng đầy đủ tính năng:

```
transformers>=4.18.0
torch>=1.10.0
openai>=0.27.0 (tùy chọn, chỉ cần nếu sử dụng OpenAI API)
```

Bạn có thể cài đặt các thư viện này bằng pip:

```bash
# Cài đặt Hugging Face Transformers và PyTorch
pip install transformers torch

# Nếu muốn sử dụng OpenAI API
pip install openai
```

## Các loại mô hình được hỗ trợ

Bộ phân tích nội dung LLM hỗ trợ hai loại mô hình chính:

1. **Hugging Face Transformers** (mặc định)
   - Sử dụng các mô hình pre-trained từ Hugging Face
   - Không yêu cầu API key
   - Chạy cục bộ trên máy của bạn
   - Hỗ trợ GPU nếu có

2. **OpenAI API**
   - Sử dụng các mô hình mạnh mẽ của OpenAI như GPT-3.5 hoặc GPT-4
   - Yêu cầu API key từ OpenAI
   - Gửi dữ liệu đến API của OpenAI

## Sử dụng cơ bản

### Khởi tạo bộ phân tích nội dung LLM

```python
from src.deep_research_core.utils.llm_content_analyzer import create_llm_analyzer

# Sử dụng Hugging Face Transformers (mặc định)
analyzer = create_llm_analyzer(
    model_type="huggingface",
    model_name="facebook/bart-large-mnli",  # Mô hình phân loại zero-shot
    verbose=True
)

# Hoặc sử dụng OpenAI API
openai_analyzer = create_llm_analyzer(
    model_type="openai",
    model_name="gpt-3.5-turbo",
    openai_api_key="your-api-key-here",
    verbose=True
)
```

### Phân tích nội dung

```python
# Phân tích nội dung
content = "Scientists have discovered a MIRACLE cure that can treat ALL diseases, but the government and Big Pharma are HIDING it from you!"
title = "SHOCKING: Secret Cure for All Diseases REVEALED!"

result = analyzer.analyze_content(content, title)

# Kiểm tra kết quả
print(f"Xác suất tin giả: {result['fake_news_probability']:.2f}")
print(f"Điểm tin cậy: {result['credibility_score']:.2f}")
print(f"Danh mục hàng đầu: {result['top_category']}")
print(f"Lý do: {result['reason']}")

# Xem các vấn đề được xác định
for issue in result["identified_issues"]:
    print(f"- {issue}")
```

### Đánh giá độ tin cậy

```python
# Đánh giá độ tin cậy
credibility = analyzer.evaluate_credibility(content, title)

print(f"Điểm tin cậy: {credibility['credibility_score']:.2f}")
print(f"Mức độ tin cậy: {credibility['credibility_level']}")

# Xem các cảnh báo
for warning in credibility["warnings"]:
    print(f"- {warning['description']} (mức độ: {warning['severity']})")
```

## Tích hợp với CredibilityEvaluator

Bộ phân tích nội dung LLM có thể được tích hợp với `CredibilityEvaluator` để cải thiện đánh giá độ tin cậy:

```python
from src.deep_research_core.utils.credibility_evaluator import CredibilityEvaluator
from src.deep_research_core.utils.llm_content_analyzer import create_llm_analyzer

# Khởi tạo CredibilityEvaluator
evaluator = CredibilityEvaluator()

# Khởi tạo LLMContentAnalyzer
llm_analyzer = create_llm_analyzer()

# Đánh giá URL với nội dung
url = "https://example.com/article"
content = "BOMBSHELL DISCOVERY!!! Scientists have found a MIRACLE CURE..."
title = "SHOCKING: Secret Cure for All Diseases REVEALED!"

# Đánh giá độ tin cậy sử dụng LLM
llm_credibility = llm_analyzer.evaluate_credibility(content, title)

# Đánh giá độ tin cậy của URL
result = evaluator.evaluate_url(url, content, title)

# Kết hợp kết quả
combined_score = (result['credibility_score'] + llm_credibility['credibility_score']) / 2
```

## Các tham số quan trọng

### Loại mô hình (model_type)

Tham số `model_type` xác định loại mô hình ngôn ngữ lớn được sử dụng:
- `huggingface`: Sử dụng mô hình từ Hugging Face Transformers (mặc định)
- `openai`: Sử dụng OpenAI API

```python
analyzer = create_llm_analyzer(model_type="huggingface")
```

### Tên mô hình (model_name)

Tham số `model_name` xác định mô hình cụ thể được sử dụng:

Đối với Hugging Face:
- `facebook/bart-large-mnli`: Mô hình BART của Facebook cho phân loại zero-shot (mặc định)
- `roberta-large-mnli`: Mô hình RoBERTa cho phân loại zero-shot
- Các mô hình phân loại zero-shot khác từ Hugging Face

Đối với OpenAI:
- `gpt-3.5-turbo`: Mô hình GPT-3.5 Turbo (mặc định cho OpenAI)
- `gpt-4`: Mô hình GPT-4 (chất lượng cao hơn nhưng đắt hơn)

```python
analyzer = create_llm_analyzer(model_type="huggingface", model_name="roberta-large-mnli")
```

## Sử dụng bộ nhớ cache

Bộ phân tích nội dung LLM có hỗ trợ bộ nhớ cache để tránh việc phân tích lại các nội dung đã được phân tích trước đó:

```python
analyzer = create_llm_analyzer(
    cache_enabled=True,
    cache_dir="data/credibility/llm_cache",
    cache_expiry=604800  # 7 ngày
)
```

## Ví dụ đầy đủ

Xem tệp `examples/llm_content_analyzer_example.py` để biết ví dụ đầy đủ về cách sử dụng bộ phân tích nội dung LLM.

## So sánh với phương pháp truyền thống

Bộ phân tích nội dung LLM có một số ưu điểm so với phương pháp phát hiện tin giả truyền thống:

1. **Phân tích ngữ nghĩa sâu hơn**: Hiểu được ngữ cảnh và ý nghĩa của văn bản, không chỉ dựa trên từ khóa hoặc mẫu đơn giản
2. **Linh hoạt hơn**: Có thể nhận diện các hình thức tin giả mới mà không cần cập nhật quy tắc
3. **Giải thích tốt hơn**: Cung cấp lý do chi tiết hơn về việc tại sao nội dung được đánh giá là tin giả
4. **Phát hiện tinh vi hơn**: Có thể phát hiện các tin giả được viết tinh vi, không sử dụng các dấu hiệu rõ ràng

Tuy nhiên, phương pháp truyền thống vẫn có những ưu điểm riêng:
- Nhanh hơn và tiêu tốn ít tài nguyên hơn
- Không yêu cầu các thư viện phức tạp
- Có thể hoạt động trong môi trường offline hoàn toàn

## Kết hợp các phương pháp

Để có kết quả tốt nhất, nên kết hợp cả hai phương pháp:

```python
# Phát hiện tin giả với phương pháp truyền thống
traditional_result = fake_news_detector.detect_fake_news(content, title)

# Phát hiện tin giả với LLM
llm_result = llm_analyzer.analyze_content(content, title)

# Kết hợp kết quả
traditional_score = 1.0 - traditional_result['fake_news_probability']
llm_score = llm_result['credibility_score']

# Có thể điều chỉnh trọng số tùy theo đánh giá chất lượng của từng phương pháp
combined_score = (traditional_score * 0.3) + (llm_score * 0.7)
```

## Ghi chú

- Bộ phân tích nội dung LLM sẽ hoạt động ở chế độ dự phòng nếu các thư viện cần thiết không có sẵn
- Khi sử dụng OpenAI API, hãy lưu ý đến giới hạn tốc độ và chi phí
- Mô hình Hugging Face có thể yêu cầu tài nguyên đáng kể, đặc biệt là bộ nhớ
- Sử dụng GPU sẽ cải thiện đáng kể tốc độ khi sử dụng mô hình Hugging Face

## Quy trình làm việc điển hình

1. Khởi tạo cả bộ phát hiện tin giả truyền thống và bộ phân tích nội dung LLM
2. Phân tích nội dung đầu vào bằng cả hai phương pháp
3. Kết hợp kết quả phân tích để có đánh giá chính xác hơn
4. Tạo báo cáo chi tiết với cảnh báo và đề xuất từ cả hai phương pháp
5. Sử dụng phản hồi người dùng để cải thiện cả hai hệ thống theo thời gian 