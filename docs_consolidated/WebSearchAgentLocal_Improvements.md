# C<PERSON><PERSON> thiện WebSearchAgentLocal

Tài liệu này mô tả các cải tiến cần thiết cho WebSearchAgentLocal để nâng cao khả năng tìm kiếm mà không cần sử dụng API bên ngoài.

## <PERSON><PERSON><PERSON>

1. [Xử lý CAPTCHA](#1-x<PERSON>-lý-captcha)
2. [Adaptive Scraping](#2-adaptive-scraping)
3. [Triển khai đầy đủ QueryDecomposer](#3-triển-khai-đầy-đủ-querydecomposer)
4. [Triển khai đầy đủ QuestionComplexityEvaluator](#4-triển-khai-đầy-đủ-questioncomplexityevaluator)
5. [Triển khai đầy đủ AnswerQualityEvaluator](#5-triển-khai-đầy-đủ-answerqualityevaluator)
6. [Triển khai đầy đủ AdaptiveCrawler](#6-triển-khai-đầy-đủ-adaptivecrawler)
7. [<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>hancedWebSearchCache](#7-c<PERSON><PERSON>-thi<PERSON>n-enhancedwebsearchcache)
8. [H<PERSON> thống Plugin](#8-hệ-thống-plugin)

## 1. Xử lý CAPTCHA

### Mô tả
Thêm khả năng phát hiện và xử lý CAPTCHA để tránh bị chặn khi tìm kiếm.

### Nhiệm vụ
1. **Thêm phương thức phát hiện CAPTCHA**
   - Tạo phương thức `detect_captcha()` để phát hiện CAPTCHA trong nội dung HTML
   - Sử dụng danh sách từ khóa để nhận diện các loại CAPTCHA phổ biến

2. **Thêm phương thức xử lý CAPTCHA**
   - Tạo phương thức `handle_captcha()` để xử lý CAPTCHA
   - Triển khai các chiến lược xử lý CAPTCHA:
     - Thay đổi User-Agent
     - Đợi và thử lại
     - Chuyển sang phương thức tìm kiếm khác

### Triển khai

```python
def detect_captcha(self, html_content: str) -> bool:
    """
    Phát hiện CAPTCHA trong nội dung HTML.

    Args:
        html_content: Nội dung HTML cần kiểm tra

    Returns:
        True nếu phát hiện CAPTCHA, False nếu không
    """
    if not html_content:
        return False

    # Chuyển đổi thành chữ thường để dễ tìm kiếm
    html_lower = html_content.lower()

    # Các từ khóa CAPTCHA phổ biến
    captcha_keywords = [
        'captcha', 'recaptcha', 'robot', 'human verification',
        'security check', 'prove you are human', 'bot check',
        'are you a robot', 'verify you are a human', 'cloudflare',
        'challenge', 'hcaptcha', 'turnstile'
    ]

    # Kiểm tra các từ khóa
    for keyword in captcha_keywords:
        if keyword in html_lower:
            return True

    return False

def handle_captcha(self, url: str, html_content: Optional[str] = None) -> Dict[str, Any]:
    """
    Xử lý CAPTCHA.

    Args:
        url: URL gặp CAPTCHA
        html_content: Nội dung HTML (tùy chọn)

    Returns:
        Từ điển chứa thông tin xử lý CAPTCHA
    """
    # Ghi nhận thời gian phát hiện CAPTCHA
    self.last_captcha_detection[url] = time.time()

    # Thử các chiến lược xử lý CAPTCHA
    strategies = [
        self._try_different_user_agent,
        self._try_delay_and_retry,
        self._try_alternative_search_method
    ]

    for strategy in strategies:
        result = strategy(url)
        if result.get('success'):
            return result

    # Nếu tất cả chiến lược đều thất bại
    return {
        'success': False,
        'url': url,
        'error': 'All CAPTCHA handling strategies failed',
        'captcha_detected': True
    }
```

## 2. Adaptive Scraping

### Mô tả
Thêm khả năng điều chỉnh chiến lược scraping dựa trên kết quả để tăng khả năng thành công khi trích xuất nội dung.

### Nhiệm vụ
1. **Thêm phương thức adaptive scraping**
   - Tạo phương thức `_adaptive_scrape()` để điều chỉnh chiến lược scraping
   - Triển khai nhiều chiến lược scraping khác nhau:
     - Sử dụng requests thông thường
     - Thay đổi User-Agent
     - Sử dụng Playwright khi cần thiết

2. **Thêm cơ chế xoay vòng User-Agent**
   - Tạo phương thức `_get_rotated_user_agent()` để xoay vòng User-Agent
   - Tạo danh sách User-Agent đa dạng

### Triển khai

```python
def _adaptive_scrape(self, url: str, headers: Optional[Dict[str, str]] = None, 
                    timeout: Optional[int] = None) -> str:
    """
    Scrape một trang web với chiến lược thích ứng.

    Args:
        url: URL cần scrape
        headers: Headers cho request
        timeout: Timeout cho request

    Returns:
        Nội dung đã scrape
    """
    # Khởi tạo headers nếu không được cung cấp
    headers = headers or {
        "User-Agent": self._get_rotated_user_agent(),
    }
    
    # Thử chiến lược 1: Sử dụng requests thông thường
    try:
        response = requests.get(url, headers=headers, timeout=timeout or self.timeout)
        if response.status_code == 200:
            # Kiểm tra CAPTCHA
            if not self.detect_captcha(response.text):
                return response.text
    except Exception as e:
        logger.warning(f"Regular scraping failed: {str(e)}")
    
    # Thử chiến lược 2: Thay đổi User-Agent
    try:
        headers["User-Agent"] = self._get_rotated_user_agent(rotate=True)
        response = requests.get(url, headers=headers, timeout=timeout or self.timeout)
        if response.status_code == 200:
            # Kiểm tra CAPTCHA
            if not self.detect_captcha(response.text):
                return response.text
    except Exception as e:
        logger.warning(f"User-Agent rotation failed: {str(e)}")
    
    # Thử chiến lược 3: Sử dụng Playwright
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page(user_agent=self._get_rotated_user_agent(rotate=True))
            page.goto(url, timeout=timeout or self.timeout * 1000)
            content = page.content()
            browser.close()
            
            # Kiểm tra CAPTCHA
            if not self.detect_captcha(content):
                return content
    except Exception as e:
        logger.warning(f"Playwright scraping failed: {str(e)}")
    
    # Nếu tất cả chiến lược đều thất bại
    return ""

def _get_rotated_user_agent(self, rotate: bool = False) -> str:
    """
    Lấy User-Agent hiện tại hoặc xoay vòng sang User-Agent mới.
    
    Args:
        rotate: Có xoay vòng sang User-Agent mới không
        
    Returns:
        User-Agent string
    """
    if not hasattr(self, 'user_agents'):
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/20100101 Firefox/95.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:95.0) Gecko/20100101 Firefox/95.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36 Edg/96.0.1054.53",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36 OPR/82.0.4227.33"
        ]
    
    if not hasattr(self, 'current_user_agent_index'):
        self.current_user_agent_index = 0
    
    if rotate:
        self.current_user_agent_index = (self.current_user_agent_index + 1) % len(self.user_agents)
    
    return self.user_agents[self.current_user_agent_index]
```

## 3. Triển khai đầy đủ QueryDecomposer

### Mô tả
Triển khai đầy đủ QueryDecomposer để phân tích và chia nhỏ câu hỏi phức tạp thành các câu hỏi đơn giản hơn.

### Nhiệm vụ
1. **Tích hợp QueryDecomposer vào phương thức search**
   - Sử dụng QueryDecomposer để phân tích truy vấn
   - Chia nhỏ câu hỏi phức tạp thành các câu hỏi đơn giản hơn
   - Tìm kiếm với từng câu hỏi con và kết hợp kết quả

2. **Triển khai phương thức decompose_query**
   - Tạo phương thức `decompose_query()` để chia nhỏ câu hỏi
   - Sử dụng các quy tắc và mẫu để chia nhỏ câu hỏi

### Triển khai

```python
def decompose_query(
    self,
    query: str,
    max_sub_queries: int = 5,
    min_sub_queries: int = 2,
) -> List[Dict[str, Any]]:
    """
    Chia nhỏ câu hỏi phức tạp thành các câu hỏi đơn giản hơn.

    Args:
        query: Câu hỏi cần chia nhỏ
        max_sub_queries: Số câu hỏi con tối đa
        min_sub_queries: Số câu hỏi con tối thiểu

    Returns:
        Danh sách các câu hỏi con
    """
    if not self.use_query_decomposer or not hasattr(self, 'query_decomposer'):
        return [{"query": query, "is_original": True, "complexity": "unknown"}]

    try:
        # Sử dụng QueryDecomposer để chia câu hỏi
        sub_queries = self.query_decomposer.decompose(
            query=query,
            max_sub_queries=max_sub_queries,
            min_sub_queries=min_sub_queries,
        )

        logger.info(f"Decomposed query into {len(sub_queries)} sub-queries")
        return sub_queries
    except Exception as e:
        logger.error(f"Error decomposing query: {str(e)}")
        return [{"query": query, "is_original": True, "complexity": "unknown"}]
```

Trong phương thức `search()`, thêm đoạn code sau để sử dụng QueryDecomposer:

```python
# Nếu câu hỏi phức tạp và sử dụng query decomposer
if evaluate_question and hasattr(self, 'question_complexity_evaluator'):
    question_evaluation = self.question_complexity_evaluator.evaluate_complexity(query)
    if question_evaluation.get("complexity_level") == "high" and self.use_query_decomposer:
        # Chia nhỏ câu hỏi
        sub_queries = self.decompose_query(
            query=query,
            max_sub_queries=5,
            min_sub_queries=2,
        )
        
        # Tìm kiếm với từng câu hỏi con
        all_results = []
        for sub_query in sub_queries:
            sub_query_text = sub_query.get("query", "")
            if sub_query_text:
                sub_results = self._search_with_method(
                    query=sub_query_text,
                    num_results=max(3, num_results // len(sub_queries)),
                    method=search_method,
                    language=language,
                    get_content=get_content,
                    force_refresh=force_refresh,
                    optimize_for_llm=optimize_for_llm,
                    max_content_length=max_content_length,
                    **kwargs,
                )
                
                if sub_results.get("success") and sub_results.get("results"):
                    all_results.extend(sub_results.get("results", []))
        
        # Kết hợp kết quả
        if all_results:
            # Sắp xếp và lọc kết quả
            unique_results = []
            seen_urls = set()
            
            for result in all_results:
                url = result.get("url", "")
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_results.append(result)
            
            # Giới hạn số lượng kết quả
            unique_results = unique_results[:num_results]
            
            # Trả về kết quả
            return {
                "success": True,
                "query": query,
                "results": unique_results,
                "engine": search_method,
                "search_method": "local",
                "timestamp": time.time(),
                "decomposed": True,
                "sub_queries": [sq.get("query", "") for sq in sub_queries],
                "question_evaluation": question_evaluation
            }
```
