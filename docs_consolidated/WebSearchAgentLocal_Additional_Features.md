# Cá<PERSON> tính năng và plugin bổ sung cho WebSearchAgentLocal

Tài liệu này liệt kê các tính năng và plugin bổ sung có thể thêm vào WebSearchAgentLocal để cải thiện chức năng và hiệu suất.

## 1. Các plugin đã có sẵn trong dự án

### 1.1. ContentFilterPlugin

**Mô tả**: Plugin lọc nội dung không phù hợp, spam và quảng cáo từ kết quả tìm kiếm.

**File nguồn**: `src/deep_research_core/agents/plugins/content_filter_plugin.py`

**Cách tích hợp**:
```python
from src.deep_research_core.agents.plugins.content_filter_plugin import ContentFilterPlugin

# Khởi tạo plugin
content_filter_plugin = ContentFilterPlugin({
    "min_content_length": 50,
    "max_spam_score": 0.7,
    "max_ad_score": 0.8,
    "enable_inappropriate_filter": True,
    "enable_spam_filter": True,
    "enable_ad_filter": True
})

# Đăng ký plugin với WebSearchAgentLocal
agent.register_plugin(content_filter_plugin)
```

**Lợi ích**: Cải thiện chất lượng kết quả tìm kiếm bằng cách loại bỏ nội dung không phù hợp, spam và quảng cáo.

### 1.2. MultilingualPlugin

**Mô tả**: Plugin hỗ trợ đa ngôn ngữ, phát hiện ngôn ngữ tự động, dịch truy vấn và kết quả.

**File nguồn**: `src/deep_research_core/agents/plugins/multilingual_plugin.py`

**Cách tích hợp**:
```python
from src.deep_research_core.agents.plugins.multilingual_plugin import MultilingualPlugin

# Khởi tạo plugin
multilingual_plugin = MultilingualPlugin({
    "default_language": "en",
    "supported_languages": ["en", "vi", "fr", "de", "es", "zh", "ja", "ko", "ru"],
    "auto_detect": True,
    "translate_query": True,
    "translate_results": True,
    "translate_content": False
})

# Đăng ký plugin với WebSearchAgentLocal
agent.register_plugin(multilingual_plugin)
```

**Lợi ích**: Cải thiện khả năng tìm kiếm đa ngôn ngữ, đặc biệt là tiếng Việt.

### 1.3. QueryOptimizationPlugin

**Mô tả**: Plugin tối ưu hóa truy vấn tìm kiếm để cải thiện kết quả.

**Cách tích hợp**: Cần tìm file nguồn cụ thể hoặc tạo mới dựa trên các module tối ưu hóa truy vấn hiện có.

**Lợi ích**: Cải thiện chất lượng kết quả tìm kiếm bằng cách tối ưu hóa truy vấn.

### 1.4. ResultRankingPlugin

**Mô tả**: Plugin xếp hạng kết quả tìm kiếm dựa trên độ phù hợp.

**Cách tích hợp**: Cần tìm file nguồn cụ thể hoặc tạo mới dựa trên các module xếp hạng kết quả hiện có.

**Lợi ích**: Cải thiện chất lượng kết quả tìm kiếm bằng cách xếp hạng kết quả theo độ phù hợp.

### 1.5. PerformanceOptimizationPlugin

**Mô tả**: Plugin tối ưu hóa hiệu suất của WebSearchAgentLocal.

**Cách tích hợp**: Cần tìm file nguồn cụ thể hoặc tạo mới dựa trên các module tối ưu hóa hiệu suất hiện có.

**Lợi ích**: Cải thiện hiệu suất của WebSearchAgentLocal khi xử lý nhiều truy vấn.

## 2. Các tính năng nâng cao khác

### 2.1. Hỗ trợ tìm kiếm tiếng Việt nâng cao

**Mô tả**: Tích hợp các phương thức tìm kiếm tiếng Việt như `search_coccoc`, `search_wikitiengviet`, `search_baomoi`.

**File nguồn**: Các phương thức này đã được tìm thấy trong `src/deep_research_core/agents/vietnamese_search_methods.py`.

**Cách tích hợp**:
```python
from src.deep_research_core.agents.vietnamese_search_methods import search_coccoc, search_wikitiengviet, search_baomoi

# Thêm các phương thức tìm kiếm tiếng Việt vào WebSearchAgentLocal
def search_vietnamese(self, query, num_results=10, **kwargs):
    """
    Tìm kiếm tiếng Việt với các công cụ tìm kiếm Việt Nam.
    """
    # Thử Cốc Cốc
    results = search_coccoc(query=query, num_results=num_results)
    if results.get("success") and results.get("results"):
        return results
    
    # Thử Wiki tiếng Việt
    results = search_wikitiengviet(query=query, num_results=num_results)
    if results.get("success") and results.get("results"):
        return results
    
    # Thử Báo Mới
    results = search_baomoi(query=query, num_results=num_results)
    return results

# Thêm phương thức vào WebSearchAgentLocal
WebSearchAgentLocal.search_vietnamese = search_vietnamese
```

**Lợi ích**: Cải thiện kết quả tìm kiếm tiếng Việt.

### 2.2. Tối ưu hóa bộ nhớ

**Mô tả**: Áp dụng các kỹ thuật tối ưu hóa bộ nhớ để cải thiện hiệu suất.

**File nguồn**: Các module tối ưu hóa bộ nhớ trong `src/deep_research_core/optimization/memory`.

**Cách tích hợp**: Cần nghiên cứu thêm để tích hợp các kỹ thuật tối ưu hóa bộ nhớ vào WebSearchAgentLocal.

**Lợi ích**: Cải thiện hiệu suất của WebSearchAgentLocal khi xử lý nhiều truy vấn.

### 2.3. Đánh giá chất lượng kết quả tìm kiếm

**Mô tả**: Tích hợp các module đánh giá chất lượng kết quả tìm kiếm.

**File nguồn**: Các module đánh giá chất lượng kết quả trong `src/deep_research_core/agents/web_search_feedback_learner.py`.

**Cách tích hợp**: Cần nghiên cứu thêm để tích hợp các module đánh giá chất lượng kết quả vào WebSearchAgentLocal.

**Lợi ích**: Cải thiện chất lượng kết quả tìm kiếm.

### 2.4. Caching thông minh

**Mô tả**: Tích hợp các module caching thông minh để cải thiện tốc độ tìm kiếm.

**File nguồn**: Các module caching thông minh đã được tìm thấy trong dự án.

**Cách tích hợp**: Cần nghiên cứu thêm để tích hợp các module caching thông minh vào WebSearchAgentLocal.

**Lợi ích**: Cải thiện tốc độ tìm kiếm.

## 3. Các plugin SearXNG có thể kích hoạt

### 3.1. Hostname replace

**Mô tả**: Plugin thay thế hostname trong kết quả tìm kiếm.

**Cách kích hoạt**: Thêm vào danh sách `enabled_plugins` trong file cấu hình SearXNG.

```yaml
enabled_plugins:
  - 'Hash plugin'
  - 'Search on category select'
  - 'Self Informations'
  - 'Tracker URL remover'
  - 'Vim-like hotkeys'
  - 'Hostname replace'  # Thêm plugin này
```

**Lợi ích**: Cải thiện khả năng đọc của URL trong kết quả tìm kiếm.

### 3.2. Open Access DOI rewrite

**Mô tả**: Plugin viết lại DOI để truy cập mở.

**Cách kích hoạt**: Thêm vào danh sách `enabled_plugins` trong file cấu hình SearXNG.

```yaml
enabled_plugins:
  - 'Hash plugin'
  - 'Search on category select'
  - 'Self Informations'
  - 'Tracker URL remover'
  - 'Vim-like hotkeys'
  - 'Open Access DOI rewrite'  # Thêm plugin này
```

**Lợi ích**: Cải thiện khả năng truy cập các bài báo học thuật.

### 3.3. Infinite scroll

**Mô tả**: Plugin cho phép cuộn vô hạn kết quả tìm kiếm.

**Cách kích hoạt**: Thêm vào danh sách `enabled_plugins` trong file cấu hình SearXNG.

```yaml
enabled_plugins:
  - 'Hash plugin'
  - 'Search on category select'
  - 'Self Informations'
  - 'Tracker URL remover'
  - 'Vim-like hotkeys'
  - 'Infinite scroll'  # Thêm plugin này
```

**Lợi ích**: Cải thiện trải nghiệm người dùng khi xem kết quả tìm kiếm.

## Kết luận

Các tính năng và plugin bổ sung này có thể giúp cải thiện đáng kể chức năng và hiệu suất của WebSearchAgentLocal. Tùy thuộc vào nhu cầu cụ thể, bạn có thể chọn tích hợp một hoặc nhiều tính năng và plugin này.
