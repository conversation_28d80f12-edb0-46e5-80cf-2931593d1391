# Hướng dẫn sử dụng các tính năng đánh giá

## Giới thiệu

Tài liệu này mô tả cách sử dụng các tính năng đánh giá mới được thêm vào WebSearchAgentLocalMerged.

## <PERSON><PERSON><PERSON> tính năng chính

### 1. <PERSON><PERSON><PERSON> giá chất lượng câu trả lời

```python
from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged

# Khởi tạo agent
agent = WebSearchAgentLocalMerged()

# Đánh giá chất lượng câu trả lời
answer = "Hà Nội là thủ đô của Việt Nam, được thành lập năm 1010."
query = "Giới thiệu về Hà Nội"

result = agent.evaluate_answer_quality(answer, query)

# Kết quả trả về là một dictionary chứa thông tin đánh giá
print(f"Điểm chất lượng: {result['quality_score']}")
print(f"Điểm chính xác: {result['accuracy_score']}")
print(f"Điểm đầy đủ: {result['completeness_score']}")
print(f"Điểm liên quan: {result['relevance_score']}")
print(f"Điểm rõ ràng: {result['clarity_score']}")
print(f"Điểm súc tích: {result['conciseness_score']}")
print(f"Giải thích: {result['explanation']}")
print(f"Điểm mạnh: {result['strengths']}")
print(f"Điểm yếu: {result['weaknesses']}")
print(f"Gợi ý: {result['suggestions']}")
```

#### Các tham số

- `answer` (str): Nội dung câu trả lời cần đánh giá
- `query` (str, optional): Truy vấn gốc, giúp đánh giá tính liên quan của câu trả lời

#### Kết quả trả về

Một dictionary chứa các thông tin đánh giá:

- `quality_score`: Điểm chất lượng tổng thể (0.0-1.0)
- `accuracy_score`: Điểm chính xác của thông tin (0.0-1.0)
- `completeness_score`: Điểm đầy đủ của nội dung (0.0-1.0)
- `relevance_score`: Điểm liên quan đến truy vấn (0.0-1.0)
- `clarity_score`: Điểm rõ ràng của câu trả lời (0.0-1.0)
- `conciseness_score`: Điểm súc tích của câu trả lời (0.0-1.0)
- `explanation`: Giải thích về đánh giá
- `strengths`: Danh sách các điểm mạnh
- `weaknesses`: Danh sách các điểm yếu
- `suggestions`: Danh sách các gợi ý cải thiện

### 2. Đánh giá độ phức tạp của câu hỏi

```python
from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged

# Khởi tạo agent
agent = WebSearchAgentLocalMerged()

# Đánh giá độ phức tạp của câu hỏi
query = "So sánh kinh tế Việt Nam và Thái Lan trong 10 năm qua"

result = agent.evaluate_question_complexity(query)

# Kết quả trả về là một dictionary chứa thông tin đánh giá
print(f"Mức độ phức tạp: {result['complexity_level']}")
print(f"Điểm phức tạp: {result['complexity_score']}")
print(f"Loại câu hỏi: {result['question_type']}")
print(f"Đặc điểm câu hỏi: {result['question_characteristics']}")
print(f"Thực thể: {result['entities']}")
print(f"Từ khóa: {result['keywords']}")
print(f"Chiến lược đề xuất: {result['recommended_strategy']}")
```

#### Các tham số

- `query` (str): Câu hỏi cần đánh giá

#### Kết quả trả về

Một dictionary chứa các thông tin đánh giá:

- `complexity_level`: Mức độ phức tạp ("low", "medium", "high")
- `complexity_score`: Điểm phức tạp (0.0-1.0)
- `question_type`: Loại câu hỏi (comparative, causal, procedural, etc.)
- `question_characteristics`: Danh sách các đặc điểm của câu hỏi
- `entities`: Danh sách các thực thể được trích xuất từ câu hỏi
- `keywords`: Danh sách các từ khóa chính
- `recommended_strategy`: Chiến lược tìm kiếm đề xuất

## Ví dụ thực tế

### Đánh giá độ phức tạp của câu hỏi và tìm kiếm với chiến lược phù hợp

```python
from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged

# Khởi tạo agent
agent = WebSearchAgentLocalMerged()

# Đánh giá độ phức tạp của câu hỏi
query = "Tại sao rừng Amazon đang bị đe dọa và những tác động đến khí hậu toàn cầu?"
complexity_result = agent.evaluate_question_complexity(query)

# Tìm kiếm với các tham số được đề xuất từ đánh giá độ phức tạp
strategy = complexity_result["recommended_strategy"]
search_results = agent.search(
    query=query,
    max_depth=strategy.get("max_depth", 2),
    max_pages=strategy.get("max_pages", 5),
    deep_crawl=strategy.get("need_decomposition", False)
)

# Đánh giá chất lượng kết quả tìm kiếm
if search_results and search_results.get("content"):
    content = search_results["content"]
    quality_result = agent.evaluate_answer_quality(content, query)
    
    # Hiển thị kết quả đánh giá
    print(f"Điểm chất lượng: {quality_result['quality_score']}")
    print(f"Điểm mạnh: {quality_result['strengths']}")
    print(f"Điểm yếu: {quality_result['weaknesses']}")
    print(f"Gợi ý cải thiện: {quality_result['suggestions']}")
```

## Chú ý

1. Các phương thức đánh giá có thể sử dụng nhiều tài nguyên hệ thống, đặc biệt là khi xử lý văn bản lớn.
2. Kết quả đánh giá có thể thay đổi tùy thuộc vào ngôn ngữ, loại câu hỏi, và chất lượng nội dung.
3. Các phương thức này hoạt động tốt nhất khi được sử dụng cùng với các phương thức khác trong WebSearchAgentLocalMerged.

## Các tính năng sắp tới

Trong tương lai, chúng tôi dự định thêm các tính năng sau:

1. Tích hợp Vector Database để cải thiện đánh giá độ liên quan ngữ nghĩa
2. Cải thiện hỗ trợ đa ngôn ngữ
3. Tối ưu hóa hiệu suất bằng asyncio
4. Tích hợp các mô hình LLM mới để cải thiện đánh giá chất lượng 