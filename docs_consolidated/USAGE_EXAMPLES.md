# 🚀 WebSearchAgentLocalMerged Usage Examples

## 📋 Table of Contents

1. [Basic Usage](#basic-usage)
2. [Advanced Search](#advanced-search)
3. [Performance Optimization](#performance-optimization)
4. [Multi-language Support](#multi-language-support)
5. [Batch Processing](#batch-processing)
6. [Error Handling](#error-handling)
7. [Real-world Use Cases](#real-world-use-cases)

## 🔍 Basic Usage

### Simple Search

```python
from deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged

# Initialize agent
agent = WebSearchAgentLocalMerged(verbose=True)

# Basic search
results = agent.search("Python programming tutorial")

# Display results
for i, result in enumerate(results['results'], 1):
    print(f"{i}. {result['title']}")
    print(f"   URL: {result['url']}")
    print(f"   Source: {result['source']}")
    print()
```

### Search with Content Extraction

```python
# Search with full content extraction
results = agent.search(
    query="machine learning algorithms",
    num_results=5,
    get_content=True
)

# Access full content
for result in results['results']:
    print(f"Title: {result['title']}")
    print(f"Content: {result.get('content', 'No content')[:200]}...")
    print(f"Word count: {len(result.get('content', '').split())}")
    print("-" * 50)
```

## 🎯 Advanced Search

### Comprehensive Search with Evaluation

```python
# Advanced search with all features
results = agent.search(
    query="climate change effects on agriculture",
    num_results=20,
    get_content=True,
    evaluate_question=True,
    evaluate_answer=True,
    use_deep_research=True
)

# Access evaluation results
if 'question_evaluation' in results:
    complexity = results['question_evaluation']['complexity_score']
    intent = results['question_evaluation']['intent']
    print(f"Question complexity: {complexity:.2f}")
    print(f"Query intent: {intent}")

if 'answer_evaluation' in results:
    quality = results['answer_evaluation']['quality_score']
    completeness = results['answer_evaluation']['completeness']
    print(f"Answer quality: {quality:.2f}")
    print(f"Completeness: {completeness:.2f}")

# Access simple answer
if 'simple_answer' in results:
    print(f"Summary: {results['simple_answer']}")
```

### Search with Custom Configuration

```python
# Custom agent configuration
agent = WebSearchAgentLocalMerged(
    verbose=True,
    timeout=45,
    max_retries=5,
    use_cache=True,
    cache_ttl=7200,  # 2 hours
    enable_performance_optimization=True
)

# Search with specific parameters
results = agent.search(
    query="artificial intelligence trends 2024",
    num_results=30,
    get_content=True,
    max_content_length=5000
)
```

## ⚡ Performance Optimization

### Enable Performance Features

```python
# Initialize with performance optimization
agent = WebSearchAgentLocalMerged(verbose=True)

# Optimize performance settings
agent.optimize_performance(
    enable_compression=True,
    enable_keep_alive=True,
    max_pool_connections=20
)

# Monitor performance
stats = agent.get_performance_stats()
print(f"Uptime: {stats['uptime_seconds']:.1f}s")
print(f"Success rate: {stats['success_rate']:.1f}%")
print(f"Requests per second: {stats['requests_per_second']:.2f}")
print(f"Cache hit rate: {stats['cache_stats']['hit_rate']:.1f}%")
```

### Cache Management

```python
# Check cache stats
cache_stats = agent.get_cache_stats()
print(f"Cache size: {cache_stats['size']} items")
print(f"Hit rate: {cache_stats['hit_rate']:.1f}%")
print(f"Memory usage: {cache_stats['memory_usage_mb']:.1f} MB")

# Clear cache if needed
if cache_stats['size'] > 1000:
    agent.clear_cache()
    print("Cache cleared")
```

## 🌍 Multi-language Support

### Vietnamese Language Search

```python
# Vietnamese search
results = agent.search(
    query="lập trình Python cơ bản",
    num_results=10,
    get_content=True
)

# Process Vietnamese content
for result in results['results']:
    # Detect language
    language = agent.detect_content_language(result.get('content', ''))
    
    if language == 'vi':
        # Extract Vietnamese keywords
        keywords = agent.extract_keywords_multilingual(
            result.get('content', ''), 'vi'
        )
        print(f"🇻🇳 {result['title']}")
        print(f"Keywords: {', '.join(keywords[:5])}")
        
        # Normalize content
        normalized = agent.normalize_content_language(
            result.get('content', ''), 'vi'
        )
        print(f"Normalized: {normalized[:100]}...")
        print()
```

### Multi-language Query Optimization

```python
# Optimize queries for different languages
queries = [
    "machine learning tutorial",
    "học máy cơ bản",
    "apprentissage automatique"
]

for query in queries:
    # Detect query language and optimize
    optimized = agent.optimize_query(query)
    
    # Generate alternatives
    alternatives = agent.generate_alternative_queries(query, 3)
    
    print(f"Original: {query}")
    print(f"Optimized: {optimized}")
    print(f"Alternatives: {alternatives}")
    print("-" * 40)
```

## 📦 Batch Processing

### Process Multiple URLs

```python
# List of URLs to process
urls = [
    "https://docs.python.org/3/",
    "https://pytorch.org/docs/",
    "https://scikit-learn.org/stable/",
    "https://pandas.pydata.org/docs/",
    "https://numpy.org/doc/"
]

# Batch process with optimization
results = agent.batch_process_urls(
    urls=urls,
    batch_size=3,
    delay_between_batches=2.0
)

# Process results
for i, result in enumerate(results):
    print(f"URL {i+1}: {result.get('url', 'Unknown')}")
    print(f"Status: {result.get('status', 'Unknown')}")
    print(f"Content length: {len(result.get('content', ''))}")
    print()
```

### Bulk Search Queries

```python
# Multiple search queries
queries = [
    "Python web development",
    "React.js best practices",
    "Docker containerization",
    "Kubernetes deployment",
    "Machine learning pipelines"
]

all_results = []

for query in queries:
    print(f"Searching: {query}")
    
    # Use adaptive timeout
    timeout = agent.adaptive_timeout(query)
    
    results = agent.search(
        query=query,
        num_results=5,
        get_content=False
    )
    
    all_results.append({
        'query': query,
        'results': results['results'],
        'search_time': results['metadata']['search_time']
    })
    
    # Brief pause between searches
    time.sleep(1)

# Analyze results
total_results = sum(len(r['results']) for r in all_results)
avg_time = sum(r['search_time'] for r in all_results) / len(all_results)

print(f"\nSummary:")
print(f"Total queries: {len(queries)}")
print(f"Total results: {total_results}")
print(f"Average search time: {avg_time:.2f}s")
```

## 🚨 Error Handling

### Robust Error Handling

```python
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

def safe_search(agent, query, max_attempts=3):
    """Perform search with error handling and retries."""
    
    for attempt in range(max_attempts):
        try:
            results = agent.search(
                query=query,
                num_results=10,
                get_content=True
            )
            
            if results['success']:
                return results
            else:
                print(f"Search failed: {results.get('error', 'Unknown error')}")
                
        except ConnectionError as e:
            print(f"Connection error (attempt {attempt + 1}): {e}")
            if attempt < max_attempts - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
                
        except TimeoutError as e:
            print(f"Timeout error (attempt {attempt + 1}): {e}")
            if attempt < max_attempts - 1:
                time.sleep(1)
                
        except Exception as e:
            print(f"Unexpected error: {e}")
            break
    
    return {'success': False, 'results': [], 'error': 'All attempts failed'}

# Usage
agent = WebSearchAgentLocalMerged(verbose=True)
results = safe_search(agent, "Python programming")

if results['success']:
    print(f"Found {len(results['results'])} results")
else:
    print("Search failed after all attempts")
```

### Health Check and Fallback

```python
# Check search engine health before searching
def check_search_engines(agent):
    """Check health of available search engines."""
    
    engines_status = {}
    
    # Check SearXNG local
    local_healthy = agent._check_searxng_health("http://localhost:8080")
    engines_status['searxng_local'] = local_healthy
    
    # Check if we can reach public instances
    try:
        import requests
        response = requests.get("https://searx.be", timeout=5)
        engines_status['searxng_public'] = response.status_code == 200
    except:
        engines_status['searxng_public'] = False
    
    return engines_status

# Usage
agent = WebSearchAgentLocalMerged(verbose=True)
status = check_search_engines(agent)

print("Search engines status:")
for engine, healthy in status.items():
    status_icon = "✅" if healthy else "❌"
    print(f"{status_icon} {engine}: {'Healthy' if healthy else 'Unavailable'}")

# Proceed with search only if at least one engine is available
if any(status.values()):
    results = agent.search("Python tutorial")
else:
    print("No search engines available")
```

## 🎯 Real-world Use Cases

### Academic Research Assistant

```python
def research_assistant(topic, depth="comprehensive"):
    """Academic research assistant using WebSearchAgent."""
    
    agent = WebSearchAgentLocalMerged(
        verbose=True,
        enable_credibility_evaluation=True
    )
    
    # Generate research queries
    base_queries = [
        f"{topic} research papers",
        f"{topic} academic studies",
        f"{topic} recent developments",
        f"{topic} literature review"
    ]
    
    all_sources = []
    
    for query in base_queries:
        results = agent.search(
            query=query,
            num_results=15,
            get_content=True,
            evaluate_question=True
        )
        
        # Filter high-credibility sources
        credible_sources = [
            r for r in results['results']
            if r.get('credibility_score', 0) > 0.7
        ]
        
        all_sources.extend(credible_sources)
    
    # Remove duplicates and sort by credibility
    unique_sources = {s['url']: s for s in all_sources}.values()
    sorted_sources = sorted(
        unique_sources,
        key=lambda x: x.get('credibility_score', 0),
        reverse=True
    )
    
    return {
        'topic': topic,
        'total_sources': len(sorted_sources),
        'high_credibility_sources': sorted_sources[:10],
        'summary': f"Found {len(sorted_sources)} credible sources on {topic}"
    }

# Usage
research = research_assistant("quantum computing algorithms")
print(research['summary'])

for i, source in enumerate(research['high_credibility_sources'][:5], 1):
    print(f"{i}. {source['title']}")
    print(f"   Credibility: {source.get('credibility_score', 0):.2f}")
    print(f"   URL: {source['url']}")
    print()
```

### News Monitoring System

```python
def news_monitor(keywords, hours_back=24):
    """Monitor news for specific keywords."""
    
    agent = WebSearchAgentLocalMerged(
        verbose=False,
        use_cache=False  # Always get fresh news
    )
    
    news_results = []
    
    for keyword in keywords:
        # Search for recent news
        query = f"{keyword} news {hours_back} hours"
        
        results = agent.search(
            query=query,
            num_results=20,
            get_content=True
        )
        
        # Filter recent results
        for result in results['results']:
            if 'published_date' in result:
                news_results.append({
                    'keyword': keyword,
                    'title': result['title'],
                    'url': result['url'],
                    'published': result['published_date'],
                    'source': result['source']
                })
    
    # Sort by publication date
    news_results.sort(
        key=lambda x: x.get('published', ''),
        reverse=True
    )
    
    return news_results

# Usage
keywords = ["artificial intelligence", "machine learning", "deep learning"]
news = news_monitor(keywords, hours_back=12)

print(f"Found {len(news)} recent news articles:")
for article in news[:10]:
    print(f"📰 {article['title']}")
    print(f"   Keyword: {article['keyword']}")
    print(f"   Published: {article.get('published', 'Unknown')}")
    print()
```

### Competitive Intelligence

```python
def competitive_analysis(company_name, competitors):
    """Analyze company and competitors using web search."""
    
    agent = WebSearchAgentLocalMerged(
        verbose=True,
        enable_deep_research=True
    )
    
    analysis = {
        'company': company_name,
        'competitors': {},
        'market_trends': []
    }
    
    # Analyze main company
    company_results = agent.search(
        query=f"{company_name} company analysis financial performance",
        num_results=25,
        get_content=True,
        use_deep_research=True
    )
    
    analysis['company_data'] = {
        'search_results': len(company_results['results']),
        'summary': company_results.get('simple_answer', ''),
        'key_sources': company_results['results'][:5]
    }
    
    # Analyze competitors
    for competitor in competitors:
        comp_results = agent.search(
            query=f"{competitor} vs {company_name} comparison",
            num_results=15,
            get_content=True
        )
        
        analysis['competitors'][competitor] = {
            'comparison_results': len(comp_results['results']),
            'summary': comp_results.get('simple_answer', ''),
            'top_results': comp_results['results'][:3]
        }
    
    # Market trends analysis
    trends_results = agent.search(
        query=f"{company_name} industry trends market analysis 2024",
        num_results=20,
        get_content=True,
        evaluate_answer=True
    )
    
    analysis['market_trends'] = {
        'trends_summary': trends_results.get('simple_answer', ''),
        'quality_score': trends_results.get('answer_evaluation', {}).get('quality_score', 0),
        'key_trends': trends_results['results'][:5]
    }
    
    return analysis

# Usage
analysis = competitive_analysis(
    company_name="OpenAI",
    competitors=["Anthropic", "Google DeepMind", "Microsoft AI"]
)

print(f"Competitive Analysis for {analysis['company']}:")
print(f"Company data sources: {analysis['company_data']['search_results']}")
print(f"Competitors analyzed: {len(analysis['competitors'])}")
print(f"Market trends quality: {analysis['market_trends']['quality_score']:.2f}")
```

## 🧹 Cleanup and Best Practices

### Proper Resource Management

```python
def search_with_cleanup(query):
    """Example of proper resource management."""
    
    agent = None
    try:
        # Initialize agent
        agent = WebSearchAgentLocalMerged(verbose=True)
        
        # Perform search
        results = agent.search(query, num_results=10)
        
        return results
        
    except Exception as e:
        print(f"Error during search: {e}")
        return {'success': False, 'error': str(e)}
        
    finally:
        # Always cleanup resources
        if agent:
            agent.cleanup_resources()
            print("Resources cleaned up")

# Usage
results = search_with_cleanup("Python programming")
```

### Context Manager Pattern

```python
class WebSearchContext:
    """Context manager for WebSearchAgent."""
    
    def __init__(self, **kwargs):
        self.kwargs = kwargs
        self.agent = None
    
    def __enter__(self):
        self.agent = WebSearchAgentLocalMerged(**self.kwargs)
        return self.agent
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.agent:
            self.agent.cleanup_resources()

# Usage with context manager
with WebSearchContext(verbose=True) as agent:
    results = agent.search("Python tutorial")
    print(f"Found {len(results['results'])} results")
# Resources automatically cleaned up
```
