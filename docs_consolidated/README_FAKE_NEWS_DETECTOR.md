# Bộ Phát Hiện Tin Giả Sử Dụng Học Máy

Tài liệu này mô tả cách sử dụng và tích hợp bộ phát hiện tin giả (Fake News Detector) để cải thiện độ chính xác của đánh giá độ tin cậy nguồn thông tin.

## Tổng quan

Bộ phát hiện tin giả sử dụng các mô hình học máy để phân tích nội dung và xác định xác suất tin giả, giúp cải thiện độ chính xác của hệ thống đánh giá độ tin cậy. Hệ thống này hoạt động dựa trên các nguyên tắc:

1. <PERSON>ân tích nội dung dựa trên các đặc trưng văn bản (từ ngữ, ngữ cảnh, văn phong)
2. Sử dụng mô hình học máy để dự đoán xác suất tin giả
3. <PERSON><PERSON><PERSON> hợ<PERSON> kết quả phát hiện tin giả vào đánh giá độ tin cậy tổng thể
4. Cung cấp các chỉ báo và cảnh báo cụ thể về nội dung đáng ngờ

## Cài đặt

Bộ phát hiện tin giả yêu cầu các thư viện Python sau để sử dụng đầy đủ tính năng:

```
scikit-learn>=0.24.0
nltk>=3.6.0
joblib>=1.0.1
```

Bạn có thể cài đặt các thư viện này bằng pip:

```bash
pip install scikit-learn nltk joblib
```

Sau khi cài đặt NLTK, cần tải các tài nguyên ngôn ngữ:

```python
import nltk
nltk.download('punkt')
nltk.download('stopwords')
nltk.download('wordnet')
```

## Cấu trúc dữ liệu

Bộ phát hiện tin giả lưu trữ mô hình và dữ liệu huấn luyện trong thư mục có cấu trúc sau:

```
data/credibility/
├── models/
│   ├── fake_news_random_forest.pkl     # Mô hình học máy
│   └── vectorizer_tfidf.pkl           # Bộ chuyển đổi đặc trưng
└── training/
    └── default_training_data.json     # Dữ liệu huấn luyện mặc định
```

## Sử dụng cơ bản

### Khởi tạo bộ phát hiện tin giả

```python
from src.deep_research_core.utils.fake_news_detector import create_fake_news_detector

# Khởi tạo bộ phát hiện tin giả
detector = create_fake_news_detector(
    model_dir="data/credibility/models",
    verbose=True
)
```

### Phát hiện tin giả từ nội dung

```python
# Phát hiện tin giả
content = "Scientists have discovered a MIRACLE cure that can treat ALL diseases, but the government and Big Pharma are HIDING it from you!"
title = "SHOCKING: Secret Cure for All Diseases REVEALED!"

result = detector.detect_fake_news(content, title)

# Kiểm tra kết quả
print(f"Xác suất tin giả: {result['fake_news_probability']:.2f}")
print(f"Là tin giả: {result['is_fake_news']}")
```

### Đánh giá độ tin cậy dựa trên phát hiện tin giả

```python
# Đánh giá độ tin cậy
credibility = detector.evaluate_content_credibility(content, title)

print(f"Điểm tin cậy: {credibility['credibility_score']:.2f}")
print(f"Mức độ tin cậy: {credibility['credibility_level']}")
```

### Huấn luyện mô hình mới

```python
# Tạo dữ liệu huấn luyện mặc định
detector.create_default_training_data()

# Tải dữ liệu huấn luyện
training_data = detector.load_training_data()

# Huấn luyện mô hình
if training_data:
    detector.train_model(training_data)

# Lưu mô hình
detector.save_model()
```

## Tích hợp với CredibilityEvaluator

Bộ phát hiện tin giả có thể được tích hợp với `CredibilityEvaluator` để cải thiện đánh giá độ tin cậy:

```python
from src.deep_research_core.utils.credibility_evaluator import CredibilityEvaluator
from src.deep_research_core.utils.fake_news_detector import create_fake_news_detector

# Khởi tạo CredibilityEvaluator
evaluator = CredibilityEvaluator()

# Khởi tạo FakeNewsDetector
detector = create_fake_news_detector()

# Đánh giá URL với nội dung
url = "https://example.com/article"
content = "BOMBSHELL DISCOVERY!!! Scientists have found a MIRACLE CURE..."
title = "SHOCKING: Secret Cure for All Diseases REVEALED!"

# Phát hiện tin giả
fake_news_result = detector.detect_fake_news(content, title)

# Đánh giá độ tin cậy của URL
result = evaluator.evaluate_url(url, content, title)

# Xem báo cáo chi tiết
report = evaluator.get_credibility_report(url, content, title)
```

## Các tham số quan trọng

### Loại mô hình (model_type)

Tham số `model_type` xác định loại mô hình học máy được sử dụng:
- `random_forest`: Random Forest Classifier (mặc định)
- `logistic_regression`: Logistic Regression
- `svm`: Support Vector Machine
- `naive_bayes`: Multinomial Naive Bayes
- `gradient_boosting`: Gradient Boosting Classifier

```python
detector = create_fake_news_detector(model_type="gradient_boosting")
```

### Loại đặc trưng (feature_type)

Tham số `feature_type` xác định cách trích xuất đặc trưng từ văn bản:
- `tfidf`: TF-IDF Vectorizer (mặc định)
- `count`: Count Vectorizer
- `custom`: Sử dụng đặc trưng tùy chỉnh

```python
detector = create_fake_news_detector(feature_type="tfidf")
```

## Ví dụ đầy đủ

Xem tệp `examples/fake_news_detection_example.py` để biết ví dụ đầy đủ về cách sử dụng bộ phát hiện tin giả.

## Dữ liệu huấn luyện

### Cấu trúc dữ liệu huấn luyện

Dữ liệu huấn luyện được lưu dưới dạng JSON với cấu trúc sau:

```json
[
  {
    "title": "Tiêu đề bài viết",
    "content": "Nội dung bài viết...",
    "is_fake_news": true
  },
  {
    "title": "Tiêu đề bài viết khác",
    "content": "Nội dung bài viết khác...",
    "is_fake_news": false
  }
]
```

### Thêm dữ liệu huấn luyện tùy chỉnh

Để thêm dữ liệu huấn luyện tùy chỉnh, tạo file JSON với cấu trúc trên và đặt trong thư mục `data/credibility/training/`.

## Các chỉ báo phát hiện tin giả

Bộ phát hiện tin giả sử dụng nhiều chỉ báo khác nhau, bao gồm:

1. **Từ khóa tin giả**: Phát hiện các từ khóa thường xuất hiện trong tin giả
2. **Mẫu clickbait**: Phát hiện các mẫu tiêu đề câu chuyện gây sốc
3. **Từ viết hoa quá mức**: Phát hiện việc sử dụng chữ viết hoa quá nhiều
4. **Dấu câu cảm xúc**: Phát hiện việc lạm dụng dấu chấm than, chấm hỏi
5. **Văn phong cường điệu**: Phát hiện ngôn ngữ cường điệu, phóng đại

## Ghi chú

- Bộ phát hiện tin giả sẽ hoạt động ở chế độ dự phòng nếu các thư viện học máy không có sẵn
- Độ chính xác của phát hiện phụ thuộc vào chất lượng và số lượng dữ liệu huấn luyện
- Đối với ngôn ngữ không phải tiếng Anh, cần dữ liệu huấn luyện riêng cho từng ngôn ngữ

## Quy trình làm việc điển hình

1. Khởi tạo bộ phát hiện tin giả với mô hình phù hợp
2. Đánh giá nội dung đầu vào để xác định xác suất tin giả
3. Kết hợp kết quả phát hiện vào đánh giá độ tin cậy tổng thể
4. Cung cấp cảnh báo và chỉ báo cụ thể cho người dùng
5. Thu thập phản hồi và cải thiện mô hình theo thời gian 