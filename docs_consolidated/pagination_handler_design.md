# Thiết kế module PaginationHandler dùng chung

Dựa trên phân tích các module xử lý phân trang hiện tại, tôi đề xuất thiết kế module PaginationHandler dùng chung như sau:

## C<PERSON>u trúc module

```python
import os
import re
import time
import json
import logging
import threading
from typing import Dict, List, Optional, Any, Tuple, Union, Set, Callable
from urllib.parse import urlparse, urljoin, parse_qs, urlencode, urlunparse
import requests
from bs4 import BeautifulSoup

# Thiết lập logging
from ..utils.structured_logging import get_logger
logger = get_logger(__name__)

# <PERSON><PERSON><PERSON> tra các thư viện phụ thuộc
try:
    from playwright.sync_api import sync_playwright, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rowserContex<PERSON>, TimeoutError as PlaywrightTimeoutError
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logger.warning("Playwright không khả dụng. <PERSON><PERSON><PERSON> số tính năng sẽ bị hạn chế.")

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    logger.warning("Selenium không khả dụng. Một số tính năng sẽ bị hạn chế.")

class PaginationHandler:
    """
    Module xử lý phân trang dùng chung cho cả WebSearchAgentLocal và AdaptiveCrawler.
    """

    def __init__(
        self,
        pagination_selectors: Optional[List[str]] = None,
        next_page_selectors: Optional[List[str]] = None,
        prev_page_selectors: Optional[List[str]] = None,
        page_number_selectors: Optional[List[str]] = None,
        max_pages: int = 10,
        max_page_depth: int = 5,
        detect_infinite_scroll: bool = True,
        detect_load_more: bool = True,
        detect_ajax_pagination: bool = True,
        detect_url_patterns: bool = True,
        url_param_page_names: Optional[List[str]] = None,
        url_path_page_patterns: Optional[List[str]] = None,
        use_async: bool = True,
        max_concurrent: int = 3,
        timeout: int = 30,
        delay: float = 1.0,
        user_agent: Optional[str] = None,
        user_agent_manager: Optional[Any] = None,
        use_playwright: bool = False,
        use_selenium: bool = False,
        pagination_detection_threshold: float = 0.7,
        wait_time_between_pages: float = 1.0,
        max_wait_time: int = 10,
        scroll_step: int = 500,
        max_scroll_attempts: int = 20,
        cache_enabled: bool = True,
        cache_ttl: int = 3600,
        verbose: bool = False,
        **kwargs
    ):
        """
        Khởi tạo PaginationHandler.

        Args:
            pagination_selectors: Các CSS selector để tìm phân trang
            next_page_selectors: Các CSS selector để tìm nút "Trang tiếp theo"
            prev_page_selectors: Các CSS selector để tìm nút "Trang trước"
            page_number_selectors: Các CSS selector để tìm số trang
            max_pages: Số lượng trang tối đa để xử lý
            max_page_depth: Độ sâu tối đa của phân trang
            detect_infinite_scroll: Phát hiện cuộn vô hạn hay không
            detect_load_more: Phát hiện nút "Tải thêm" hay không
            detect_ajax_pagination: Phát hiện phân trang AJAX hay không
            detect_url_patterns: Phát hiện mẫu URL hay không
            url_param_page_names: Các tên tham số URL cho số trang
            url_path_page_patterns: Các mẫu đường dẫn URL cho số trang
            use_async: Sử dụng tải không đồng bộ nếu khả dụng
            max_concurrent: Số kết nối đồng thời tối đa
            timeout: Thời gian chờ kết nối (giây)
            delay: Thời gian chờ giữa các yêu cầu (giây)
            user_agent: User-Agent tùy chỉnh
            user_agent_manager: Đối tượng UserAgentManager để lấy User-Agent
            use_playwright: Sử dụng Playwright thay vì requests
            use_selenium: Sử dụng Selenium thay vì requests
            pagination_detection_threshold: Ngưỡng phát hiện phân trang
            wait_time_between_pages: Thời gian chờ giữa các trang (giây)
            max_wait_time: Thời gian chờ tối đa (giây)
            scroll_step: Bước cuộn (pixel)
            max_scroll_attempts: Số lần cuộn tối đa
            cache_enabled: Bật cache hay không
            cache_ttl: Thời gian sống của cache (giây)
            verbose: Ghi log chi tiết
            **kwargs: Các tham số bổ sung
        """
        # Khởi tạo các thuộc tính
        self.pagination_selectors = pagination_selectors or [
            ".pagination",
            ".pager",
            ".pages",
            ".page-numbers",
            "nav.pagination",
            "ul.pagination",
            "div.pagination",
            ".paginate",
            ".paginator",
            ".paging",
            ".page-navigation",
            ".page-nav",
            ".wp-pagenavi",
            ".navigation",
            ".phantrang",
            ".phan-trang",
            ".phanTrang",
            ".page-links",
        ]
        self.next_page_selectors = next_page_selectors or [
            ".next",
            ".next-page",
            ".next_page",
            ".nextPage",
            ".pagination-next",
            ".pagination__next",
            ".pagination-item--next",
            ".pagination__item--next",
            ".pagination-link--next",
            ".pagination__link--next",
            ".next-posts",
            ".next-posts-link",
            ".next-page-link",
            ".next-link",
            ".next-button",
            ".next-btn",
            ".nextBtn",
            ".nextButton",
            ".next-nav",
            ".next-navigation",
            ".next-pagination",
            ".next-pager",
            ".next-pages",
            ".next-page-nav",
            ".next-page-navigation",
            ".next-page-pagination",
            ".next-page-pager",
            ".next-page-pages",
            ".trang-sau",
            ".trangSau",
            ".trang_sau",
            ".trang-ke",
            ".trangKe",
            ".trang_ke",
            ".trang-tiep",
            ".trangTiep",
            ".trang_tiep",
            ".trang-tiep-theo",
            ".trangTiepTheo",
            ".trang_tiep_theo",
            "a[rel='next']",
            "a[rel='nofollow next']",
            "a[aria-label='Next']",
            "a[aria-label='Next Page']",
            "a:contains('Next')",
            "a:contains('Next Page')",
            "a:contains('Next »')",
            "a:contains('»')",
            "a:contains('Trang sau')",
            "a:contains('Trang kế')",
            "a:contains('Trang tiếp')",
            "a:contains('Trang tiếp theo')",
            "a:contains('Tiếp')",
            "a:contains('Tiếp theo')",
            "a:contains('Tiếp »')",
            "a:contains('Sau')",
            "a:contains('Sau »')",
            "a:contains('Kế')",
            "a:contains('Kế »')",
        ]
```

## Các phương thức chính

1. **detect_pagination(content, url)**: Phát hiện phân trang trên trang web
2. **get_next_page(url, content)**: Lấy URL của trang tiếp theo
3. **get_prev_page(url, content)**: Lấy URL của trang trước
4. **get_page_numbers(url, content)**: Lấy danh sách số trang
5. **extract_paginated_content(url, content_selector, ...)**: Trích xuất nội dung từ trang phân trang
6. **handle_pagination(url, pagination_info, page_handler, page=None)**: Xử lý phân trang
7. **handle_infinite_scroll(page, max_scrolls, scroll_delay)**: Xử lý infinite scroll
8. **handle_load_more(page, button_selector, max_clicks, click_delay)**: Xử lý nút "Tải thêm"
9. **handle_ajax_pagination(page, next_page_selector, max_pages, wait_time)**: Xử lý phân trang AJAX

## Các phương thức hỗ trợ

1. **_detect_pagination_type(content, url)**: Phát hiện loại phân trang
2. **_extract_page_numbers(content, url)**: Trích xuất số trang
3. **_extract_next_page(content, url)**: Trích xuất URL trang tiếp theo
4. **_extract_prev_page(content, url)**: Trích xuất URL trang trước
5. **_detect_infinite_scroll(content)**: Phát hiện infinite scroll
6. **_detect_load_more(content)**: Phát hiện nút "Tải thêm"
7. **_detect_ajax_pagination(content)**: Phát hiện phân trang AJAX
8. **_detect_url_pattern(url)**: Phát hiện mẫu URL
9. **_get_cache_key(url)**: Lấy khóa cache cho URL
10. **_get_from_cache(url)**: Lấy kết quả từ cache
11. **_save_to_cache(url, result)**: Lưu kết quả vào cache
12. **_get_user_agent()**: Lấy User-Agent

## Các tính năng đặc biệt

1. **Phát hiện nhiều loại phân trang**: Phát hiện phân trang dựa trên CSS selector, tham số URL, mẫu đường dẫn URL
2. **Xử lý phân trang dạng nút**: Hỗ trợ xử lý phân trang dạng nút (Next, Previous)
3. **Xử lý phân trang dạng số**: Hỗ trợ xử lý phân trang dạng số (1, 2, 3, ...)
4. **Xử lý phân trang dạng "Xem thêm"**: Hỗ trợ xử lý phân trang dạng "Xem thêm"
5. **Xử lý phân trang dạng "Load more"**: Hỗ trợ xử lý phân trang dạng "Load more"
6. **Xử lý phân trang dạng "Infinite scroll"**: Phát hiện và xử lý infinite scroll
7. **Xử lý phân trang dạng "AJAX"**: Phát hiện và xử lý phân trang AJAX
8. **Xử lý phân trang dạng "URL"**: Hỗ trợ xử lý phân trang dạng "URL"
9. **Xử lý phân trang dạng "Query parameter"**: Hỗ trợ xử lý phân trang dạng "Query parameter"
10. **Xử lý phân trang dạng "Path"**: Hỗ trợ xử lý phân trang dạng "Path"
11. **Xử lý phân trang dạng "Fragment"**: Hỗ trợ xử lý phân trang dạng "Fragment"
12. **Xử lý phân trang dạng "Hash"**: Hỗ trợ xử lý phân trang dạng "Hash"
13. **Xử lý phân trang dạng "State"**: Hỗ trợ xử lý phân trang dạng "State"
14. **Xử lý phân trang dạng "History"**: Hỗ trợ xử lý phân trang dạng "History"
15. **Xử lý phân trang dạng "Push state"**: Hỗ trợ xử lý phân trang dạng "Push state"
16. **Xử lý phân trang dạng "Replace state"**: Hỗ trợ xử lý phân trang dạng "Replace state"
17. **Xử lý phân trang dạng "Pop state"**: Hỗ trợ xử lý phân trang dạng "Pop state"
18. **Xử lý phân trang dạng "Window.location"**: Hỗ trợ xử lý phân trang dạng "Window.location"
19. **Xử lý phân trang dạng "Document.location"**: Hỗ trợ xử lý phân trang dạng "Document.location"
20. **Xử lý phân trang dạng "Location.href"**: Hỗ trợ xử lý phân trang dạng "Location.href"
21. **Hỗ trợ Playwright và Selenium**: Có thể sử dụng Playwright hoặc Selenium để xử lý phân trang
22. **Hỗ trợ tiếng Việt**: Hỗ trợ phát hiện phân trang tiếng Việt
23. **Cache kết quả**: Lưu cache kết quả để tránh truy cập lại
24. **Xử lý đồng thời**: Hỗ trợ xử lý đồng thời để tăng hiệu suất
25. **Thống kê**: Theo dõi thống kê về việc sử dụng

## Cách tích hợp

### Tích hợp vào WebSearchAgentLocal

```python
from ..utils.pagination_handler import PaginationHandler

def integrate_pagination_handler(agent, config=None):
    """
    Tích hợp PaginationHandler vào WebSearchAgentLocal.
    """
    # Cấu hình mặc định
    default_config = {
        "max_pages": 10,
        "detect_infinite_scroll": True,
        "detect_load_more": True,
        "detect_ajax_pagination": True,
        "detect_url_patterns": True,
        "use_playwright": True,
        "cache_enabled": True,
        "verbose": False
    }

    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    pagination_config = {**default_config, **(config or {})}

    # Khởi tạo PaginationHandler
    agent._pagination_handler = PaginationHandler(**pagination_config)

    # Thêm phương thức extract_paginated_content vào agent
    agent.extract_paginated_content = lambda url, content_selector, **kwargs: agent._pagination_handler.extract_paginated_content(url, content_selector, **kwargs)

    # Đánh dấu là đã tích hợp
    agent.pagination_handler_integrated = True

    logger.info("PaginationHandler đã được tích hợp thành công vào WebSearchAgentLocal")
```

### Tích hợp vào AdaptiveCrawler

```python
from ..utils.pagination_handler import PaginationHandler

def integrate_pagination_handler(crawler, config=None):
    """
    Tích hợp PaginationHandler vào AdaptiveCrawler.
    """
    # Cấu hình mặc định
    default_config = {
        "max_pages": crawler.max_pages_per_pagination,
        "detect_infinite_scroll": crawler.handle_infinite_scroll,
        "detect_load_more": True,
        "detect_ajax_pagination": crawler.handle_ajax,
        "detect_url_patterns": True,
        "use_playwright": crawler.use_playwright,
        "timeout": crawler.timeout,
        "delay": crawler.delay,
        "user_agent": crawler.user_agent,
        "verbose": crawler.verbose
    }

    # Kết hợp cấu hình mặc định và cấu hình được cung cấp
    pagination_config = {**default_config, **(config or {})}

    # Khởi tạo PaginationHandler
    crawler.pagination_handler = PaginationHandler(**pagination_config)

    # Thay thế phương thức _handle_pagination
    crawler._handle_pagination = lambda content, url: crawler.pagination_handler.detect_pagination(content, url)

    # Đánh dấu là đã tích hợp
    crawler.pagination_handler_integrated = True

    logger.info("PaginationHandler đã được tích hợp thành công vào AdaptiveCrawler")
```
