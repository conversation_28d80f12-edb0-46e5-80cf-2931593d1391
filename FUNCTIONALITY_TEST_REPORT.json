{"timestamp": "2025-05-28T20:25:16.494298", "test_type": "functionality_test", "summary": {"total_tests": 6, "passed": 1, "partial": 2, "failed": 3}, "test_results": {"web_search_agent": {"status": "FAILED", "details": "cannot import name 'BaseTransport' from 'httpx' (/home/<USER>/.local/lib/python3.10/site-packages/httpx/__init__.py)"}, "adaptive_crawler": {"status": "FAILED", "details": "cannot import name 'BaseTransport' from 'httpx' (/home/<USER>/.local/lib/python3.10/site-packages/httpx/__init__.py)"}, "vietnamese_search": {"status": "FAILED", "details": "No module named 'deep_research_core.utils.vietnamese_search_methods'"}, "utils_modules": {"status": "PARTIAL", "details": "0/3 utils modules imported successfully", "results": ["base_utils: FAILED - No module named 'deep_research_core.utils.base_utils'", "file_processor: FAILED - No module named 'deep_research_core.utils.file_processor'", "credibility_evaluator: FAILED - No module named 'deep_research_core.utils.credibility_evaluator'"]}, "shared_modules": {"status": "PARTIAL", "details": "0/3 shared modules imported successfully", "results": ["captcha_handler: FAILED - No module named 'deep_research_core.utils.shared'", "user_agent_manager: FAILED - No module named 'deep_research_core.utils.shared'", "shared_file_processor: FAILED - No module named 'deep_research_core.utils.shared'"]}, "sample_test": {"status": "SUCCESS", "details": "Sample functionality test passed"}}, "overall_status": "FAILED"}