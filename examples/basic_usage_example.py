#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Usage example cho Deep Research Core.
"""

import sys
import os
import json
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

def basic_search_example():
    """Example tìm kiếm cơ bản."""
    print("🔍 Basic Search Example")
    print("-" * 30)
    
    # Simulate search results
    search_query = "biến đổi khí hậu"
    
    # Mock search results
    results = {
        "query": search_query,
        "timestamp": datetime.now().isoformat(),
        "results": [
            {
                "title": "Biến đổi khí hậu và tác động đến môi trường",
                "url": "https://example1.com",
                "snippet": "Biến đổi khí hậu đang gây ra nhiều tác động nghiêm trọng..."
            },
            {
                "title": "Gi<PERSON>i pháp ứng phó với biến đổi khí hậu",
                "url": "https://example2.com", 
                "snippet": "Các giải pháp bền vững để ứng phó với biến đổi khí hậu..."
            }
        ],
        "total_results": 2,
        "search_time": 0.5
    }
    
    print(f"Query: {search_query}")
    print(f"Found {len(results['results'])} results:")
    
    for i, result in enumerate(results['results'], 1):
        print(f"\n{i}. {result['title']}")
        print(f"   URL: {result['url']}")
        print(f"   Snippet: {result['snippet']}")
    
    # Save results
    os.makedirs('examples/output', exist_ok=True)
    with open('examples/output/basic_search_example.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Results saved to examples/output/basic_search_example.json")

def vietnamese_search_example():
    """Example tìm kiếm tiếng Việt."""
    print("\n🇻🇳 Vietnamese Search Example")
    print("-" * 30)
    
    # Vietnamese query optimization example
    original_query = "tim kiem thong tin"
    optimized_query = "tìm kiếm thông tin"
    
    print(f"Original: {original_query}")
    print(f"Optimized: {optimized_query}")
    
    # Mock Vietnamese search results
    results = {
        "original_query": original_query,
        "optimized_query": optimized_query,
        "language": "vi",
        "results": [
            {
                "title": "Hướng dẫn tìm kiếm thông tin hiệu quả",
                "url": "https://vietnamese-site1.com",
                "snippet": "Các phương pháp tìm kiếm thông tin chính xác và nhanh chóng..."
            }
        ]
    }
    
    print(f"\nVietnamese search results:")
    for result in results['results']:
        print(f"- {result['title']}")
        print(f"  {result['snippet']}")

def main():
    """Main example function."""
    print("📚 DEEP RESEARCH CORE - USAGE EXAMPLES")
    print("=" * 50)
    
    basic_search_example()
    vietnamese_search_example()
    
    print("\n" + "=" * 50)
    print("✅ Examples completed!")
    print("\n📖 For more examples, check:")
    print("  - docs_consolidated/USAGE_EXAMPLES.md")
    print("  - examples/ directory")

if __name__ == "__main__":
    main()
