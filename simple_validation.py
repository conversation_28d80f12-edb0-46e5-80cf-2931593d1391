#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple validation: <PERSON><PERSON><PERSON> tra cấu trúc dự án sau khi tổ chức lại.
"""

import os
import json
from datetime import datetime

def check_structure():
    """Kiểm tra cấu trúc thư mục."""
    print("📁 Checking project structure...")
    
    expected_dirs = [
        'src/deep_research_core',
        'docs_consolidated', 
        'tests_consolidated',
        'config_consolidated',
        'scripts_consolidated',
        'archive'
    ]
    
    structure_check = {}
    for dir_name in expected_dirs:
        if os.path.exists(dir_name):
            # Count files
            file_count = 0
            for root, dirs, files in os.walk(dir_name):
                file_count += len(files)
            
            structure_check[dir_name] = {
                'exists': True,
                'file_count': file_count
            }
            print(f"  ✅ {dir_name}: {file_count} files")
        else:
            structure_check[dir_name] = {
                'exists': False,
                'file_count': 0
            }
            print(f"  ❌ {dir_name}: Missing")
    
    return structure_check

def check_main_files():
    """Kiểm tra các file chính."""
    print("\n📄 Checking main files...")
    
    main_files = [
        'README.md',
        'src/deep_research_core/agents/web_search_agent_local_merged.py',
        'src/deep_research_core/agents/adaptive_crawler_consolidated_merged.py',
        'config_consolidated/requirements.txt'
    ]
    
    file_check = {}
    for file_path in main_files:
        if os.path.exists(file_path):
            try:
                size = os.path.getsize(file_path)
                file_check[file_path] = {
                    'exists': True,
                    'size': size
                }
                print(f"  ✅ {file_path}: {size} bytes")
            except Exception as e:
                file_check[file_path] = {
                    'exists': True,
                    'error': str(e)
                }
                print(f"  ⚠️ {file_path}: Error - {e}")
        else:
            file_check[file_path] = {
                'exists': False
            }
            print(f"  ❌ {file_path}: Missing")
    
    return file_check

def count_root_files():
    """Đếm files ở root level."""
    print("\n📊 Counting root level files...")
    
    root_files = [f for f in os.listdir('.') if os.path.isfile(f)]
    root_dirs = [d for d in os.listdir('.') if os.path.isdir(d) and not d.startswith('.')]
    
    print(f"  📄 Root files: {len(root_files)}")
    print(f"  📁 Root directories: {len(root_dirs)}")
    
    # Show root files
    for file in root_files:
        print(f"    📄 {file}")
    
    return {
        'root_files': root_files,
        'root_dirs': root_dirs,
        'root_file_count': len(root_files),
        'root_dir_count': len(root_dirs)
    }

def check_requirements_files():
    """Kiểm tra requirements files."""
    print("\n📦 Checking requirements files...")
    
    req_files = [
        'config_consolidated/requirements.txt',
        'config_consolidated/requirements-core.txt',
        'config_consolidated/requirements-credibility.txt', 
        'config_consolidated/requirements-llm.txt'
    ]
    
    req_check = {}
    for req_file in req_files:
        if os.path.exists(req_file):
            try:
                with open(req_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = [line.strip() for line in content.split('\n') 
                            if line.strip() and not line.startswith('#')]
                
                req_check[req_file] = {
                    'exists': True,
                    'package_count': len(lines),
                    'sample_packages': lines[:3]  # First 3 packages
                }
                print(f"  ✅ {req_file}: {len(lines)} packages")
                
            except Exception as e:
                req_check[req_file] = {
                    'exists': True,
                    'error': str(e)
                }
                print(f"  ⚠️ {req_file}: Error - {e}")
        else:
            req_check[req_file] = {
                'exists': False
            }
            print(f"  ❌ {req_file}: Missing")
    
    return req_check

def create_quick_start():
    """Tạo quick start guide."""
    print("\n📖 Creating QUICK_START.md...")
    
    quick_start_content = """# 🚀 Quick Start Guide

## Cài đặt

```bash
# Cài đặt dependencies
pip install -r config_consolidated/requirements.txt
```

## Sử dụng WebSearchAgent

```python
import sys
sys.path.append('src')

from deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged

# Khởi tạo agent
agent = WebSearchAgentLocalMerged()

# Tìm kiếm
results = agent.search("biến đổi khí hậu", num_results=5)
print(results)
```

## Cấu trúc Dự án

- `src/deep_research_core/` - Main codebase
- `docs_consolidated/` - Documentation  
- `tests_consolidated/` - Tests
- `config_consolidated/` - Configurations
- `scripts_consolidated/` - Utility scripts

## Chạy Tests

```bash
cd tests_consolidated/
python test_web_search_agent_local_merged.py
```
"""
    
    with open('QUICK_START.md', 'w', encoding='utf-8') as f:
        f.write(quick_start_content)
    
    print("  ✅ Created QUICK_START.md")

def generate_simple_report(structure_check, file_check, root_analysis, req_check):
    """Tạo báo cáo đơn giản."""
    print("\n📊 Generating validation report...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'validation_type': 'simple_structure_check',
        'summary': {
            'structure_dirs_ok': len([d for d in structure_check.values() if d['exists']]),
            'structure_dirs_total': len(structure_check),
            'main_files_ok': len([f for f in file_check.values() if f['exists']]),
            'main_files_total': len(file_check),
            'root_files': root_analysis['root_file_count'],
            'requirements_files_ok': len([r for r in req_check.values() if r['exists']]),
            'requirements_files_total': len(req_check)
        },
        'details': {
            'structure_check': structure_check,
            'file_check': file_check,
            'root_analysis': root_analysis,
            'requirements_check': req_check
        },
        'status': 'COMPLETED'
    }
    
    with open('SIMPLE_VALIDATION_REPORT.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("  ✅ Report saved: SIMPLE_VALIDATION_REPORT.json")
    return report

def main():
    """Main validation function."""
    print("🔍 SIMPLE PROJECT VALIDATION")
    print("=" * 50)
    
    # Step 1: Check structure
    structure_check = check_structure()
    
    # Step 2: Check main files
    file_check = check_main_files()
    
    # Step 3: Count root files
    root_analysis = count_root_files()
    
    # Step 4: Check requirements
    req_check = check_requirements_files()
    
    # Step 5: Create quick start
    create_quick_start()
    
    # Step 6: Generate report
    report = generate_simple_report(structure_check, file_check, root_analysis, req_check)
    
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY:")
    print(f"  📁 Directory structure: {report['summary']['structure_dirs_ok']}/{report['summary']['structure_dirs_total']} OK")
    print(f"  📄 Main files: {report['summary']['main_files_ok']}/{report['summary']['main_files_total']} OK")
    print(f"  📋 Root files: {report['summary']['root_files']} files")
    print(f"  📦 Requirements: {report['summary']['requirements_files_ok']}/{report['summary']['requirements_files_total']} OK")
    
    # Calculate overall score
    total_checks = (report['summary']['structure_dirs_total'] + 
                   report['summary']['main_files_total'] + 
                   report['summary']['requirements_files_total'])
    passed_checks = (report['summary']['structure_dirs_ok'] + 
                    report['summary']['main_files_ok'] + 
                    report['summary']['requirements_files_ok'])
    
    score = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
    
    print(f"\n🎯 OVERALL SCORE: {score:.1f}% ({passed_checks}/{total_checks} checks passed)")
    
    if score >= 90:
        print("🎉 Excellent! Project structure is well organized.")
    elif score >= 70:
        print("✅ Good! Minor issues to address.")
    else:
        print("⚠️ Needs attention. Several issues found.")
    
    print("\n✅ Simple validation completed!")

if __name__ == "__main__":
    main()
