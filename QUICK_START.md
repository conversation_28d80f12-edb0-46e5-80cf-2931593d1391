# 🚀 Quick Start Guide

## Cài đặt

```bash
# Cài đặt dependencies
pip install -r config_consolidated/requirements.txt
```

## Sử dụng WebSearchAgent

```python
import sys
sys.path.append('src')

from deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged

# Khởi tạo agent
agent = WebSearchAgentLocalMerged()

# Tìm kiếm
results = agent.search("biến đổi khí hậu", num_results=5)
print(results)
```

## Cấu trúc Dự án

- `src/deep_research_core/` - Main codebase
- `docs_consolidated/` - Documentation  
- `tests_consolidated/` - Tests
- `config_consolidated/` - Configurations
- `scripts_consolidated/` - Utility scripts

## Chạy Tests

```bash
cd tests_consolidated/
python test_web_search_agent_local_merged.py
```
