{"timestamp": "2025-05-28T20:17:23.226114", "summary": {"total_files": 65892, "root_level_files": 148, "documentation_files": 73, "test_files": 1115, "duplicate_candidates": 27}, "file_counts": {"Markdown docs": 2904, "Python files": 1866, "Text files": 96, "JSON files": 2640, "Other files": 58160, "YAML configs": 224, "Test files": 2}, "root_analysis": {"root_dirs": ["thuvienphapluat_docs", "deepresearch", "__pycache__", "docs", "backup_20250528_201501", "examples", "test_downloads", "tools", "backup_20250528_201532", "temp_test_dir", "src", "backup_20250528_201608", "tasks", "static", "results", "tests", "data", "config", "backup", "test_results"], "root_files": ["user_agent_manager_design.md", "MERGE_PLAN.md", "WebSearchAgentLocal_Implementation_Plan.md", "ENHANCED_SEARXNG_IMPLEMENTATION_SUMMARY.md", "HUONG_DAN_TIM_KIEM_TIENG_VIET.md", "test_extract_videos.py", "test_comprehensive_web_search_agent.py", "performance_optimization.py", "pagination_handler_design.md", "test_websearch_real_functionality.py", "simple_test.py", "PLANNING.md", "WebSearchAgentLocal_Tasks.md", "requirements_ui.txt", "test_extract_audio.py", "INSTALLATION.md", "adaptive_crawler_upgrade_status.md", "test_consolidated_simple.py", "playwright_handler_design.md", "test_extract_images.py", "fix_web_search_agent_local_merged.py", "ADAPTIVE_CRAWLER_ANALYSIS_REPORT.md", "test_file_processor.py", "merge_check_results.json", "test_performance_benchmark.py", "CONSOLIDATION_PLAN.md", "CONSOLIDATION_REPORT.md", "simple_file_processor_test.py", "test_vietnamese_utils.py", "TASK_3_ADVANCED_FEATURES.md", "PROGRESS_SUMMARY.md", "test_final_comprehensive_integration.py", "local_tasks.txt", "performance_measurement.py", "WEBSITE_CRAWLER_README.md", "test_adaptive_crawler_consolidated.py", "TASK_4_FINAL_SUMMARY.md", "test_extract_files.py", "SEARXNG_LOCAL_PRIORITY_IMPLEMENTATION.md", "test_deep_crawl.py", "README_ADAPTIVE_CRAWLER.md", "TASK_2_UTILS_COMPLETION.md", "comprehensive_test_web_search_agent.py", "requirements-credibility.txt", "ADAPTIVE_CRAWLER_UPGRADE_PLAN.md", "TASK_REPORT.md", "search_statistics.json", "test_step10_integration_manager.py", "integration_summary.md", "analyze_project_structure.py", "WebSearchAgentLocal_Improvements.md", "example_crawl_results.md", "quick_searxng_check.py", "detailed_test.py", "WebSearchAgentLocal_Advanced_Features_Guide.md", "quick_search_test.py", "requirements-core.txt", "test_results.json", "test_simple_import.py", "requirements-llm.txt", "TASK_1_COMPLETION_SUMMARY.md", ".giti<PERSON>re", "file_processor_design.md", "website_crawler_with_download.py", "TASK_1_FINAL_VERIFICATION.md", "MISSING_FEATURES_ASSESSMENT.md", "ADAPTIVE_CRAWLER_IMPLEMENTATION_PLAN.md", "IMPLEMENTATION_REPORT.md", "DETAILED_COMPARISON_REPORT.md", "test_step8_site_structure.py", "progress_summary.md", "test_web_search_agent_local_features.py", "ADAPTIVE_CRAWLER_MERGE_TASKS.md", "WebSearchAgentLocal_Additional_Features.md", "restructure_phase1.py", "test_searxng_local_only.py", "minimal_test.py", "test_final_status_check.py", "WebSearchAgentLocal_Improvements_Part2.md", "VIETNAMESE_SEARCH_README.md", "test_real_search.py", "TASKS.md", "README_FEEDBACK.md", "test_web_search_agent_merged_simple.py", "ADAPTIVE_CRAWLER_MERGE_REPORT.md", "language_handler_design.md", "API_DOCUMENTATION.md", "WEBSITE_CRAWLER_WITH_DOWNLOAD_README.md", "website_crawler.py", "deep_crawl_results.md", "IMPLEMENTATION_SUMMARY.md", "USAGE_EXAMPLES.md", "task.txt", "README_LLM_ANALYZER.md", "TASK_1_FEATURE_MAPPING_ASSESSMENT.md", "test_consolidation_completion.py", "test_user_agent_manager.py", "TASK_3_FINAL_SUMMARY.md", "test_searxng_docker.py", "test_step1_integration.py", "README_WEBSEARCHAGENT_COMPREHENSIVE.md", "demo_evaluation_features.py", "test_consolidated_agent.py", "TASK_1_VERIFICATION_REPORT.md", "README_FAKE_NEWS_DETECTOR.md", "adaptive_crawler_upgrade_plan.md", "test_results.md", "config_manager_design.md", "task.md", "test_simple_merge.py", "final_production_readiness_check.py", "test_searxng_api.py", "test_modules.py", "ADAPTIVE_CRAWLER_MERGE_PLAN.md", "simple_search_test.py", "TASK_4_TESTING_DOCUMENTATION.md", "test_adaptive_crawler_consolidated_merged.py", "README_MULTILINGUAL.md", "test_adaptive_crawler_integration.py", "ADAPTIVE_CRAWLER_COMPREHENSIVE_ANALYSIS.md", "test_web_search_agent_structure.py", "PERFORMANCE_IMPROVEMENTS.md", "README_UI.md", "test_web_search_agent_local_merged.py", "README.md", "test_import_adaptive_crawler.py", "test_site_structure_handler.py", "PROJECT_RESTRUCTURE_PLAN.md", "requirements.txt", "check_circular_imports.py", "captcha_handler_design.md", "test_web_search_agent_merged.py", "direct_test.py", "test_utils_modules.py", "test_searxng_priority.py", "WebSearchAgentLocal_Improvements_Part3.md", "test_simple_functionality.py", "TASK_3_COMPLETION_SUMMARY.md", "site_structure_handler_design.md", "TASK.md", "test_minimal_agent.py", "README_EVALUATION_FEATURES.md", "README_FIXES.md", "typestubs.py", "web_search_agent_local_merged_fixed.py", "example_crawl_results.json", "test_step9_language_handler.py", "test_new_features.py"], "docs": ["user_agent_manager_design.md", "MERGE_PLAN.md", "WebSearchAgentLocal_Implementation_Plan.md", "ENHANCED_SEARXNG_IMPLEMENTATION_SUMMARY.md", "HUONG_DAN_TIM_KIEM_TIENG_VIET.md", "pagination_handler_design.md", "PLANNING.md", "WebSearchAgentLocal_Tasks.md", "INSTALLATION.md", "adaptive_crawler_upgrade_status.md", "playwright_handler_design.md", "ADAPTIVE_CRAWLER_ANALYSIS_REPORT.md", "CONSOLIDATION_PLAN.md", "CONSOLIDATION_REPORT.md", "TASK_3_ADVANCED_FEATURES.md", "PROGRESS_SUMMARY.md", "WEBSITE_CRAWLER_README.md", "TASK_4_FINAL_SUMMARY.md", "SEARXNG_LOCAL_PRIORITY_IMPLEMENTATION.md", "README_ADAPTIVE_CRAWLER.md", "TASK_2_UTILS_COMPLETION.md", "ADAPTIVE_CRAWLER_UPGRADE_PLAN.md", "TASK_REPORT.md", "integration_summary.md", "WebSearchAgentLocal_Improvements.md", "example_crawl_results.md", "WebSearchAgentLocal_Advanced_Features_Guide.md", "TASK_1_COMPLETION_SUMMARY.md", "file_processor_design.md", "TASK_1_FINAL_VERIFICATION.md", "MISSING_FEATURES_ASSESSMENT.md", "ADAPTIVE_CRAWLER_IMPLEMENTATION_PLAN.md", "IMPLEMENTATION_REPORT.md", "DETAILED_COMPARISON_REPORT.md", "progress_summary.md", "ADAPTIVE_CRAWLER_MERGE_TASKS.md", "WebSearchAgentLocal_Additional_Features.md", "WebSearchAgentLocal_Improvements_Part2.md", "VIETNAMESE_SEARCH_README.md", "TASKS.md", "README_FEEDBACK.md", "ADAPTIVE_CRAWLER_MERGE_REPORT.md", "language_handler_design.md", "API_DOCUMENTATION.md", "WEBSITE_CRAWLER_WITH_DOWNLOAD_README.md", "deep_crawl_results.md", "IMPLEMENTATION_SUMMARY.md", "USAGE_EXAMPLES.md", "README_LLM_ANALYZER.md", "TASK_1_FEATURE_MAPPING_ASSESSMENT.md", "TASK_3_FINAL_SUMMARY.md", "README_WEBSEARCHAGENT_COMPREHENSIVE.md", "TASK_1_VERIFICATION_REPORT.md", "README_FAKE_NEWS_DETECTOR.md", "adaptive_crawler_upgrade_plan.md", "test_results.md", "config_manager_design.md", "task.md", "ADAPTIVE_CRAWLER_MERGE_PLAN.md", "TASK_4_TESTING_DOCUMENTATION.md", "README_MULTILINGUAL.md", "ADAPTIVE_CRAWLER_COMPREHENSIVE_ANALYSIS.md", "PERFORMANCE_IMPROVEMENTS.md", "README_UI.md", "README.md", "PROJECT_RESTRUCTURE_PLAN.md", "captcha_handler_design.md", "WebSearchAgentLocal_Improvements_Part3.md", "TASK_3_COMPLETION_SUMMARY.md", "site_structure_handler_design.md", "TASK.md", "README_EVALUATION_FEATURES.md", "README_FIXES.md"], "configs": ["requirements_ui.txt", "merge_check_results.json", "local_tasks.txt", "requirements-credibility.txt", "search_statistics.json", "requirements-core.txt", "test_results.json", "requirements-llm.txt", "task.txt", "requirements.txt", "example_crawl_results.json"], "python_files": ["test_extract_videos.py", "test_comprehensive_web_search_agent.py", "performance_optimization.py", "test_websearch_real_functionality.py", "simple_test.py", "test_extract_audio.py", "test_consolidated_simple.py", "test_extract_images.py", "fix_web_search_agent_local_merged.py", "test_file_processor.py", "test_performance_benchmark.py", "simple_file_processor_test.py", "test_vietnamese_utils.py", "test_final_comprehensive_integration.py", "performance_measurement.py", "test_adaptive_crawler_consolidated.py", "test_extract_files.py", "test_deep_crawl.py", "comprehensive_test_web_search_agent.py", "test_step10_integration_manager.py", "analyze_project_structure.py", "quick_searxng_check.py", "detailed_test.py", "quick_search_test.py", "test_simple_import.py", "website_crawler_with_download.py", "test_step8_site_structure.py", "test_web_search_agent_local_features.py", "restructure_phase1.py", "test_searxng_local_only.py", "minimal_test.py", "test_final_status_check.py", "test_real_search.py", "test_web_search_agent_merged_simple.py", "website_crawler.py", "test_consolidation_completion.py", "test_user_agent_manager.py", "test_searxng_docker.py", "test_step1_integration.py", "demo_evaluation_features.py", "test_consolidated_agent.py", "test_simple_merge.py", "final_production_readiness_check.py", "test_searxng_api.py", "test_modules.py", "simple_search_test.py", "test_adaptive_crawler_consolidated_merged.py", "test_adaptive_crawler_integration.py", "test_web_search_agent_structure.py", "test_web_search_agent_local_merged.py", "test_import_adaptive_crawler.py", "test_site_structure_handler.py", "check_circular_imports.py", "test_web_search_agent_merged.py", "direct_test.py", "test_utils_modules.py", "test_searxng_priority.py", "test_simple_functionality.py", "test_minimal_agent.py", "typestubs.py", "web_search_agent_local_merged_fixed.py", "test_step9_language_handler.py", "test_new_features.py"]}, "comparison": {"src": {"path": "src/deep_research_core", "subdirs": ["docs", "credibility", "ui", "utils", "agents", "security", "api", "tests", "data", "integrations"], "files": ["websearch_agent_local.py", "docs/credibility_evaluation.md", "credibility/fake_news_detector.py", "credibility/fact_check_integration.py", "credibility/vietnamese_credibility_integration.py", "credibility/vietnamese_source_credibility.py", "credibility/__init__.py", "credibility/evaluators/credibility_evaluator.py", "credibility/evaluators/__init__.py", "credibility/evaluators/base_evaluator.py", "credibility/models/content_model.py", "credibility/models/base_model.py", "credibility/models/static_model.py", "credibility/models/__init__.py", "credibility/detectors/fake_news_detector.py", "credibility/detectors/__init__.py", "credibility/detectors/base_detector.py", "ui/app.py", "ui/run.py", "ui/templates/base.html", "ui/templates/untrusted_domains.html", "ui/templates/index.html", "ui/templates/trusted_domains.html", "ui/static/css/style.css", "ui/static/js/main.js", "utils/credibility_integrator.py", "utils/fake_news_detector.py", "utils/credibility_integration.py", "utils/vietnamese_search_integration.py", "utils/adapter.py", "utils/network_utils.py", "utils/vietnamese_search_methods.py", "utils/async_search.py", "utils/stealth_plugin.py", "utils/content_extractor_integration.py", "utils/language_support.py", "utils/source_credibility_evaluator.py", "utils/logging_utils.py", "utils/feedback_system.py", "utils/multilevel_cache.py", "utils/simple_language_detector.py", "utils/structured_logging.py", "utils/factcheckorg_api_client.py", "utils/question_complexity_evaluator.py", "utils/advanced_monitoring.py", "utils/llm_content_analyzer.py", "utils/vietnamese_diacritics.py", "utils/vietnamese_ranking.py", "utils/credibility_evaluator.py", "utils/base_utils.py", "utils/vietnamese_utils.py", "utils/advanced_language_detector.py", "utils/source_credibility_integration.py", "utils/snopes_api_client.py", "utils/fact_check_integration.py", "utils/clickbait_detector.py", "utils/vietnamese_search_integration_setup.py", "utils/config_utils.py", "utils/cache_utils.py", "utils/content_extraction_utils.py", "utils/feedback_collection.py", "utils/monitoring_integration.py", "utils/credibility_utils.py", "utils/disinformation_detector.py", "utils/credibility_config_loader.py", "utils/__init__.py", "utils/multilingual_text_processor.py", "utils/query_utils.py", "utils/file_processor.py", "utils/error_utils.py", "utils/advanced_content_analyzer.py", "utils/fact_check_api_client.py", "utils/politifact_api_client.py", "utils/result_utils.py", "utils/advanced_content_extractor.py", "utils/machine_learning_feedback.py", "utils/factcheck_org_api_client.py", "utils/multilingual_utils.py", "utils/feedback_integration.py", "utils/answer_quality_evaluator.py", "utils/resources/stopwords/vi.txt", "utils/resources/stopwords/de.txt", "utils/resources/stopwords/fr.txt", "utils/resources/stopwords/en.txt", "utils/resources/stopwords/es.txt", "utils/shared/site_structure_handler.py", "utils/shared/playwright_handler.py", "utils/shared/user_agent_manager.py", "utils/shared/language_handler.py", "utils/shared/config_manager.py", "utils/shared/captcha_handler.py", "utils/shared/all_modules_integration.py", "utils/shared/integration.py", "utils/shared/__init__.py", "utils/shared/file_processor.py", "utils/shared/README.md", "utils/shared/pagination_handler.py", "agents/enhanced_web_search_features.py", "agents/content_extractor_integration.py", "agents/web_search_agent.py", "agents/adaptive_crawler_form.py", "agents/web_search_agent_local_merged.py", "agents/advanced_javascript_handler.py", "agents/adaptive_crawler_integration.py", "agents/adaptive_crawler_spa.py", "agents/adaptive_crawler_ajax.py", "agents/adaptive_crawler_pagination.py", "agents/__init__.py", "agents/adaptive_crawler_consolidated.py", "agents/adaptive_crawler_javascript.py", "agents/extract_content_improved.py", "agents/adaptive_crawler.py", "agents/adaptive_crawler_consolidated_merged.py", "agents/multilingual_crawler.py", "agents/credibility_evaluator_integration.py", "agents/web_search_agent_local_merged_fixed.py", "security/request_security.py", "api/rest_api.py", "tests/test_vietnamese_search.py", "tests/test_credibility_evaluator.py", "tests/test_advanced_language_detector.py", "data/dictionaries/query_patterns.json", "data/dictionaries/search_engines.json", "data/dictionaries/reliable_domains.json", "data/dictionaries/vietnamese_stopwords.txt", "data/dictionaries/unreliable_domains.json", "integrations/__init__.py", "integrations/llm/base_analyzer.py", "integrations/llm/base.py", "integrations/llm/openai_analyzer.py", "integrations/llm/huggingface_analyzer.py", "integrations/llm/__init__.py"], "total_files": 132, "python_files": 114, "test_files": 3}, "deepresearch": {"path": "deepresearch", "subdirs": ["notebooks", "searxng", "cache", "volumes", "deepresearch", "downloads", "__pycache__", "docs", "examples", "documentation", "frontend", "src", "output", "tasks", "temp_implementation", "static", "scripts", "test", "benchmarks", "tests", "data", "config", "test_results"], "files": ["run_all_tests.py", "project_structure.md", ".gitattributes", "test_model_loader.py", "web_search_agent_enhanced.py", "requirements-full.txt", "openr1_integration.py", "CONTRIBUTING.md", ".env.example", "test_check_ddg_params.py", "hello.py", "test_reasoning_basic.py", "WEBSEARCHAGENT_IMPROVEMENT_TASKS.md", "test_reasoning_edge_cases.py", "FUTURE_IMPROVEMENTS.md", "LICENSE", "test_web_search_agent_local_performance.py", "check_libraries.py", "tinyzero_example.py", "trlx_integration.py", "simple_test.py", "vietnamese_search_integration.py", "PLANNING.md", "setup.py", "run_evaluation.py", "TASK_FILE_DOWNLOAD.md", "test_direct_crawlee.py", "test_crossref_openalex_fallback.py", "test_validator.py", "VERL_SUMMARY.md", "test_web_search_agent_local_advanced.py", "docker-compose.searxng.yml", ".augment-guidelines", "test_deep_crawler_enhanced.py", "run_test_with_output.py", "pytest.ini", ".cursorrules", "temp_tinyzero_integration.py", "test_local_agent.py", "adaptive_crawler_upgrade_task.md", "test_web_imports.py", "test_file_processor.py", "comprehensive_test_web_search_agent_local.py", "test_crawl_standalone.py", "MANIFEST.in", "package.json", "improve_modules.py", "test_torch_cuda.py", "test_simple_module.py", ".dockerignore", "pyproject.toml", "check_torch_installation.py", "test_tot_empty_query.py", "temp_openr1_integration.py", "test_torch_transformers.py", "web_search_improved_test.json", "test_imports.py", "prm_readme.md", "test_searxng_crawlee.py", "test_web_search_agent_local_large_scale.py", "test_crawlee.py", "crawlee_playwright_output_samples.json", "test_search_optimizer.py", "task11_description.md", "test_error_recovery.py", ".coveragerc", "test_deep_crawl.py", "test_crawlee_playwright_search.py", "test_searxng_connection.py", "README_ADAPTIVE_CRAWLER.md", "README_ENHANCED_ERROR_HANDLING.md", "cot_rag_result.json", "test_openalex_crossref_fallback.py", "evaluation_config.json", "README_TESTING.md", "test_query_optimizer_integration.py", "test_standalone.py", "test_all_modules.py", "settings.yml", "test_100_requests.py", "search_statistics.json", "test_book_search_direct.py", "web_search_agent_local.py", "test_reasoning_modules.py", "README_LOCAL_AGENT.md", "anthropic_reward_model.py", "captcha_solver_basic.py", "question_complexity_evaluator.py", "query_optimizer_cache.json", "query_decomposer.py", "specific_query_results.json", "default_settings.yml", "test_searxng_crawlee_simple.js", "test_file_download_direct.py", "HOW_TO_FIX_INT_OBJECT_IS_NOT_SUBSCRIPTABLE.md", "CHANGELOG.md", "MILVUS_SETUP.md", "test_websearch_evaluation.py", "vietnamese_rag_result.json", "README_IMPROVEMENTS.md", ".giti<PERSON>re", "test_improved_web_search_agent.py", "VIETNAMESE_SUPPORT.md", "README_ERROR_RECOVERY.md", "temp_outcome_based.py", "test_advanced_crawlee.py", "tinyzero_integration.py", "PROGRESS_REPORT.md", "fix_api_providers.py", "check_torch.py", "requirements-dev.txt", "test_search_local_limit.py", "test_merged_agent.py", "test_enhanced_agent.py", "adaptive_crawler_integration.py", "test_performance_nlp.py", "test_web_search.py", "simple_query_optimizer_test.py", "test_crawlee_playwright_output.py", "web_search_agent_local_fix.py", "fix_transformers.py", "VERL_README.md", "web_search_evaluation_results.json", "test_query_decomposer.py", "move_tests.py", "test_reasoning_only.py", "task_update.sh", "test_restructuring_summary.md", "package-lock.json", "test_tot_simple.py", "test_security_enhancements.py", "verl_example.py", "module_list.txt", "test_list.txt", "cot_advanced_evaluation_config.json", "test_deep_crawl_basic.py", "captcha_handler.py", "verl_framework.py", "test_reasoning_components.py", "run_rl_tuning_tests.py", "VIETNAMESE_README.md", "test_web_search_agent_local_comprehensive.py", ".gitignore.new", "test_outcome_based.py", "test_deep_crawl_final.py", "hello_test.py", "suppress_warnings.py", "TASKS.md", "README_SUPPRESS_WARNINGS.md", "LIBRARIES.md", "README-LIBRARIES.md", "reorganize.sh", "test_specialized_search.py", ".editorconfig", "test_search_simple.py", "test_simple_deep_crawler.py", "run_server.sh", "test_web_search_agent_local.py", "test_academic_search.py", "test_search_flow.py", "test_websearch_fixed.py", "install_libraries.py", "optimized_results.json", "searxng_crawlee_results.json", "test_model_loader_simple.py", "README_MILVUS.md", "test_captcha_solver.py", "create_directory.py", "simple_test_improved.py", "run_api_server.py", "error_recovery_summary.md", "test_crawl_methods.py", "run_tests.py", "web_search_test_results.json", "test_verl_framework.py", "test_web_search_agent_local_custom.py", "test_restructuring.md", "ADAPTIVE_CRAWLER-UPGRADE_PLAN.md", "test_deep_crawler_with_file_download.py", "simple_test_fixed.py", "FIXES.md", "IMPLEMENTATION_SUMMARY.md", "run_all_web_search_tests.py", "simple_rag_result.json", "main.py", "search_test_results.json", "test_source_attribution.py", "update_searxng_config.sh", "start_searxng.sh", "__init__.py", "test_check_ddgs.py", "test_module.py", "test_query_analyzer.py", "README_ENHANCED_AGENT.md", "Dockerfile", ".augmentguidelines", "test_file_extraction.py", "ADAPTIVE_CRAWLER-IMPLEMENTATION_PLAN.md", "UPDATE.md", "test_extended_document_extractor.py", "docker-compose.extended.yml", "REWARDS_TEST_STATUS.md", "deep_crawl_improvements.py", "test_decide_method.py", "test_prm_reward_model.py", "test_modules_simple.py", "test_output.txt", "test_searxng_api.py", "test_modules.py", "duckduckgo_response.html", "test_multilingual_search.py", "web_search_enhanced_test.json", "test_optimizer.py", "README_WEB_SEARCH_LOCAL.md", "simple_search_test.py", "mkdocs.yml", "test_tot_basic.py", "test_deep_crawl_minimal.py", "test_file_download.py", "test_rl_modules.py", "temp_verl_integration.py", "temp_local_model_optimizer.py", "README_ENHANCED_ADAPTIVE_CRAWLER.md", "test_adaptive_crawler_integration.py", "LOCAL_SEARCH_TASK.md", "adaptive_crawler.py", "tox.ini", "test_all_integrations.py", ".pre-commit-config.yaml", "temp_agent_reward_model.py", "test_standalone_improved.py", "test_tinyzero_framework.py", "exceptions.py", "README.md", "restructuring_guide.md", "search_test_results.log", "<PERSON><PERSON><PERSON>", "ADAPTIVE_CRAWLER-TASK_LIST.md", "test_query_optimizer.py", "test_simple.py", "UI_IMPLEMENTATION_PLAN.md", "test_web_search_agent_local_advanced_detailed.py", "test_web_search_agent_local_error_handling.py", "setup_milvus.sh", "requirements.txt", "test_searxng_agent.py", "test_cache_mechanism.py", "README_IMPROVED_AGENT.md", "temp_trlx_integration.py", "run_test_web_search.py", "IMPLEMENTATION_GUIDE.md", "update_tasks.py", "answer_quality_evaluator.py", "test_specific_query.py", "test_search_methods.py", "test_web_search_agent_local_improvements.py", "test_web_search_agent_mock.py", "check_directory.py", "test_deep_crawl_improved.py", "project_structure_new.md", "docker-compose.yml", "test_openalex_crossref.py", "README_NEW_MODULES.md", "test_query_optimizer_standalone.py", "test_improved_web_search_agent_fix.py", "web_search_comprehensive_test.json", "simple_web_search.py", "test_decide_method_advanced.py", "TASK.md", "test_reasoning_comprehensive.py", "test_deep_crawl_improved_simple.py", "RL_TUNING_TEST_GUIDE.md", "academic_search.py", "crawlee_results.json", "test_search.py", "deep_research_integration.py", "test_query_analyzer_direct.py", "notebooks/grpo_tutorial.ipynb", "notebooks/ppo_tutorial.ipynb", "notebooks/sft_tutorial.ipynb", "notebooks/README.md", ".pytest_cache/.gitignore", ".pytest_cache/CACHEDIR.TAG", ".pytest_cache/README.md", ".pytest_cache/v/cache/stepwise", ".pytest_cache/v/cache/nodeids", "searxng/settings.yml", "searxng/settings_improved.yml", "cache/So sánh tác động của biến đổi khí hậu đến nông nghiệp ở các vùng miền khác nhau của Việt Nam.json", "cache/metadata.json", "cache/Phân tích chi tiết tác động của biến đổi khí hậu đến nông nghiệp ở Đồng bằng sông Cửu Long và đề xuất các giải pháp thích ứng bền vững.json", "cache/Các g<PERSON>i pháp thích ứng với biến đổi khí hậu trong nông nghiệp.json", "cache/Thời tiết <PERSON> hôm nay.json", "volumes/minio/.minio.sys/format.json", "volumes/minio/.minio.sys/buckets/.usage-cache.bin/xl.meta", "volumes/minio/.minio.sys/buckets/.usage.json/xl.meta", "volumes/minio/.minio.sys/buckets/.bloomcycle.bin/xl.meta", "volumes/minio/.minio.sys/buckets/a-bucket/.usage-cache.bin/xl.meta", "volumes/minio/.minio.sys/buckets/a-bucket/.metadata.bin/xl.meta", "volumes/minio/.minio.sys/pool.bin/xl.meta", "volumes/minio/.minio.sys/config/iam/format.json/xl.meta", "volumes/minio/.minio.sys/config/config.json/xl.meta", "volumes/minio/a-bucket/files/stats_log/457214996327325825/457214996327325826/457214996327325835/100/457214996327114297/xl.meta", "volumes/minio/a-bucket/files/stats_log/457214996327325825/457214996327325826/457214996327325835/100/1/xl.meta", "volumes/minio/a-bucket/files/insert_log/457214996327325825/457214996327325826/457214996327325835/103/457214996327114292/xl.meta", "volumes/minio/a-bucket/files/insert_log/457214996327325825/457214996327325826/457214996327325835/100/457214996327114289/xl.meta", "volumes/minio/a-bucket/files/insert_log/457214996327325825/457214996327325826/457214996327325835/104/457214996327114293/xl.meta", "volumes/minio/a-bucket/files/insert_log/457214996327325825/457214996327325826/457214996327325835/101/457214996327114290/xl.meta", "volumes/minio/a-bucket/files/insert_log/457214996327325825/457214996327325826/457214996327325835/1/457214996327114296/xl.meta", "volumes/minio/a-bucket/files/insert_log/457214996327325825/457214996327325826/457214996327325835/105/457214996327114294/xl.meta", "volumes/minio/a-bucket/files/insert_log/457214996327325825/457214996327325826/457214996327325835/0/457214996327114295/xl.meta", "volumes/minio/a-bucket/files/insert_log/457214996327325825/457214996327325826/457214996327325835/102/457214996327114291/xl.meta", "volumes/etcd/member/wal/0000000000000000-0000000000000000.wal", "volumes/etcd/member/snap/db", "volumes/milvus/rdb_data/OPTIONS-000007", "volumes/milvus/rdb_data/LOCK", "volumes/milvus/rdb_data/MANIFEST-000004", "volumes/milvus/rdb_data/OPTIONS-000009", "volumes/milvus/rdb_data/LOG", "volumes/milvus/rdb_data/CURRENT", "volumes/milvus/rdb_data/IDENTITY", "volumes/milvus/rdb_data_meta_kv/OPTIONS-000007", "volumes/milvus/rdb_data_meta_kv/LOCK", "volumes/milvus/rdb_data_meta_kv/MANIFEST-000004", "volumes/milvus/rdb_data_meta_kv/LOG", "volumes/milvus/rdb_data_meta_kv/CURRENT", "volumes/milvus/rdb_data_meta_kv/IDENTITY", "downloads/dummy.pdf", "docs/user-case.md", "docs/advanced_rag_guide.md", "docs/vietnamese_domain_compounds.md", "docs/vietnamese_compound_processor.md", "docs/vietnamese_support.md", "docs/cot_rag_comprehensive_guide.md", "docs/advanced-search-features.md", "docs/METRICS_README.md", "docs/rag_implementations_guide.md", "docs/vietnamese_search_integration.md", "docs/index.md", "docs/vietnamese_rl_framework_integration.md", "docs/web_search_agent.md", "docs/CACHING_IMPROVEMENTS.md", "docs/api_reference.md", "docs/PLAN_SUMMARY.md", "docs/gemini_dpo_implementation.md", "docs/error_recovery.md", "docs/user-case-improvement-tasks.md", "docs/troubleshooting.md", "docs/monitoring_guide.md", "docs/crawlee_comparison_search.md", "docs/CONCURRENT_SEARCH_SUMMARY.md", "docs/cotrag_improvements.md", "docs/cotrag_enhanced.md", "docs/sft_guide.md", "docs/monitoring_guide_en.md", "docs/monitoring_guide_comprehensive.md", "docs/integrated_evaluator_guide.md", "docs/vietnamese_rl_evaluator_guide.md", "docs/regression_testing_guide.md", "docs/architecture_diagram.md", "docs/playwright-setup.md", "docs/testing_guide.md", "docs/captcha-handling-setup.md", "docs/enhanced_rag_tot_cot.md", "docs/vietnamese_embedding_guide.md", "docs/security-improvements.md", "docs/tot_parameter_optimizer.md", "docs/vector_store_implementations_guide.md", "docs/advanced_error_recovery.md", "docs/tot_guide.md", "docs/redundancy_analysis.md", "docs/cot_rag_guide.md", "docs/searx_qwant_improvements.md", "docs/milvus_vector_store_guide.md", "docs/user_guide.md", "docs/gemini_dpo_pr.md", "docs/dpo_guide.md", "docs/web_search_agent_improvements.md", "docs/query_optimizer_improvements.md", "docs/query_analyzer.md", "docs/README.md", "docs/adaptive-search-cache.md", "docs/vietnamese_embeddings.md", "docs/structure_analysis.md", "docs/milvus_vietnamese_support.md", "docs/concurrent_search_README.md", "docs/model_evaluation.md", "docs/searx_qwant_analysis.md", "docs/ADVANCED_REASONING_PLAN.md", "docs/test_performance_optimization.md", "docs/enhanced_cot_guide.md", "docs/error_recovery_guide.md", "docs/reasoning_features.md", "docs/searxng-local-guide.md", "docs/tot_rag_guide.md", "docs/source_attribution_guide.md", "docs/user_guide/index.md", "docs/user_guide/usage.md", "docs/user_guide/installation.md", "docs/rl_tuning/grpo_providers_guide.md", "docs/rl_tuning/vietnamese_reward_guide.md", "docs/rl_tuning/framework_adapters_guide.md", "docs/sft/deepseek_sft.md", "docs/guides/sft_guide.md", "docs/guides/ppo_guide.md", "docs/guides/grpo_guide.md", "docs/examples/tot_reasoning_example.py", "docs/examples/lora_qlora_example.py", "docs/examples/cot_reasoning_example.py", "docs/examples/react_reasoning_example.py", "docs/examples/cotrag_adaptive_learning_example.py", "docs/examples/rl_tuning_example.py", "docs/examples/cotrag_enhanced_example.py", "docs/examples/local_model_example.py", "docs/examples/peft_comparison_example.py", "docs/examples/cotrag_advanced_strategies_example.py", "docs/examples/README.md", "docs/examples/cotrag_vietnamese_example.py", "docs/examples/rag_example.py", "docs/implementation_notes/faiss_vector_store_completion.md", "docs/implementation_notes/weaviate_vector_store_completion.md", "docs/implementation_notes/pinecone_vector_store_completion.md", "docs/tutorials/getting_started.md", "docs/tutorials/index.md", "docs/tutorials/advanced_usage.md", "docs/extensions/README.md", "docs/api/checkpoint_manager.md", "docs/api/advanced-search-api.md", "docs/api/proxy_rotation_manager.md", "docs/api/models.md", "docs/api/authentication.md", "docs/api/vietnamese-support.md", "docs/api/user_agent_rotation_manager.md", "docs/api/README.md", "docs/api/adaptive_crawler.md", "docs/api/search_performance_dashboard.md", "docs/api/rl_tuning/ppo.md", "docs/api/rl_tuning/grpo.md", "docs/api/rl_tuning/sft.md", "docs/api/evaluation/index.md", "docs/api/evaluation/vietnamese_rl_evaluator.md", "docs/api/optimization/index.md", "docs/api/optimization/vietnamese_model_quantization.md", "docs/multi_agent/advanced_querying.md", "docs/multi_agent/README.md", "docs/testing/RL_TUNING_TESTS.md", "docs/improved_sections/module_combinations.md", "docs/improved_sections/reasoning_formats_comparison.md", "docs/improved_sections/reasoning_formats_hierarchy.mermaid", "docs/improved_sections/use_cases_advanced.md", "docs/improved_sections/vietnamese_support.md", "docs/improved_sections/README_reasoning_formats.md", "docs/improved_sections/use_cases_intermediate.md", "docs/improved_sections/reasoning_formats.md", "docs/improved_sections/use_cases_basic.md", "docs/improved_sections/user-case-improved.md", "docs/improved_sections/user-case-improved-outline.md", "docs/improved_sections/reasoning_formats_hierarchy.md", "docs/improved_sections/reasoning_formats_differences.md", "docs/improved_sections/reasoning_formats_summary.md", "docs/improved_sections/error_handling.md", "docs/improved_sections/tool_integration.md", "docs/improved_sections/use_case_decision_tree.md", "docs/improved_sections/reasoning_format_decision_tree.mermaid", "docs/improved_sections/optimization_techniques.md", "docs/improved_sections/agent_systems.md", "docs/benchmarks/README.md", "examples/test_openrouter_cot.py", "examples/test_web_search_flow.py", "examples/web_search_agent_simple_example.py", "examples/vietnamese_embeddings_example.py", "examples/test_websearch_auto.py", "examples/sft_example.py", "examples/test_adaptive_cot.py", "examples/advanced_shared_memory_query.py", "examples/web_search_agent_example.py", "examples/vietnamese_llm_integration.py", "examples/memory_compression_example.py", "examples/test_moonlight.py", "examples/anthropic_reward_model_example.py", "examples/test_sqlite_vector_rag_hierarchical.py", "examples/test_openrouter.py", "examples/chatbot_fine_tuning.py", "examples/tot_example.py", "examples/faiss_rag_example.py", "examples/enhanced_web_search_example.py", "examples/enhanced_search_example.py", "examples/rag_error_recovery.py", "examples/error_recovery_example.py", "examples/test_sqlite_vector_rag_adaptive.py", "examples/advanced_error_recovery.py", "examples/test_sqlite_vector_rag_openrouter_simple.py", "examples/test_system_with_openrouter.py", "examples/cot_error_recovery.py", "examples/test_vietnamese_search.py", "examples/lora_example.py", "examples/multi_query_tot_rag_example.py", "examples/test_sqlite_vector_rag_simple.py", "examples/react_local_example.py", "examples/test_cotrag.py", "examples/test_milvus_simple.py", "examples/test_sqlite_vector_rag_ann.py", "examples/adaptive_search_cache_example.py", "examples/model_evaluation_example.py", "examples/contextual_compression_example.py", "examples/hyde_example.py", "examples/got_example.py", "examples/external_monitoring_example.py", "examples/rag_prompt_optimization_example.py", "examples/test_milvus_rag_fixed.py", "examples/error_monitoring.py", "examples/test_cohere_mistral_providers.py", "examples/recursive_tot_example.py", "examples/integrated_web_search_example.py", "examples/test_sqlite_vector_rag_semantic.py", "examples/test_enhanced_cot.py", "examples/vietnamese_agent_environment_advanced.py", "examples/streaming_response_example.py", "examples/cached_web_search_agent_example.py", "examples/test_milvus_client_rag.py", "examples/fastapi_streaming_example.py", "examples/semantic_chunking_example.py", "examples/gemini_dpo_example.py", "examples/milvus_rag_example.py", "examples/test_sqlite_rag.py", "examples/milvus_vector_store_example.py", "examples/test_milvus_embedded_rag.py", "examples/test_vietnamese_embeddings.py", "examples/rag_comparison_example.py", "examples/metrics_dashboard_example.py", "examples/test_sqlite_vector_rag_openrouter.py", "examples/sft_training_example.py", "examples/test_websearch_crawlee.py", "examples/test_sqlite_vector_rag_rtree.py", "examples/kv_cache_pruning_example.py", "examples/test_cot_rag_integration.py", "examples/milvus_vietnamese_example.py", "examples/test_sqlite_vector_rag_auto_update.py", "examples/web_search_llm_pipeline_example.py", "examples/monitoring_example.py", "examples/simple_rag.py", "examples/weaviate_rag_example.py", "examples/web_search_captcha_example.py", "examples/test_sqlite_vector_rag_improved.py", "examples/adaptive_kv_cache_example.py", "examples/knowledge_graph_example.py", "examples/deepseek_sft_example.py", "examples/ml_error_classification.py", "examples/test_vietnamese_support.py", "examples/distributed_error_recovery.py", "examples/test_sqlite_vector_rag_enhanced.py", "examples/advanced_vietnamese_search_example.py", "examples/memory_versioning_example.py", "examples/grpo_gemini_example.py", "examples/rl_tuning_example.py", "examples/vector_store_benchmark.py", "examples/test_cot_rag.py", "examples/enhanced_rag_tot_cot_example.py", "examples/tot_rag_example.py", "examples/grpo_cohere_example.py", "examples/test_searxng_local.py", "examples/vietnamese_rl_framework_integration.py", "examples/test_milvus_lite_rag.py", "examples/test_sqlite_vector_rag_new.py", "examples/tot_error_recovery.py", "examples/test_domain_evaluation.py", "examples/concurrent_search_example.py", "examples/multilingual_search_example.py", "examples/rag_error_handling_example.py", "examples/plugin_example.py", "examples/test_cot_rag_features.py", "examples/grpo_mistral_example.py", "examples/security_example.py", "examples/pinecone_rag_example.py", "examples/vietnamese_agent_environment.py", "examples/vietnamese_model_quantization_example.py", "examples/test_cotrag_simple.py", "examples/react_advanced_demo.py", "examples/test_sqlite_vector_rag_features.py", "examples/test_sqlite_vector_rag_evaluation.py", "examples/test_milvus_updated.py", "examples/rollout_strategies_example.py", "examples/README.md", "examples/vietnamese_rl_evaluation_example.py", "examples/test_cotrag_comparison.py", "examples/mixed_precision_example.py", "examples/hybrid_search_example.py", "examples/cot_rag_example.py", "examples/vietnamese_domain_compounds_example.py", "examples/ppo_training_example.py", "examples/simple_moonlight.py", "examples/test_milvus_rag.py", "examples/test_search_methods.py", "examples/integrated_evaluator_example.py", "examples/scaling_example.py", "examples/plugin_example_extended.py", "examples/test_smooth_cot.py", "examples/web_search_agent_enhanced_example.py", "examples/milvus_document_processing_example.py", "examples/test_simple_rag.py", "examples/cache_memory_optimization_example.py", "examples/optimized_rag_example.py", "examples/grpo_training_example.py", "examples/test_openrouter_minimal.py", "examples/dpo_example.py", "examples/vietnamese_trajectory_collection.py", "examples/react_reasoner_demo.py", "examples/vietnamese_rl_evaluator_example.py", "examples/test_websearch.py", "examples/test_sqlite_vector_rag.py", "examples/rl_tuning/trlx_adapter_example.py", "examples/rl_tuning/sft_example.py", "examples/rl_tuning/verl_adapter_example.py", "examples/rl_tuning/tinyzero_adapter_example.py", "examples/rl_tuning/vietnamese_reward_example.py", "examples/rl_tuning/framework_integrations_example.py", "examples/rl_tuning/openr1_adapter_example.py", "examples/rl_tuning/vietnamese_adapter_example.py", "examples/extensions/reasoning/collaborative_reasoner.py", "examples/extensions/retrieval/semantic_reranker.py", "examples/results/integrated_evaluator_english_rl.json", "examples/results/vietnamese_rl_metrics_Default_good_response.json", "examples/results/vietnamese_rl_metrics_VieBERT_good_response.json", "examples/results/vietnamese_rl_metrics_XLM-RoBERTa_good_response.json", "examples/results/vietnamese_rl_metrics_Default_vietnamese_cultural_response.json", "examples/results/vietnamese_rl_metrics_PhoBERT_good_response.json", "examples/results/integrated_evaluator_batch_results.json", "examples/results/vietnamese_rl_metrics_VieBERT_poor_response.json", "examples/results/integrated_evaluator_vietnamese_reasoning.json", "examples/results/vietnamese_rl_metrics_XLM-RoBERTa_poor_response.json", "examples/results/integrated_evaluator_english_factuality.json", "examples/results/vietnamese_rl_metrics_PhoBERT_vietnamese_cultural_response.json", "examples/results/vietnamese_rl_metrics_Default_poor_response.json", "examples/results/vietnamese_rl_metrics_VieBERT_vietnamese_cultural_response.json", "examples/results/vietnamese_rl_metrics_XLM-RoBERTa_vietnamese_cultural_response.json", "examples/results/integrated_evaluator_vietnamese_rl.json", "examples/results/vietnamese_rl_metrics_PhoBERT_poor_response.json", "examples/ppo/api_ppo_example.py", "examples/ppo/ppo_example.py", "examples/ppo/README.md", "documentation/INSTALLATION.md", "documentation/USAGE_GUIDE.md", "documentation/ARCHITECTURE.md", "documentation/PERFORMANCE_OPTIMIZATION.md", "documentation/README.md", "documentation/FAQ.md", "frontend/tsconfig.json", "frontend/package.json", "frontend/package-lock.json", "frontend/README.md", "frontend/mockups/document_management.md", "frontend/mockups/visualization_explorer.md", "frontend/mockups/ui_design_plan.md", "frontend/mockups/query_interface.md", "frontend/mockups/README.md", "frontend/docs/implementation_plan.md", "frontend/src/setupTests.ts", "frontend/src/index.css", "frontend/src/App.tsx", "frontend/src/index.tsx", "frontend/src/context/ToastContext.tsx", "frontend/src/context/ThemeContext.tsx", "frontend/src/pages/DebugPage.tsx", "frontend/src/pages/AccessibilityPage.tsx", "frontend/src/pages/Dashboard.tsx", "frontend/src/pages/AnalyticsPage.tsx", "frontend/src/pages/EnhancedQueryPage.tsx", "frontend/src/pages/SessionHistoryPage.tsx", "frontend/src/pages/QueryPage.tsx", "frontend/src/pages/ResponsiveDemo.tsx", "frontend/src/pages/ResponsiveTestPage.tsx", "frontend/src/pages/DocumentsPage.tsx", "frontend/src/pages/VisualizationsPage.tsx", "frontend/src/pages/ComparisonPage.tsx", "frontend/src/pages/ChatDemo.tsx", "frontend/src/types/index.ts", "frontend/src/i18n/useLocale.ts", "frontend/src/i18n/LanguageContext.tsx", "frontend/src/i18n/README.md", "frontend/src/i18n/i18n.ts", "frontend/src/i18n/locales/vi/translation.json", "frontend/src/i18n/locales/en/translation.json", "frontend/src/components/reasoning/EnhancedQueryInterface.tsx", "frontend/src/components/reasoning/ChatInterface.tsx", "frontend/src/components/reasoning/QueryInterface.tsx", "frontend/src/components/feedback/FeedbackCollector.tsx", "frontend/src/components/documents/DocumentUploader.tsx", "frontend/src/components/documents/DocumentDetails.tsx", "frontend/src/components/documents/CollectionManager.tsx", "frontend/src/components/documents/__tests__/DocumentUploader.test.tsx", "frontend/src/components/documents/__tests__/CollectionManager.test.tsx", "frontend/src/components/visualization/TreeVisualizer.tsx", "frontend/src/components/visualization/ReActVisualizer.tsx", "frontend/src/components/visualization/GraphVisualizer.tsx", "frontend/src/components/visualization/index.ts", "frontend/src/components/visualization/CoTVisualizer.tsx", "frontend/src/components/visualization/__tests__/TreeVisualizer.test.tsx", "frontend/src/components/visualization/__tests__/CoTVisualizer.test.tsx", "frontend/src/components/debugging/DebugStepViewer.tsx", "frontend/src/components/debugging/DebugConsole.tsx", "frontend/src/components/debugging/index.ts", "frontend/src/components/debugging/DebugSessionInfo.tsx", "frontend/src/components/debugging/SessionHistoryManager.tsx", "frontend/src/components/debugging/__tests__/DebugConsole.test.tsx", "frontend/src/components/layout/Sidebar.tsx", "frontend/src/components/layout/AppHeader.tsx", "frontend/src/components/layout/MainLayout.tsx", "frontend/src/components/layout/Footer.tsx", "frontend/src/components/layout/__tests__/Sidebar.test.tsx", "frontend/src/components/layout/__tests__/Footer.test.tsx", "frontend/src/components/layout/__tests__/MainLayout.test.tsx", "frontend/src/components/layout/__tests__/AppHeader.test.tsx", "frontend/src/components/common/LoadingIndicator.tsx", "frontend/src/components/common/FormInput.tsx", "frontend/src/components/common/ResponsiveCard.tsx", "frontend/src/components/common/CodeBlock.tsx", "frontend/src/components/common/SearchInput.tsx", "frontend/src/components/common/ContentCard.tsx", "frontend/src/components/common/Card.tsx", "frontend/src/components/common/LanguageSwitcher.tsx", "frontend/src/components/common/ResponsiveGrid.tsx", "frontend/src/components/common/AccessibilityMenu.tsx", "frontend/src/components/common/index.ts", "frontend/src/components/common/Button.tsx", "frontend/src/components/common/Form.tsx", "frontend/src/components/common/ThemeToggle.tsx", "frontend/src/components/common/__tests__/ThemeToggle.test.tsx", "frontend/src/components/common/__tests__/SearchInput.test.tsx", "frontend/src/components/common/__tests__/ContentCard.test.tsx", "frontend/src/components/common/__tests__/LoadingIndicator.test.tsx", "frontend/src/components/common/__tests__/ResponsiveCard.test.tsx", "frontend/src/components/common/__tests__/CodeBlock.test.tsx", "frontend/src/components/common/__tests__/FormInput.test.tsx", "frontend/src/theme/tokens.ts", "frontend/src/theme/ThemeContext.tsx", "frontend/src/theme/index.ts", "frontend/src/theme/README.md", "frontend/src/theme/theme.ts", "frontend/src/theme/__tests__/ThemeContext.test.tsx", "frontend/src/utils/formatters.ts", "frontend/src/utils/routeOptimization.ts", "frontend/src/utils/cacheOptimization.ts", "frontend/src/utils/ResponsiveUtils.ts", "frontend/src/utils/imageOptimization.ts", "frontend/src/utils/ResponsiveLayout.ts", "frontend/src/utils/performance.ts", "frontend/src/utils/README.md", "frontend/src/utils/__tests__/ResponsiveUtils.test.tsx", "frontend/src/api/apiService.ts", "frontend/src/hooks/useApi.ts", "frontend/src/layouts/MainLayout.tsx", "frontend/src/services/feedbackService.ts", "frontend/public/index.html", "frontend/public/robots.txt", "frontend/public/manifest.json", "frontend/public/favicon.ico", ".github/workflows/ci.yml", ".github/workflows/release.yml", "src/deep_research_core/__init__.py.new", "src/deep_research_core/__init__.py", "src/deep_research_core/rag/contextual_compression.py", "src/deep_research_core/rag/base.py", "src/deep_research_core/rag/sqlite_vector_rag.py", "src/deep_research_core/rag/__init__.py", "src/deep_research_core/rag/error_recovery_rag.py", "src/deep_research_core/rag/document_processing/context_processor.py", "src/deep_research_core/rag/document_processing/semantic_chunker.py", "src/deep_research_core/rag/document_processing/__init__.py", "src/deep_research_core/rag/search/keyword_search.py", "src/deep_research_core/rag/search/hybrid_search.py", "src/deep_research_core/rag/search/__init__.py", "src/deep_research_core/rl_tuning/providers.py", "src/deep_research_core/rl_tuning/__init__.py", "src/deep_research_core/rl_tuning/README.md", "src/deep_research_core/rl_tuning/sft/evaluation.py", "src/deep_research_core/rl_tuning/sft/deepseek_sft.py", "src/deep_research_core/rl_tuning/sft/huggingface_sft.py", "src/deep_research_core/rl_tuning/sft/base.py", "src/deep_research_core/rl_tuning/sft/qwq_sft.py", "src/deep_research_core/rl_tuning/sft/openrouter_sft.py", "src/deep_research_core/rl_tuning/sft/data_augmentation.py", "src/deep_research_core/rl_tuning/sft/openai_sft.py", "src/deep_research_core/rl_tuning/sft/__init__.py", "src/deep_research_core/rl_tuning/sft/multi_criteria_evaluation.py", "src/deep_research_core/rl_tuning/prm/base.py", "src/deep_research_core/rl_tuning/prm/anthropic_reward_model.py", "src/deep_research_core/rl_tuning/prm/data_utils.py", "src/deep_research_core/rl_tuning/prm/reward_model.py", "src/deep_research_core/rl_tuning/prm/__init__.py", "src/deep_research_core/rl_tuning/frameworks/openr1_integration.py", "src/deep_research_core/rl_tuning/frameworks/trlx_integration.py", "src/deep_research_core/rl_tuning/frameworks/base.py", "src/deep_research_core/rl_tuning/frameworks/verl_integration.py", "src/deep_research_core/rl_tuning/frameworks/tinyzero_integration.py", "src/deep_research_core/rl_tuning/frameworks/vietnamese_adapter.py", "src/deep_research_core/rl_tuning/frameworks/framework_factory.py", "src/deep_research_core/rl_tuning/frameworks/__init__.py", "src/deep_research_core/rl_tuning/frameworks/README.md", "src/deep_research_core/rl_tuning/environment/vietnamese_support.py", "src/deep_research_core/rl_tuning/environment/llm_integration.py", "src/deep_research_core/rl_tuning/environment/vietnamese_templates.py", "src/deep_research_core/rl_tuning/environment/examples.py", "src/deep_research_core/rl_tuning/environment/agent_environment.py", "src/deep_research_core/rl_tuning/environment/recovery_handler.py", "src/deep_research_core/rl_tuning/environment/llm_action_interpreter.py", "src/deep_research_core/rl_tuning/environment/timeout_handler.py", "src/deep_research_core/rl_tuning/environment/reward_shaping.py", "src/deep_research_core/rl_tuning/environment/reasoning_environment.py", "src/deep_research_core/rl_tuning/environment/adapters.py", "src/deep_research_core/rl_tuning/environment/__init__.py", "src/deep_research_core/rl_tuning/environment/observation_formatter.py", "src/deep_research_core/rl_tuning/environment/README.md", "src/deep_research_core/rl_tuning/environment/reward_functions.py", "src/deep_research_core/rl_tuning/environment/agent_environment_utils.py", "src/deep_research_core/rl_tuning/providers/grpo_providers.py", "src/deep_research_core/rl_tuning/providers/sft_providers.py", "src/deep_research_core/rl_tuning/providers/ppo_providers.py", "src/deep_research_core/rl_tuning/providers/__init__.py", "src/deep_research_core/rl_tuning/grpo/huggingface_grpo.py", "src/deep_research_core/rl_tuning/grpo/outcome_rewards.py", "src/deep_research_core/rl_tuning/grpo/mistral_grpo.py", "src/deep_research_core/rl_tuning/grpo/base.py", "src/deep_research_core/rl_tuning/grpo/policy_optimizer.py", "src/deep_research_core/rl_tuning/grpo/adaptive_reward.py", "src/deep_research_core/rl_tuning/grpo/format_rewards.py", "src/deep_research_core/rl_tuning/grpo/deepseek_grpo.py", "src/deep_research_core/rl_tuning/grpo/evaluator_reward.py", "src/deep_research_core/rl_tuning/grpo/vietnamese_reward.py", "src/deep_research_core/rl_tuning/grpo/gemini_grpo.py", "src/deep_research_core/rl_tuning/grpo/__init__.py", "src/deep_research_core/rl_tuning/grpo/cohere_grpo.py", "src/deep_research_core/rl_tuning/grpo/qwq_grpo.py", "src/deep_research_core/rl_tuning/grpo/reward_functions.py", "src/deep_research_core/rl_tuning/grpo/openrouter_grpo.py", "src/deep_research_core/rl_tuning/rollout_strategies/base.py", "src/deep_research_core/rl_tuning/rollout_strategies/mcts.py", "src/deep_research_core/rl_tuning/rollout_strategies/dfsdt.py", "src/deep_research_core/rl_tuning/rollout_strategies/__init__.py", "src/deep_research_core/rl_tuning/adapters/tinyzero_adapter.py", "src/deep_research_core/rl_tuning/adapters/verl_adapter.py", "src/deep_research_core/rl_tuning/adapters/openr1_adapter.py", "src/deep_research_core/rl_tuning/adapters/trlx_adapter.py", "src/deep_research_core/rl_tuning/adapters/__init__.py", "src/deep_research_core/rl_tuning/model_paradigm/vietnamese_support.py", "src/deep_research_core/rl_tuning/model_paradigm/complex_case_handler.py", "src/deep_research_core/rl_tuning/model_paradigm/grpo_tuner.py", "src/deep_research_core/rl_tuning/model_paradigm/performance_optimizer.py", "src/deep_research_core/rl_tuning/model_paradigm/__init__.py", "src/deep_research_core/rl_tuning/model_paradigm/environment_integration.py", "src/deep_research_core/rl_tuning/model_paradigm/rl_tuner.py", "src/deep_research_core/rl_tuning/model_paradigm/ppo_tuner.py", "src/deep_research_core/rl_tuning/model_paradigm/prm_tuner.py", "src/deep_research_core/rl_tuning/model_paradigm/rl_model_paradigm.py", "src/deep_research_core/rl_tuning/benchmarks/__init__.py", "src/deep_research_core/rl_tuning/benchmarks/agent_benchmark_evaluator.py", "src/deep_research_core/rl_tuning/dpo/openai_dpo.py", "src/deep_research_core/rl_tuning/dpo/base.py", "src/deep_research_core/rl_tuning/dpo/openrouter_dpo.py", "src/deep_research_core/rl_tuning/dpo/gemini_dpo.py", "src/deep_research_core/rl_tuning/dpo/anthropic_dpo.py", "src/deep_research_core/rl_tuning/dpo/__init__.py", "src/deep_research_core/rl_tuning/rewards/__init__.py", "src/deep_research_core/rl_tuning/rewards/agent_reward_model.py", "src/deep_research_core/rl_tuning/trajectories/trajectory_scaler.py", "src/deep_research_core/rl_tuning/trajectories/trajectory_collector.py", "src/deep_research_core/rl_tuning/trajectories/README_TRAJECTORY_SCALER.md", "src/deep_research_core/rl_tuning/trajectories/__init__.py", "src/deep_research_core/rl_tuning/trajectories/examples/collect_reasoning_trajectories.py", "src/deep_research_core/rl_tuning/trajectories/examples/trajectory_scaling_example.py", "src/deep_research_core/rl_tuning/action_space/dynamic.py", "src/deep_research_core/rl_tuning/action_space/constraints.py", "src/deep_research_core/rl_tuning/action_space/manager.py", "src/deep_research_core/rl_tuning/action_space/awareness.py", "src/deep_research_core/rl_tuning/action_space/__init__.py", "src/deep_research_core/rl_tuning/action_space/README_ACTION_SPACE_AWARENESS.md", "src/deep_research_core/rl_tuning/action_space/examples/action_space_awareness_example.py", "src/deep_research_core/rl_tuning/action_space/examples/__init__.py", "src/deep_research_core/rl_tuning/ppo/mistral_ppo.py", "src/deep_research_core/rl_tuning/ppo/gemini_ppo.py", "src/deep_research_core/rl_tuning/ppo/base.py", "src/deep_research_core/rl_tuning/ppo/openai_ppo.py", "src/deep_research_core/rl_tuning/ppo/dataset.py", "src/deep_research_core/rl_tuning/ppo/deepseek_ppo.py", "src/deep_research_core/rl_tuning/ppo/advantage_utils.py", "src/deep_research_core/rl_tuning/ppo/qwq_ppo.py", "src/deep_research_core/rl_tuning/ppo/anthropic_ppo.py", "src/deep_research_core/rl_tuning/ppo/openrouter_ppo.py", "src/deep_research_core/rl_tuning/ppo/ppo.py", "src/deep_research_core/rl_tuning/ppo/__init__.py", "src/deep_research_core/rl_tuning/ppo/cohere_ppo.py", "src/deep_research_core/reasoning/cotrag_weight_optimizer.py", "src/deep_research_core/reasoning/knowledge_graph_integration.py", "src/deep_research_core/reasoning/source_attribution.py", "src/deep_research_core/reasoning/weaviate_rag.py", "src/deep_research_core/reasoning/tot_rag.py", "src/deep_research_core/reasoning/cotrag_enhanced.py", "src/deep_research_core/reasoning/faiss_rag.py", "src/deep_research_core/reasoning/tot_optimization.py", "src/deep_research_core/reasoning/sqlite_vector_rag_vietnamese.py", "src/deep_research_core/reasoning/README_SQLITE_VECTOR_RAG_ENHANCED.md", "src/deep_research_core/reasoning/react_local.py", "src/deep_research_core/reasoning/rag_tot_cot.py", "src/deep_research_core/reasoning/rag_error_handler.py", "src/deep_research_core/reasoning/tot_evaluation.py", "src/deep_research_core/reasoning/source_attribution_rag.py", "src/deep_research_core/reasoning/prompt_templates.py", "src/deep_research_core/reasoning/sqlite_vector_rag_adaptive.py", "src/deep_research_core/reasoning/ragtotcot_weight_optimizer.py", "src/deep_research_core/reasoning/sqlite_vector_rag_hierarchical.py", "src/deep_research_core/reasoning/multi_query_decomposer.py", "src/deep_research_core/reasoning/base.py", "src/deep_research_core/reasoning/base_rag.py", "src/deep_research_core/reasoning/parallel_exploration_manager.py", "src/deep_research_core/reasoning/react.py", "src/deep_research_core/reasoning/milvus_rag.py", "src/deep_research_core/reasoning/__init__.py.new", "src/deep_research_core/reasoning/user_feedback_optimizer.py", "src/deep_research_core/reasoning/multi_source_validator.py", "src/deep_research_core/reasoning/rag_prompt_templates.py", "src/deep_research_core/reasoning/query_understanding.py", "src/deep_research_core/reasoning/multi_query_decomposition.py", "src/deep_research_core/reasoning/tot.py", "src/deep_research_core/reasoning/query_decomposer.py", "src/deep_research_core/reasoning/sqlite_vector_rag.py", "src/deep_research_core/reasoning/sqlite_vector_rag_ann.py", "src/deep_research_core/reasoning/cotrag_vietnamese.py", "src/deep_research_core/reasoning/batched_reasoning.py", "src/deep_research_core/reasoning/cotrag_recency_handler.py", "src/deep_research_core/reasoning/sqlite_vector_rag_semantic.py", "src/deep_research_core/reasoning/cot_optimization.py", "src/deep_research_core/reasoning/memory.py", "src/deep_research_core/reasoning/cot.py", "src/deep_research_core/reasoning/knowledge_graph_rag.py", "src/deep_research_core/reasoning/react_rag.py", "src/deep_research_core/reasoning/enhanced_ragtotcot_weight_optimizer.py", "src/deep_research_core/reasoning/enhanced_cotrag.py", "src/deep_research_core/reasoning/relevance_scorer.py", "src/deep_research_core/reasoning/cotrag_ambiguity_handler.py", "src/deep_research_core/reasoning/fact_checker.py", "src/deep_research_core/reasoning/advanced_evaluator.py", "src/deep_research_core/reasoning/multi_stage_reasoner.py", "src/deep_research_core/reasoning/reasoning_models_exploration.py", "src/deep_research_core/reasoning/recursive_tot.py", "src/deep_research_core/reasoning/knowledge_graph_reasoner.py", "src/deep_research_core/reasoning/pinecone_rag.py", "src/deep_research_core/reasoning/hybrid_search.py", "src/deep_research_core/reasoning/cotrag_comparison_handler.py", "src/deep_research_core/reasoning/conversation_memory.py", "src/deep_research_core/reasoning/rag.py", "src/deep_research_core/reasoning/ml_conflict_resolver.py", "src/deep_research_core/reasoning/cotrag_vietnamese_adapter.py", "src/deep_research_core/reasoning/parallel_reasoning_processor.py", "src/deep_research_core/reasoning/real_time_retriever.py", "src/deep_research_core/reasoning/cotrag_multilingual.py", "src/deep_research_core/reasoning/__init__.py", "src/deep_research_core/reasoning/adaptive_cot.py", "src/deep_research_core/reasoning/batched.py", "src/deep_research_core/reasoning/tot_errors.py", "src/deep_research_core/reasoning/feedback_loop.py", "src/deep_research_core/reasoning/semantic_chunking.py", "src/deep_research_core/reasoning/resource_aware_parallel.py", "src/deep_research_core/reasoning/self_reflection.py", "src/deep_research_core/reasoning/cotrag_adaptive_learning.py", "src/deep_research_core/reasoning/cotrag_vietnamese_embedding_adapter.py", "src/deep_research_core/reasoning/evidence_verifier.py", "src/deep_research_core/reasoning/README_SQLITE_VECTOR_RAG.md", "src/deep_research_core/reasoning/ragtotcot_query_classifier.py", "src/deep_research_core/reasoning/tree_of_thoughts.py", "src/deep_research_core/reasoning/README.md", "src/deep_research_core/reasoning/tot_mock.py", "src/deep_research_core/reasoning/batch_reasoning.py", "src/deep_research_core/reasoning/streaming.py", "src/deep_research_core/reasoning/cot_rag.py", "src/deep_research_core/reasoning/sqlite_vector_rag_rtree.py", "src/deep_research_core/reasoning/enhanced_cot.py", "src/deep_research_core/reasoning/cotrag_multi_query.py", "src/deep_research_core/reasoning/sqlite_vector_rag_enhanced.py", "src/deep_research_core/reasoning/enhanced_rag_tot_cot.py", "src/deep_research_core/reasoning/cotrag_advanced_strategies.py", "src/deep_research_core/reasoning/rag/base.py", "src/deep_research_core/reasoning/rag/sqlite_vector_rag.py", "src/deep_research_core/reasoning/rag/implementation.py", "src/deep_research_core/reasoning/rag/__init__.py", "src/deep_research_core/reasoning/optimizations/sqlite_optimizations.py", "src/deep_research_core/reasoning/optimizations/milvus_optimizations.py", "src/deep_research_core/reasoning/optimizations/__init__.py", "src/deep_research_core/reasoning/integrated/web_search_llm_pipeline.py", "src/deep_research_core/reasoning/integrated/tot_rag_integration.py", "src/deep_research_core/reasoning/integrated/web_search_rag_integration.py", "src/deep_research_core/reasoning/integrated/cot_rag_integration.py", "src/deep_research_core/reasoning/integrated/web_search_tot_integration.py", "src/deep_research_core/reasoning/integrated/__init__.py", "src/deep_research_core/reasoning/integrated/README.md", "src/deep_research_core/reasoning/integrated/web_search_cot_integration.py", "src/deep_research_core/reasoning/formats/outcome_based.py", "src/deep_research_core/reasoning/formats/react.py", "src/deep_research_core/reasoning/formats/evaluator.py", "src/deep_research_core/reasoning/formats/__init__.py", "src/deep_research_core/reasoning/formats/README.md", "src/deep_research_core/reasoning/query_classification/categories.py", "src/deep_research_core/reasoning/query_classification/base_classifier.py", "src/deep_research_core/reasoning/query_classification/ml_classifier.py", "src/deep_research_core/reasoning/query_classification/rule_based_classifier.py", "src/deep_research_core/reasoning/query_classification/__init__.py", "src/deep_research_core/reasoning/tot/parameter_optimizer.py", "src/deep_research_core/reasoning/tot/base.py", "src/deep_research_core/reasoning/tot/implementation.py", "src/deep_research_core/reasoning/tot/__init__.py", "src/deep_research_core/reasoning/trajectory_scaling/base.py", "src/deep_research_core/reasoning/trajectory_scaling/__init__.py", "src/deep_research_core/reasoning/combined/rag_tot_cot.py", "src/deep_research_core/reasoning/combined/multi_query_tot_rag.py", "src/deep_research_core/reasoning/combined/base.py", "src/deep_research_core/reasoning/combined/multi_query_tot_rag.md", "src/deep_research_core/reasoning/combined/multi_query_rag.py", "src/deep_research_core/reasoning/combined/__init__.py", "src/deep_research_core/reasoning/got/base.py", "src/deep_research_core/reasoning/got/implementation.py", "src/deep_research_core/reasoning/got/__init__.py", "src/deep_research_core/reasoning/got/README.md", "src/deep_research_core/reasoning/action_space/awareness.py", "src/deep_research_core/reasoning/action_space/__init__.py", "src/deep_research_core/multilingual/vietnamese_diacritic_processor.py", "src/deep_research_core/multilingual/vietnamese_domain_compounds.py", "src/deep_research_core/multilingual/vietnamese_prompt_optimizer.py", "src/deep_research_core/multilingual/vietnamese_nlp_integrations.py", "src/deep_research_core/multilingual/abbreviation_processor.py", "src/deep_research_core/multilingual/vietnamese_dialect_processor.py", "src/deep_research_core/multilingual/vietnamese_compound_processor.py", "src/deep_research_core/multilingual/abbreviation_rag_integration.py", "src/deep_research_core/multilingual/abbreviation_ml_detector.py", "src/deep_research_core/multilingual/vietnamese_embeddings.py", "src/deep_research_core/multilingual/advanced_vietnamese_semantic.py", "src/deep_research_core/multilingual/abbreviation_reasoning_integration.py", "src/deep_research_core/multilingual/__init__.py", "src/deep_research_core/multilingual/enhanced_vietnamese_embeddings.py", "src/deep_research_core/multilingual/abbreviation_ml_advanced.py", "src/deep_research_core/crawlers/checkpoint_manager.py", "src/deep_research_core/crawlers/media_handler.py", "src/deep_research_core/crawlers/proxy_rotation_manager.py", "src/deep_research_core/crawlers/file_handler.py", "src/deep_research_core/crawlers/distributed_crawler.py", "src/deep_research_core/crawlers/result_exporter.py", "src/deep_research_core/crawlers/site_structure_handler.py", "src/deep_research_core/crawlers/advanced_features_integration.py", "src/deep_research_core/crawlers/javascript_handler.py", "src/deep_research_core/crawlers/adaptive_crawler_captcha_integration.py", "src/deep_research_core/crawlers/form_handler.py", "src/deep_research_core/crawlers/user_agent_rotation_manager.py", "src/deep_research_core/crawlers/screenshot_manager.py", "src/deep_research_core/crawlers/adaptive_crawler.py", "src/deep_research_core/crawlers/enhanced_adaptive_crawler.py", "src/deep_research_core/crawlers/pagination_handler.py", "src/deep_research_core/crawlers/infinite_scroll_handler.py", "src/deep_research_core/tools/api.py", "src/deep_research_core/tools/document.py", "src/deep_research_core/tools/calculator.py", "src/deep_research_core/tools/base.py", "src/deep_research_core/tools/web.py", "src/deep_research_core/tools/search.py", "src/deep_research_core/tools/__init__.py", "src/deep_research_core/tools/database.py", "src/deep_research_core/tools/file.py", "src/deep_research_core/retrieval/document_chunking.py", "src/deep_research_core/retrieval/adaptive_weighting.py", "src/deep_research_core/retrieval/dummy_knowledge_graph.py", "src/deep_research_core/retrieval/base.py", "src/deep_research_core/retrieval/query_expansion.py", "src/deep_research_core/retrieval/milvus_vector_store.py", "src/deep_research_core/retrieval/reranking.py", "src/deep_research_core/retrieval/__init__.py.new", "src/deep_research_core/retrieval/pinecone_vector_store.py", "src/deep_research_core/retrieval/multilingual_retrieval.py", "src/deep_research_core/retrieval/faiss_ann_search.py", "src/deep_research_core/retrieval/hyde_retriever.py", "src/deep_research_core/retrieval/faiss_vector_store.py", "src/deep_research_core/retrieval/memory_vector_store.py", "src/deep_research_core/retrieval/semantic_chunker.py", "src/deep_research_core/retrieval/weaviate_vector_store.py", "src/deep_research_core/retrieval/vector_store.py", "src/deep_research_core/retrieval/__init__.py", "src/deep_research_core/retrieval/advanced_retrieval.py", "src/deep_research_core/retrieval/hierarchical_document.py", "src/deep_research_core/retrieval/README.md", "src/deep_research_core/retrieval/abstract_vector_store.py", "src/deep_research_core/retrieval/query_expansion/base.py", "src/deep_research_core/retrieval/query_expansion/__init__.py", "src/deep_research_core/retrieval/reranking/base.py", "src/deep_research_core/retrieval/reranking/__init__.py", "src/deep_research_core/retrieval/document_chunking/base.py", "src/deep_research_core/retrieval/document_chunking/smart_chunker.py", "src/deep_research_core/retrieval/document_chunking/semantic_chunker.py", "src/deep_research_core/retrieval/document_chunking/paragraph_chunker.py", "src/deep_research_core/retrieval/document_chunking/__init__.py", "src/deep_research_core/retrieval/document_chunking/fixed_size_chunker.py", "src/deep_research_core/retrieval/document_chunking/sentence_chunker.py", "src/deep_research_core/retrieval/vector_store/base.py", "src/deep_research_core/retrieval/vector_store/milvus_vector_store.py", "src/deep_research_core/retrieval/vector_store/__init__.py", "src/deep_research_core/retrieval/vector_store/sqlite_vector_store.py", "src/deep_research_core/providers/base_provider.py", "src/deep_research_core/providers/gemini_provider.py", "src/deep_research_core/providers/cohere_provider.py", "src/deep_research_core/providers/__init__.py", "src/deep_research_core/providers/mistral_provider.py", "src/deep_research_core/providers/mistral/provider.py", "src/deep_research_core/providers/mistral/__init__.py", "src/deep_research_core/providers/qwq/provider.py", "src/deep_research_core/providers/qwq/__init__.py", "src/deep_research_core/providers/deepseek/provider.py", "src/deep_research_core/providers/deepseek/__init__.py", "src/deep_research_core/providers/cohere/provider.py", "src/deep_research_core/providers/cohere/__init__.py", "src/deep_research_core/providers/anthropic/provider.py", "src/deep_research_core/providers/anthropic/__init__.py", "src/deep_research_core/providers/gemini/provider.py", "src/deep_research_core/providers/gemini/__init__.py", "src/deep_research_core/providers/openrouter/provider.py", "src/deep_research_core/providers/openrouter/__init__.py", "src/deep_research_core/providers/openai/provider.py", "src/deep_research_core/providers/openai/__init__.py", "src/deep_research_core/utils/error_handling.py", "src/deep_research_core/utils/react_feedback.py", "src/deep_research_core/utils/content_summarizer.py", "src/deep_research_core/utils/query_memory_system.py", "src/deep_research_core/utils/error_recovery.py", "src/deep_research_core/utils/playwright_installer.py", "src/deep_research_core/utils/resource_limiter.py", "src/deep_research_core/utils/error_analytics.py", "src/deep_research_core/utils/ml_error_classifier.py", "src/deep_research_core/utils/intelligent_error_detection.py", "src/deep_research_core/utils/compression_utils.py", "src/deep_research_core/utils/streaming_response.py", "src/deep_research_core/utils/smart_cache.py", "src/deep_research_core/utils/tool_complexity_analyzer.py", "src/deep_research_core/utils/enhanced_cache.py", "src/deep_research_core/utils/memory_management.py", "src/deep_research_core/utils/memory_manager.py", "src/deep_research_core/utils/vietnamese_captcha_handler.py", "src/deep_research_core/utils/distributed_tracing.py", "src/deep_research_core/utils/file_analyzer.py", "src/deep_research_core/utils/adaptive_rate_limiter.py", "src/deep_research_core/utils/error_visualization.py", "src/deep_research_core/utils/async_search_executor.py", "src/deep_research_core/utils/rate_limiter.py", "src/deep_research_core/utils/logging_utils.py", "src/deep_research_core/utils/searxng_errors.py", "src/deep_research_core/utils/text_processing.py", "src/deep_research_core/utils/error_monitoring.py", "src/deep_research_core/utils/structured_logging.py", "src/deep_research_core/utils/adaptive_parameters.py", "src/deep_research_core/utils/__init__.py.new", "src/deep_research_core/utils/file_downloader.py", "src/deep_research_core/utils/retry_handler.py", "src/deep_research_core/utils/question_complexity_evaluator.py", "src/deep_research_core/utils/task29_description.md", "src/deep_research_core/utils/performance_monitor.py", "src/deep_research_core/utils/query_decomposer.py", "src/deep_research_core/utils/async_content_extractor.py", "src/deep_research_core/utils/robots_parser.py", "src/deep_research_core/utils/caching.py", "src/deep_research_core/utils/language_detector.py", "src/deep_research_core/utils/search_performance_dashboard.py", "src/deep_research_core/utils/plugin_manager.py", "src/deep_research_core/utils/search_worker_pool.py", "src/deep_research_core/utils/vietnamese_utils.py", "src/deep_research_core/utils/error_strategy_optimizer.py", "src/deep_research_core/utils/react_cache.py", "src/deep_research_core/utils/domain_credibility_updater.py", "src/deep_research_core/utils/interaction_history_optimizer.py", "src/deep_research_core/utils/query_analyzer.py", "src/deep_research_core/utils/captcha_handler.py", "src/deep_research_core/utils/web_search_metrics.py", "src/deep_research_core/utils/distributed_error_recovery.py", "src/deep_research_core/utils/html_processor.py", "src/deep_research_core/utils/text_similarity.py", "src/deep_research_core/utils/advanced_recovery_strategies.py", "src/deep_research_core/utils/search_utils.py", "src/deep_research_core/utils/advanced_cache.py", "src/deep_research_core/utils/content_extraction_utils.py", "src/deep_research_core/utils/tool_selection.py", "src/deep_research_core/utils/file_format_handler.py", "src/deep_research_core/utils/types.py", "src/deep_research_core/utils/performance_optimizer.py", "src/deep_research_core/utils/context_based_tool_selector.py", "src/deep_research_core/utils/memory_integration.py", "src/deep_research_core/utils/plugin_integration.py", "src/deep_research_core/utils/__init__.py", "src/deep_research_core/utils/local_query_analyzer.py", "src/deep_research_core/utils/health_checker.py", "src/deep_research_core/utils/url_utils.py", "src/deep_research_core/utils/specialized_extractors.py", "src/deep_research_core/utils/test_utils.py", "src/deep_research_core/utils/feedback_collector.py", "src/deep_research_core/utils/vietnamese_nlp.py", "src/deep_research_core/utils/cache_memory_manager.py", "src/deep_research_core/utils/query_analyzer_cache.py", "src/deep_research_core/utils/cache_manager.py", "src/deep_research_core/utils/advanced_react_cache.py", "src/deep_research_core/utils/performance_integration.py", "src/deep_research_core/utils/update_api_warnings.py", "src/deep_research_core/utils/bot_avoidance.py", "src/deep_research_core/utils/resource_manager.py", "src/deep_research_core/utils/README.md", "src/deep_research_core/utils/error_learning.py", "src/deep_research_core/utils/multilingual_utils.py", "src/deep_research_core/utils/interaction_history.py", "src/deep_research_core/utils/vietnamese_text_processor.py", "src/deep_research_core/utils/feedback_learner.py", "src/deep_research_core/utils/dependency_manager.py", "src/deep_research_core/utils/alerting.py", "src/deep_research_core/utils/reasoning_error_recovery.py", "src/deep_research_core/utils/performance.py", "src/deep_research_core/utils/web_search_file_processor.py", "src/deep_research_core/utils/performance_metrics.py", "src/deep_research_core/utils/external_monitoring/grafana_dashboard.py", "src/deep_research_core/utils/external_monitoring/prometheus_exporter.py", "src/deep_research_core/utils/external_monitoring/__init__.py", "src/deep_research_core/visualization/interactive_visualizer.py", "src/deep_research_core/visualization/tot_rag_visualizer.py", "src/deep_research_core/evaluation/integrated_evaluator.py", "src/deep_research_core/evaluation/rl_evaluator.py", "src/deep_research_core/evaluation/reasoning_model_evaluator.py", "src/deep_research_core/evaluation/factuality_evaluator.py", "src/deep_research_core/evaluation/evaluation_dataset.py", "src/deep_research_core/evaluation/ragtotcot_analyzer.py", "src/deep_research_core/evaluation/evaluator.py", "src/deep_research_core/evaluation/stepwise_reasoning_evaluator.py", "src/deep_research_core/evaluation/vietnamese_metrics.py", "src/deep_research_core/evaluation/cot_evaluator.py", "src/deep_research_core/evaluation/domain_metrics.py", "src/deep_research_core/evaluation/__init__.py", "src/deep_research_core/evaluation/vietnamese_benchmark.py", "src/deep_research_core/evaluation/metrics.py", "src/deep_research_core/evaluation/vietnamese_rl_evaluator.py", "src/deep_research_core/evaluation/base_evaluator.py", "src/deep_research_core/evaluation/domain_evaluator.py", "src/deep_research_core/evaluation/README.md", "src/deep_research_core/evaluation/vietnamese_quality_metrics.py", "src/deep_research_core/evaluation/reasoning_evaluator.py", "src/deep_research_core/evaluation/qa_evaluator.py", "src/deep_research_core/evaluation/cotrag_error_analyzer.py", "src/deep_research_core/agents/enhanced_result_ranker.py", "src/deep_research_core/agents/web_search_comparison.py", "src/deep_research_core/agents/synthesizer.py", "src/deep_research_core/agents/web_search_cache_advanced.py", "src/deep_research_core/agents/optimized_search_engine.py", "src/deep_research_core/agents/llm_integration.py", "src/deep_research_core/agents/error_recovery.py", "src/deep_research_core/agents/media_handler.py", "src/deep_research_core/agents/researcher.py", "src/deep_research_core/agents/web_search_agent_improved.py", "src/deep_research_core/agents/vietnamese_search_integration.py", "src/deep_research_core/agents/document_extractors_extended.py", "src/deep_research_core/agents/evaluate_question_complexity.py", "src/deep_research_core/agents/orchestrator.py", "src/deep_research_core/agents/advanced_web_search_features.py", "src/deep_research_core/agents/vietnamese_nlp_fallback.py", "src/deep_research_core/agents/performance_optimizations.py", "src/deep_research_core/agents/performance_tracker.py", "src/deep_research_core/agents/enhanced_web_search_agent.py", "src/deep_research_core/agents/search_result_processor.py", "src/deep_research_core/agents/multimedia_search.py", "src/deep_research_core/agents/error_handler.py", "src/deep_research_core/agents/network_utils.py", "src/deep_research_core/agents/base_search_agent.py", "src/deep_research_core/agents/web_search_error_recovery.py", "src/deep_research_core/agents/advanced_crawler_methods.py", "src/deep_research_core/agents/semantic_analyzer_integration.py", "src/deep_research_core/agents/smart_cache.py", "src/deep_research_core/agents/search_cache_integration.py", "src/deep_research_core/agents/vietnamese_search_methods.py", "src/deep_research_core/agents/vietnamese_search_integration_improved.py", "src/deep_research_core/agents/file_handler.py", "src/deep_research_core/agents/semantic_web_search_cache.py", "src/deep_research_core/agents/test_vietnamese_search.py", "src/deep_research_core/agents/answer_quality_evaluator_integration.py", "src/deep_research_core/agents/web_search_feedback_learner.py", "src/deep_research_core/agents/content_extractor.py", "src/deep_research_core/agents/adaptive_crawler.py.new", "src/deep_research_core/agents/web_search_cache_enhanced.py", "src/deep_research_core/agents/query_expansion.py", "src/deep_research_core/agents/rate_limiter.py", "src/deep_research_core/agents/academic_search_methods.py", "src/deep_research_core/agents/quality_evaluator.py", "src/deep_research_core/agents/web_search_agent_cache.py", "src/deep_research_core/agents/document_extractors.py", "src/deep_research_core/agents/web_search_agent_base.py", "src/deep_research_core/agents/web_search_agent.py", "src/deep_research_core/agents/web_search_methods.py", "src/deep_research_core/agents/search_engine_interface.py", "src/deep_research_core/agents/dynamic_content_handler.py", "src/deep_research_core/agents/README_VI_IMPROVEMENTS.md", "src/deep_research_core/agents/web_search_agent_local.py", "src/deep_research_core/agents/crawlee_comparison.js", "src/deep_research_core/agents/question_complexity_evaluator.py", "src/deep_research_core/agents/file_processor_fallback.py", "src/deep_research_core/agents/additional_search_engines.py", "src/deep_research_core/agents/search_engines.py", "src/deep_research_core/agents/file_processor_integration.py", "src/deep_research_core/agents/searxng_search.py", "src/deep_research_core/agents/resource_monitor.py", "src/deep_research_core/agents/caching.py", "src/deep_research_core/agents/README_IMPROVEMENTS.md", "src/deep_research_core/agents/decompose_query.py", "src/deep_research_core/agents/advanced_crawlee.py", "src/deep_research_core/agents/web_search_agent_local_helpers.py", "src/deep_research_core/agents/select_box_handler.py", "src/deep_research_core/agents/batch_search_optimizer.py", "src/deep_research_core/agents/performance_optimizer_improved.py", "src/deep_research_core/agents/crawlee_search.py", "src/deep_research_core/agents/adaptive_crawler_integration.py", "src/deep_research_core/agents/web_search_crawlee.py", "src/deep_research_core/agents/llm_search_analyzer.py", "src/deep_research_core/agents/advanced_crawler.py", "src/deep_research_core/agents/media_search.py", "src/deep_research_core/agents/web_search_agent_local_improvements.py", "src/deep_research_core/agents/javascript_handler.py", "src/deep_research_core/agents/unified_web_search_agent.py", "src/deep_research_core/agents/sentiment_analyzer.py", "src/deep_research_core/agents/query_analyzer.py", "src/deep_research_core/agents/link_queue_manager.py", "src/deep_research_core/agents/multimedia_search_fallback.py", "src/deep_research_core/agents/network_manager.py", "src/deep_research_core/agents/playwright_search.py", "src/deep_research_core/agents/auth_manager.py", "src/deep_research_core/agents/enhanced_query_analyzer.py", "src/deep_research_core/agents/query_decomposer_integration.py", "src/deep_research_core/agents/form_handler.py", "src/deep_research_core/agents/module_integration_manager.py", "src/deep_research_core/agents/web_search_memory_optimizer.py", "src/deep_research_core/agents/vietnamese_search_engines.py", "src/deep_research_core/agents/web_search_error_handler.py", "src/deep_research_core/agents/crawlee_scraper.js", "src/deep_research_core/agents/advanced_summarizer.py", "src/deep_research_core/agents/web_search_agent_local_plugin_integration.py", "src/deep_research_core/agents/enhanced_web_search_cache.py", "src/deep_research_core/agents/searxng_crawlee_integration.py", "src/deep_research_core/agents/crawlee_integration.py", "src/deep_research_core/agents/config.py", "src/deep_research_core/agents/proxy_manager.py", "src/deep_research_core/agents/integrated_web_search_agent.py", "src/deep_research_core/agents/specialized_search_methods.py", "src/deep_research_core/agents/semantic_analyzer.py", "src/deep_research_core/agents/__init__.py", "src/deep_research_core/agents/concurrent_processor.py", "src/deep_research_core/agents/parallel_search.py", "src/deep_research_core/agents/search_result_optimizer.py", "src/deep_research_core/agents/vietnamese_nlp.py", "src/deep_research_core/agents/plugin_system.py", "src/deep_research_core/agents/result_ranking_integration.py", "src/deep_research_core/agents/captcha_solver.py", "src/deep_research_core/agents/captcha_handler_integration.py", "src/deep_research_core/agents/extract_content_improved.py", "src/deep_research_core/agents/web_search_vietnamese.py", "src/deep_research_core/agents/adaptive_crawler.py", "src/deep_research_core/agents/multilingual_crawler.py", "src/deep_research_core/agents/optimized_cache.py", "src/deep_research_core/agents/language_utils.py", "src/deep_research_core/agents/multimedia_search_integration.py", "src/deep_research_core/agents/test_plugin_integration.py", "src/deep_research_core/agents/README.md", "src/deep_research_core/agents/memory_optimized_crawler.py", "src/deep_research_core/agents/query_optimizer.py", "src/deep_research_core/agents/deep_crawl_improved.py", "src/deep_research_core/agents/web_search_agent_merged.py", "src/deep_research_core/agents/pagination_handler.py", "src/deep_research_core/agents/advanced_error_strategies.py", "src/deep_research_core/agents/test_web_search_agent_merged.py", "src/deep_research_core/agents/answer_quality_evaluator.py", "src/deep_research_core/agents/adaptive_scraper_integration.py", "src/deep_research_core/agents/optimized_crawlee.py", "src/deep_research_core/agents/README_MERGE.md", "src/deep_research_core/agents/result_analyzer.py", "src/deep_research_core/agents/web_search_file_processor.py", "src/deep_research_core/agents/improved_deep_research.py", "src/deep_research_core/agents/integrated_search_engine.py", "src/deep_research_core/agents/multilingual_support.py", "src/deep_research_core/agents/academic_search.py", "src/deep_research_core/agents/web_search_cache.py", "src/deep_research_core/agents/deep_research_integration.py", "src/deep_research_core/agents/domain_analyzers/domain_specific_analyzer.py", "src/deep_research_core/agents/domain_analyzers/__init__.py", "src/deep_research_core/agents/query_expansion/semantic_expansion.py", "src/deep_research_core/agents/query_expansion/advanced_nlp.py", "src/deep_research_core/agents/query_expansion/__init__.py", "src/deep_research_core/agents/search_engines/specialized_engines_part2.py", "src/deep_research_core/agents/search_engines/specialized_engines_part3.py", "src/deep_research_core/agents/search_engines/specialized_engines.py", "src/deep_research_core/agents/search_engines/__init__.py", "src/deep_research_core/agents/lightweight/vietnamese_nlp_lite.py", "src/deep_research_core/agents/lightweight/__init__.py", "src/deep_research_core/agents/nlp_analyzers/transformer_sentiment_analyzer.py", "src/deep_research_core/agents/nlp_analyzers/__init__.py", "src/deep_research_core/agents/utils/parallel_util.py", "src/deep_research_core/agents/utils/retry_util.py", "src/deep_research_core/agents/utils/__init__.py", "src/deep_research_core/agents/error_strategies/engine_specific_strategies_part2.py", "src/deep_research_core/agents/error_strategies/engine_specific_strategies_part3.py", "src/deep_research_core/agents/error_strategies/__init__.py", "src/deep_research_core/agents/error_strategies/engine_specific_strategies.py", "src/deep_research_core/agents/plugins/performance_optimization_plugin.py", "src/deep_research_core/agents/plugins/result_ranking_plugin.py", "src/deep_research_core/agents/plugins/query_optimization_plugin.py", "src/deep_research_core/agents/plugins/multilingual_plugin.py", "src/deep_research_core/agents/plugins/content_filter_plugin.py", "src/deep_research_core/agents/plugins/data/inappropriate_keywords.txt", "src/deep_research_core/agents/plugins/data/ad_keywords.txt", "src/deep_research_core/agents/plugins/data/spam_keywords.txt", "src/deep_research_core/agents/plugins/data/metrics/performance_metrics.json", "src/deep_research_core/agents/distributed/__init__.py", "src/deep_research_core/agents/distributed/parallel_search.py", "src/deep_research_core/agents/distributed/distributed_cache.py", "src/deep_research_core/agents/tests/test_web_search_agent_integration.py", "src/deep_research_core/agents/tests/test_web_search_agent_simple.py", "src/deep_research_core/agents/tests/test_web_search_query_analysis.py", "src/deep_research_core/agents/tests/__init__.py", "src/deep_research_core/agents/tests/test_web_search_agent_simple_features.py", "src/deep_research_core/agents/tests/integration/test_web_search_integration.py", "src/deep_research_core/agents/tests/unit/test_web_search_agent_optimizations.py", "src/deep_research_core/agents/tests/unit/test_improvements.py", "src/deep_research_core/agents/tests/unit/test_vietnamese_nlp.py", "src/deep_research_core/agents/tests/unit/test_web_search_agent_comprehensive.py", "src/deep_research_core/agents/tests/unit/__init__.py", "src/deep_research_core/agents/tests/unit/test_rate_limiter.py", "src/deep_research_core/agents/tests/unit/test_utils.py", "src/deep_research_core/agents/tests/unit/test_web_search_agent_improvements.py", "src/deep_research_core/agents/tests/performance/test_websearch_performance.py", "src/deep_research_core/security/authentication.py", "src/deep_research_core/security/__init__.py", "src/deep_research_core/security/authorization.py", "src/deep_research_core/web/app.py", "src/deep_research_core/web/auth.py", "src/deep_research_core/web/error_handler.py", "src/deep_research_core/web/sft_routes.py", "src/deep_research_core/web/grpo_routes.py", "src/deep_research_core/web/ppo_routes.py", "src/deep_research_core/web/error_api.py", "src/deep_research_core/web/config.py", "src/deep_research_core/web/api_routes.py", "src/deep_research_core/web/templates/base.html", "src/deep_research_core/web/templates/dashboard.html", "src/deep_research_core/web/templates/index.html", "src/deep_research_core/web/templates/errors/400.html", "src/deep_research_core/web/templates/errors/404.html", "src/deep_research_core/web/templates/errors/403.html", "src/deep_research_core/web/templates/errors/500.html", "src/deep_research_core/web/templates/errors/401.html", "src/deep_research_core/web/templates/sft/configure.html", "src/deep_research_core/web/templates/sft/index.html", "src/deep_research_core/web/templates/sft/train.html", "src/deep_research_core/web/templates/sft/evaluate.html", "src/deep_research_core/web/templates/sft/status.html", "src/deep_research_core/web/templates/sft/evaluate_result.html", "src/deep_research_core/web/templates/feedback/ab_testing.html", "src/deep_research_core/web/templates/feedback/view.html", "src/deep_research_core/web/templates/feedback/widget.html", "src/deep_research_core/web/templates/feedback/list.html", "src/deep_research_core/web/templates/feedback/analytics.html", "src/deep_research_core/web/templates/feedback/create_ab_test.html", "src/deep_research_core/web/templates/feedback/dashboard.html", "src/deep_research_core/web/templates/feedback/view_ab_test.html", "src/deep_research_core/web/templates/feedback/improvements.html", "src/deep_research_core/web/templates/grpo/configure.html", "src/deep_research_core/web/templates/grpo/index.html", "src/deep_research_core/web/templates/grpo/train.html", "src/deep_research_core/web/templates/grpo/evaluate.html", "src/deep_research_core/web/templates/grpo/status.html", "src/deep_research_core/web/templates/grpo/evaluate_result.html", "src/deep_research_core/web/templates/api/test.html", "src/deep_research_core/web/templates/api/keys.html", "src/deep_research_core/web/templates/api/index.html", "src/deep_research_core/web/templates/api/create_key.html", "src/deep_research_core/web/templates/api/documentation.html", "src/deep_research_core/web/templates/api/reasoning_result.html", "src/deep_research_core/web/templates/api/test_result.html", "src/deep_research_core/web/templates/api/reasoning.html", "src/deep_research_core/web/templates/api/advanced_query.html", "src/deep_research_core/web/templates/debug/token_monitor.html", "src/deep_research_core/web/templates/debug/reasoning_path.html", "src/deep_research_core/web/templates/debug/index.html", "src/deep_research_core/web/templates/debug/performance.html", "src/deep_research_core/web/templates/admin/error_detail.html", "src/deep_research_core/web/templates/admin/error_dashboard.html", "src/deep_research_core/web/templates/ppo/configure.html", "src/deep_research_core/web/templates/ppo/index.html", "src/deep_research_core/web/templates/ppo/train.html", "src/deep_research_core/web/templates/ppo/evaluate.html", "src/deep_research_core/web/templates/ppo/status.html", "src/deep_research_core/web/templates/ppo/evaluate_result.html", "src/deep_research_core/web/routes/feedback_routes.py", "src/deep_research_core/web/routes/api_routes.py", "src/deep_research_core/web/routes/debug_routes.py", "src/deep_research_core/web/utils/feedback_improvement.py", "src/deep_research_core/web/utils/feedback_utils.py", "src/deep_research_core/web/utils/performance_profiler.py", "src/deep_research_core/web/utils/feedback_analytics.py", "src/deep_research_core/web/utils/ab_testing.py", "src/deep_research_core/web/static/js/feedback.js", "src/deep_research_core/web/static/js/feedback_analytics.js", "src/deep_research_core/web/static/js/cot_visualization.js", "src/deep_research_core/web/static/js/performance_profiler.js", "src/deep_research_core/web/static/js/react_visualization.js", "src/deep_research_core/web/static/js/error_visualization.js", "src/deep_research_core/web/static/js/tot_visualization.js", "src/deep_research_core/web/static/js/advanced_query_builder.js", "src/deep_research_core/web/static/js/token_usage_monitor.js", "src/deep_research_core/web/static/js/reasoning_path_debugger.js", "src/deep_research_core/web/static/js/rag_visualization.js", "src/deep_research_core/plugins/base_plugin.py", "src/deep_research_core/plugins/performance_optimization_plugin.py", "src/deep_research_core/plugins/result_ranking_plugin.py", "src/deep_research_core/plugins/user_psychology_plugin.py", "src/deep_research_core/plugins/query_optimization_plugin.py", "src/deep_research_core/plugins/multilingual_plugin.py", "src/deep_research_core/plugins/search_statistics_plugin.py", "src/deep_research_core/plugins/url_filter_plugin.py", "src/deep_research_core/plugins/semantic_analysis_plugin.py", "src/deep_research_core/plugins/content_filter_plugin.py", "src/deep_research_core/plugins/media_extraction_plugin.py", "src/deep_research_core/plugins/content_enhancement_plugin.py", "src/deep_research_core/plugins/captcha_handler_plugin.py", "src/deep_research_core/models/reasoning_model_evaluator.py", "src/deep_research_core/models/base.py", "src/deep_research_core/models/__init__.py.new", "src/deep_research_core/models/qwq_32b.py", "src/deep_research_core/models/embeddings.py", "src/deep_research_core/models/__init__.py", "src/deep_research_core/models/deepseek_r1.py", "src/deep_research_core/models/model_selector.py", "src/deep_research_core/models/README.md", "src/deep_research_core/models/base_reasoning_model.py", "src/deep_research_core/models/gpt_o1.py", "src/deep_research_core/models/embedding/__init__.py", "src/deep_research_core/models/embedding/embedding_model.py", "src/deep_research_core/models/embeddings/openai_embeddings.py", "src/deep_research_core/models/embeddings/base.py", "src/deep_research_core/models/embeddings/vietnamese_multilingual_e5.py", "src/deep_research_core/models/embeddings/vietnamese_phobert.py", "src/deep_research_core/models/embeddings/vietnamese_xlm_roberta.py", "src/deep_research_core/models/embeddings/__init__.py", "src/deep_research_core/models/embeddings/vietnamese_viebert.py", "src/deep_research_core/models/embeddings/multilingual_e5.py", "src/deep_research_core/models/api/openai.py", "src/deep_research_core/models/api/mistral.py", "src/deep_research_core/models/api/base.py", "src/deep_research_core/models/api/openrouter.py", "src/deep_research_core/models/api/anthropic.py", "src/deep_research_core/models/api/google.py", "src/deep_research_core/models/api/deepseek.py", "src/deep_research_core/models/api/__init__.py", "src/deep_research_core/models/api/cohere.py", "src/deep_research_core/models/api/README.md", "src/deep_research_core/models/api/qwq.py", "src/deep_research_core/models/api/anthropic/provider.py", "src/deep_research_core/models/api/anthropic/__init__.py", "src/deep_research_core/models/api/openrouter/provider.py", "src/deep_research_core/models/api/openrouter/__init__.py", "src/deep_research_core/models/api/openai/provider.py", "src/deep_research_core/models/api/openai/__init__.py", "src/deep_research_core/models/local/mistral_model.py", "src/deep_research_core/models/local/base.py", "src/deep_research_core/models/local/phi_model.py", "src/deep_research_core/models/local/model_loader.py", "src/deep_research_core/models/local/quantization.py", "src/deep_research_core/models/local/__init__.py", "src/deep_research_core/models/local/llama_model.py", "src/deep_research_core/models/local/README.md", "src/deep_research_core/optimization/__init__.py", "src/deep_research_core/optimization/peft_benchmark.py", "src/deep_research_core/optimization/ia3/__init__.py", "src/deep_research_core/optimization/ia3/README.md", "src/deep_research_core/optimization/memory/gradient_checkpointing.py", "src/deep_research_core/optimization/memory/adaptive_kv_cache.py", "src/deep_research_core/optimization/memory/kv_cache_pruning.py", "src/deep_research_core/optimization/memory/__init__.py", "src/deep_research_core/optimization/memory/gradient_accumulation.py", "src/deep_research_core/optimization/memory/token_importance.py", "src/deep_research_core/optimization/memory/README.md", "src/deep_research_core/optimization/memory/chunked_attention.py", "src/deep_research_core/optimization/memory/pruning_hooks.py", "src/deep_research_core/optimization/parallel/inference.py", "src/deep_research_core/optimization/parallel/thread_management.py", "src/deep_research_core/optimization/parallel/__init__.py", "src/deep_research_core/optimization/parallel/workload.py", "src/deep_research_core/optimization/parallel/training.py", "src/deep_research_core/optimization/lora/evaluation.py", "src/deep_research_core/optimization/lora/model_adapters.py", "src/deep_research_core/optimization/lora/qlora.py", "src/deep_research_core/optimization/lora/base.py", "src/deep_research_core/optimization/lora/merging.py", "src/deep_research_core/optimization/lora/__init__.py", "src/deep_research_core/optimization/lora/training.py", "src/deep_research_core/optimization/caching/redis_provider.py", "src/deep_research_core/optimization/caching/predictive_cache.py", "src/deep_research_core/optimization/caching/semantic_cache.py", "src/deep_research_core/optimization/caching/cache_prefetcher.py", "src/deep_research_core/optimization/caching/access_pattern_analyzer.py", "src/deep_research_core/optimization/caching/cache_decorator.py", "src/deep_research_core/optimization/caching/cache_warmer.py", "src/deep_research_core/optimization/caching/warming_strategies.py", "src/deep_research_core/optimization/caching/__init__.py", "src/deep_research_core/optimization/caching/distributed_cache.py", "src/deep_research_core/optimization/caching/adaptive_search_cache.py", "src/deep_research_core/optimization/caching/eviction_policies.py", "src/deep_research_core/optimization/prefix_tuning/base.py", "src/deep_research_core/optimization/prefix_tuning/__init__.py", "src/deep_research_core/optimization/prompt_tuning/base.py", "src/deep_research_core/optimization/prompt_tuning/__init__.py", "src/deep_research_core/optimization/mixed_precision/trainer.py", "src/deep_research_core/optimization/mixed_precision/utils.py", "src/deep_research_core/optimization/mixed_precision/config.py", "src/deep_research_core/optimization/mixed_precision/__init__.py", "src/deep_research_core/optimization/adapter/base.py", "src/deep_research_core/optimization/adapter/__init__.py", "src/deep_research_core/optimization/quantization/mixed_precision_quantization.py", "src/deep_research_core/optimization/quantization/vietnamese_model_quantization.py", "src/deep_research_core/optimization/quantization/__init__.py", "src/deep_research_core/api/app.py", "src/deep_research_core/api/__init__.py.new", "src/deep_research_core/api/main.py", "src/deep_research_core/api/__init__.py", "src/deep_research_core/api/routes/visualization.py", "src/deep_research_core/api/routes/__init__.py", "src/deep_research_core/api/models/__init__.py", "src/deep_research_core/multi_agent/roles.py", "src/deep_research_core/multi_agent/enhanced_bayesian_consensus.py", "src/deep_research_core/multi_agent/core.py", "src/deep_research_core/multi_agent/task_decomposer.py", "src/deep_research_core/multi_agent/domain_roles.py", "src/deep_research_core/multi_agent/bayesian_consensus.py", "src/deep_research_core/multi_agent/memory_compression.py", "src/deep_research_core/multi_agent/__init__.py", "src/deep_research_core/multi_agent/query_parser.py", "src/deep_research_core/multi_agent/hierarchical_task_representation.py", "src/deep_research_core/multi_agent/memory_version.py", "src/deep_research_core/multi_agent/README.md", "src/deep_research_core/multi_agent/dependency_tracker.py", "src/deep_research_core/analyzers/semantic_analyzer.py", "src/deep_research_core/scaling/load_balancer.py", "src/deep_research_core/scaling/__init__.py", "src/deep_research_core/scaling/sharding.py", "src/deep_research_core/scrapers/adaptive_scraper.py", "src/deep_research_core/config/monitoring_config.py", "src/deep_research_core/config/providers.py", "src/deep_research_core/config/app_config.py", "src/deep_research_core/config/__init__.py", "src/deep_research_core/config/models.py", "src/deep_research_core/config/api_warnings.py", "src/deep_research_core/config/README.md", "src/deep_research_core/search/multimedia_search.py", "src/deep_research_core/search/video_search.py", "src/deep_research_core/search/elasticsearch_integration.py", "src/deep_research_core/search/search_engines_integration.py", "src/deep_research_core/search/typesense_integration.py", "src/deep_research_core/search/image_search.py", "src/deep_research_core/search/document_search.py", "src/deep_research_core/search/meilisearch_integration.py", "src/deep_research_core/search/search_engine_selector.py", "src/deep_research_core/search/multimedia_search_integration.py", "src/deep_research_core/search/solr_integration.py", "src/deep_research_core/search/audio_search.py", "src/deep_research_core/nlp/vietnamese_nlp_integration.py", "src/deep_research_core/nlp/nlp_integration.py", "src/deep_research_core/nlp/__init__.py", "src/deep_research_core/nlp/nlp_engine.py", "src/deep_research_core/nlp/data/vietnamese_stopwords.txt", "src/deep_research_core/nlp/data/__init__.py", "src/deep_research_core/nlp/data/synonyms.json", "src/output/basic_search_results.json", "src/output/html_fallback_results.json", "src/scripts/run_evaluation.py", "src/scripts/run_enhanced_cotrag.py", "src/scripts/main.py", "src/scripts/test_web_search_improvements.py", "src/scripts/README.md", "src/tests/test_adaptive_kv_cache.py", "src/tests/test_consensus.py", "src/tests/test_token_importance.py", "src/tests/test_kv_cache_pruning.py", "output/basic_search_results.json", "output/html_fallback_results.json", "tasks/10_implementation_plans.md", "tasks/04_advanced_reasoning_techniques.md", "tasks/WebSearchAgentLocal_Tasks.md", "tasks/09_optimization_module.md", "tasks/adaptive_crawler_integration.md", "tasks/01_project_status.md", "tasks/03_basic_reasoning_formats.md", "tasks/05_agent_features.md", "tasks/02_completed_modules.md", "tasks/07_web_module.md", "tasks/12_general_optimization.md", "tasks/11_future_development.md", "tasks/web_search_agent_improvements.md", "tasks/08_multi_agent_module.md", "tasks/06_advanced_feature_integration.md", "temp_implementation/trajectory_scaler.py", "temp_implementation/openrouter_dpo_test.py", "temp_implementation/api_agent_reward_model.py", "temp_implementation/ppo_integration.md", "temp_implementation/agent_reward_model.py", "static/visualizer.html", "scripts/analyze_test_performance.py", "scripts/create_evaluation_dataset.py", "scripts/setup_vietnamese_support.py", "scripts/run_tests.sh", "scripts/evaluate_vietnamese_embeddings.py", "scripts/evaluate_cotrag_performance.py", "scripts/run_regression_tests.sh", "test/test_advanced_vietnamese_semantic.py", "test/test_network_manager.py", "benchmarks/vector_store_benchmark.py", "tests/run_all_tests.py", "tests/test_vietnamese_diacritic.py", "tests/test_cot_optimization.py", "tests/test_chunked_attention.py", "tests/test_captcha_handler.py", "tests/test_result_analyzer.py", "tests/test_adaptive_rate_limiter.py", "tests/test_domain_roles.py", "tests/test_adaptive_crawler_captcha_integration_full.py", "tests/test_enhanced_web_search_agent.py", "tests/test_gradient_accumulation.py", "tests/test_gradient_checkpointing.py", "tests/test_rag_prompt_templates.py", "tests/test_llm_integration.py", "tests/test_vietnamese_dialect.py", "tests/test_vietnamese_environment_support.py", "tests/test_bot_avoidance.py", "tests/test_search_utils.py", "tests/test_ragtotcot_weight_optimizer.py", "tests/test_web_search_agent_local_additional.py", "tests/test_vietnamese_utils.py", "tests/test_file_format_handler.py", "tests/test_rag_tot_cot_integration.py", "tests/test_captcha_fallback.py", "tests/test_domain_specific_analyzer.py", "tests/test_web_search_agent_local_async.py", "tests/test_web_search_agent_local_multimedia.py", "tests/test_error_recovery.py", "tests/test_vietnamese_trajectory_collection.py", "tests/test_adaptive_scraper.py", "tests/test_vietnamese_compound_processor.py", "tests/test_performance_optimization.py", "tests/test_rl_model_paradigm_vietnamese.py", "tests/test_adaptive_crawler_upgrade.py", "tests/test_error_recovery_integration.py", "tests/test_enhanced_result_ranker.py", "tests/test_improved_search.py", "tests/view_search_optimizer_results.py", "tests/README_TESTS.md", "tests/test_qa_evaluator.py", "tests/test_spa_handling.py", "tests/test_agent_environment.py", "tests/test_content_summarizer.py", "tests/test_adaptive_crawler_basic.py", "tests/test_search_result_optimizer.py", "tests/test_improvements.py", "tests/test_timeout_recovery.py", "tests/test_enhanced_cache.py", "tests/test_sqlite_vector_store.py", "tests/test_enhanced_cot_advanced.py", "tests/test_specialized_extractors.py", "tests/test_rag_tot_cot.py", "tests/test_query_memory_system.py", "tests/run_rl_tuning_tests.py", "tests/test_vietnamese_support.py", "tests/test_shared_memory_query.py", "tests/test_error_handling.py", "tests/test_resource_manager.py", "tests/test_specialized_search.py", "tests/test_react.py", "tests/test_multilingual_crawler.py", "tests/test_plugin_manager.py", "tests/test_nlp_analyzers.py", "tests/test_cache_manager.py", "tests/test_captcha_handler_integration.py", "tests/test_infinite_scroll.py", "tests/test_advanced_features.py", "tests/test_integrated_web_search_agent_fixes.py", "tests/test_web_search_agent_local_file_extraction.py", "tests/test_content_extraction_utils.py", "tests/test_totrag_enhanced.py", "tests/test_vietnamese_rl_adapter.py", "tests/test_query_decomposer_integration.py", "tests/test_feedback_loop.py", "tests/test_adaptive_crawler_captcha_integration.py", "tests/test_query_analyzer_cache.py", "tests/__init__.py", "tests/test_cotrag_enhanced.py", "tests/test_rate_limiter.py", "tests/test_query_analyzer.py", "tests/test_file_extraction.py", "tests/search_optimizer_evaluation_results.json", "tests/test_ajax_handling.py", "tests/test_performance_profiler.py", "tests/test_vietnamese_dialect_processor_integration.py", "tests/test_vietnamese_rl_evaluator.py", "tests/test_vietnamese_agent_support.py", "tests/test_integrated_web_search_agent.py", "tests/test_plugins.py", "tests/test_enhanced_search_agent.py", "tests/test_adaptive_crawler_integration.py", "tests/test_adaptive_crawler_file_extraction.py", "tests/README.md", "tests/test_simple.py", "tests/test_semantic_analyzer.py", "tests/test_web_search_agent_local_comprehensive_all.py", "tests/test_fallback_methods.py", "tests/test_web_search_agent_local_improvements.py", "tests/test_vietnamese_domain_compounds.py", "tests/test_cot_evaluator.py", "tests/test_question_complexity_evaluator.py", "tests/test_form_handling.py", "tests/test_vietnamese_support_integration.py", "tests/test_robots_parser.py", "tests/test_llm_integration_vietnamese.py", "tests/test_dynamic_content_handler.py", "tests/test_new_features.py", "tests/test_multi_agent/test_enhanced_bayesian_consensus.py", "tests/test_multi_agent/test_task_decomposer_simple.py", "tests/test_multi_agent/test_consensus.py", "tests/test_multi_agent/test_bayesian_consensus.py", "tests/test_multi_agent/test_task_decomposer.py", "tests/test_multi_agent/test_multi_round_consensus.py", "tests/reasoning/rag/__init__.py", "tests/reasoning/integrated/test_cot_rag_integration.py", "tests/reasoning/integrated/test_tot_rag_integration.py", "tests/reasoning/integrated/__init__.py", "tests/integration/test_search_cache_integration.py", "tests/integration/test_reasoning_rl_integration.py", "tests/integration/test_milvus_vector_store.py", "tests/integration/test_cohere_mistral_integration.py", "tests/integration/__init__.py", "tests/integration/README.md", "tests/integration/rl_tuning/__init__.py", "tests/integration/rl_tuning/sft/test_deepseek_sft_integration.py", "tests/integration/rl_tuning/frameworks/__init__.py", "tests/integration/rl_tuning/frameworks/test_frameworks_integration.py", "tests/integration/reasoning/test_rag_integration.py", "tests/integration/reasoning/test_adaptive_cot_integration.py", "tests/integration/reasoning/test_vector_stores_integration.py", "tests/integration/reasoning/test_enhanced_rag_tot_cot_integration.py", "tests/integration/providers/test_provider_integration.py", "tests/integration/providers/__init__.py", "tests/unit/test_hyde_retriever.py", "tests/unit/__init__.py", "tests/unit/README.md", "tests/unit/rag/document_processing/test_context_processor.py", "tests/unit/rag/document_processing/test_semantic_chunker.py", "tests/unit/rag/search/test_hybrid_search.py", "tests/unit/rag/search/test_keyword_search.py", "tests/unit/rl_tuning/test_rl_edge_cases.py", "tests/unit/rl_tuning/test_agent_environment.py", "tests/unit/rl_tuning/__init__.py", "tests/unit/rl_tuning/test_rl_modules_comprehensive.py", "tests/unit/rl_tuning/sft/test_data_augmentation.py", "tests/unit/rl_tuning/sft/test_evaluation.py", "tests/unit/rl_tuning/sft/test_sft_base.py", "tests/unit/rl_tuning/sft/__init__.py", "tests/unit/rl_tuning/sft/test_deepseek_sft.py", "tests/unit/rl_tuning/sft/test_qwq_sft.py", "tests/unit/rl_tuning/prm/test_base.py", "tests/unit/rl_tuning/prm/test_preference_model.py", "tests/unit/rl_tuning/prm/test_data_utils.py", "tests/unit/rl_tuning/prm/test_reward_model.py", "tests/unit/rl_tuning/prm/test_openai_reward_model.py", "tests/unit/rl_tuning/prm/test_anthropic_reward_model.py", "tests/unit/rl_tuning/prm/test_deepseek_reward_model.py", "tests/unit/rl_tuning/prm/__init__.py", "tests/unit/rl_tuning/prm/README.md", "tests/unit/rl_tuning/action_space_awareness/test_action_space_awareness.py", "tests/unit/rl_tuning/frameworks/test_tinyzero_integration.py", "tests/unit/rl_tuning/frameworks/test_openr1_integration.py", "tests/unit/rl_tuning/frameworks/test_verl_integration.py", "tests/unit/rl_tuning/frameworks/test_trlx_integration.py", "tests/unit/rl_tuning/frameworks/test_framework_factory.py", "tests/unit/rl_tuning/grpo/test_openrouter_grpo.py", "tests/unit/rl_tuning/grpo/test_qwq_grpo.py", "tests/unit/rl_tuning/grpo/test_vietnamese_reward.py", "tests/unit/rl_tuning/grpo/test_gemini_grpo.py", "tests/unit/rl_tuning/grpo/test_reward_functions.py", "tests/unit/rl_tuning/grpo/__init__.py", "tests/unit/rl_tuning/grpo/test_huggingface_grpo.py", "tests/unit/rl_tuning/grpo/test_mistral_grpo.py", "tests/unit/rl_tuning/grpo/test_cohere_grpo.py", "tests/unit/rl_tuning/grpo/test_deepseek_grpo.py", "tests/unit/rl_tuning/adapters/test_verl_adapter.py", "tests/unit/rl_tuning/adapters/__init__.py", "tests/unit/rl_tuning/model_paradigm/test_rl_tuner.py", "tests/unit/rl_tuning/model_paradigm/test_rl_model_paradigm.py", "tests/unit/rl_tuning/model_paradigm/test_performance_optimizer.py", "tests/unit/rl_tuning/model_paradigm/test_complex_case_handler.py", "tests/unit/rl_tuning/model_paradigm/test_vietnamese_support.py", "tests/unit/rl_tuning/model_paradigm/__init__.py", "tests/unit/rl_tuning/benchmarks/test_agent_benchmark_evaluator.py", "tests/unit/rl_tuning/benchmarks/__init__.py", "tests/unit/rl_tuning/dpo/test_gemini_dpo.py", "tests/unit/rl_tuning/dpo/test_preprocessing.py", "tests/unit/rl_tuning/dpo/test_base.py", "tests/unit/rl_tuning/dpo/test_preference_model.py", "tests/unit/rl_tuning/dpo/test_openai_dpo.py", "tests/unit/rl_tuning/dpo/test_dpo_dataset.py", "tests/unit/rl_tuning/dpo/test_openrouter_dpo.py", "tests/unit/rl_tuning/dpo/test_loss_functions.py", "tests/unit/rl_tuning/dpo/test_trainer.py", "tests/unit/rl_tuning/dpo/test_dpo_utils.py", "tests/unit/rl_tuning/dpo/test_dpo_trainer.py", "tests/unit/rl_tuning/dpo/test_loss.py", "tests/unit/rl_tuning/dpo/__init__.py", "tests/unit/rl_tuning/dpo/test_callbacks.py", "tests/unit/rl_tuning/dpo/test_dataset.py", "tests/unit/rl_tuning/dpo/test_anthropic_dpo.py", "tests/unit/rl_tuning/rewards/test_agent_reward_model.py", "tests/unit/rl_tuning/trajectories/test_trajectory_collector.py", "tests/unit/rl_tuning/trajectories/test_trajectory_scaler.py", "tests/unit/rl_tuning/action_space/test_action_space_awareness.py", "tests/unit/rl_tuning/action_space/test_action_space_manager.py", "tests/unit/rl_tuning/ppo/test_anthropic_ppo.py", "tests/unit/rl_tuning/ppo/test_base.py", "tests/unit/rl_tuning/ppo/test_advantage_utils.py", "tests/unit/rl_tuning/ppo/test_openai_ppo.py", "tests/unit/rl_tuning/ppo/test_openrouter_ppo.py", "tests/unit/rl_tuning/ppo/__init__.py", "tests/unit/rl_tuning/ppo/test_qwq_ppo.py", "tests/unit/rl_tuning/ppo/test_deepseek_ppo.py", "tests/unit/reasoning/test_evidence_verifier.py", "tests/unit/reasoning/test_cotrag_advanced.py", "tests/unit/reasoning/test_adaptive_cot.py", "tests/unit/reasoning/test_multi_stage_reasoner.py", "tests/unit/reasoning/test_cotrag_error_analysis.py", "tests/unit/reasoning/test_enhanced_ragtotcot_weight_optimizer.py", "tests/unit/reasoning/test_enhanced_cotrag.py", "tests/unit/reasoning/test_source_attribution_enhanced.py", "tests/unit/reasoning/test_pinecone_rag.py", "tests/unit/reasoning/test_recursive_tot.py", "tests/unit/reasoning/test_real_time_retriever.py", "tests/unit/reasoning/test_cotrag_weight_adjustment.py", "tests/unit/reasoning/test_multi_source_validator.py", "tests/unit/reasoning/test_cotrag_vietnamese_embedding_adapter.py", "tests/unit/reasoning/test_cotrag_irrelevant_docs.py", "tests/unit/reasoning/test_knowledge_graph_integration.py", "tests/unit/reasoning/test_base_rag.py", "tests/unit/reasoning/test_cotrag_advanced_strategies.py", "tests/unit/reasoning/test_rag_error_handler.py", "tests/unit/reasoning/test_knowledge_graph_rag.py", "tests/unit/reasoning/test_weaviate_rag.py", "tests/unit/reasoning/test_react_error_recovery.py", "tests/unit/reasoning/test_query_decomposer.py", "tests/unit/reasoning/test_tot_rag.py", "tests/unit/reasoning/test_faiss_rag.py", "tests/unit/reasoning/test_relevance_scorer.py", "tests/unit/reasoning/test_cot_rag.py", "tests/unit/reasoning/test_source_attribution.py", "tests/unit/reasoning/__init__.py", "tests/unit/reasoning/test_cotrag_enhanced.py", "tests/unit/reasoning/test_cotrag_comprehensive.py", "tests/unit/reasoning/test_cotrag_adaptive_learning.py", "tests/unit/reasoning/test_multi_query_decomposition.py", "tests/unit/reasoning/test_enhanced_rag_tot_cot.py", "tests/unit/reasoning/test_query_understanding.py", "tests/unit/reasoning/test_self_reflection.py", "tests/unit/reasoning/test_milvus_rag.py", "tests/unit/reasoning/test_sqlite_vector_rag.py", "tests/unit/reasoning/formats/test_evaluator.py", "tests/unit/reasoning/formats/test_outcome_based.py", "tests/unit/reasoning/formats/test_react.py", "tests/unit/reasoning/formats/__init__.py", "tests/unit/reasoning/formats/test_react_reasoner.py", "tests/unit/reasoning/query_classification/test_ml_classifier.py", "tests/unit/reasoning/cot/benchmark.py", "tests/unit/reasoning/cot/conftest.py", "tests/unit/reasoning/cot/test_enhanced_cot_error.py", "tests/unit/reasoning/cot/mock_provider.py", "tests/unit/reasoning/cot/test_enhanced_cot.py", "tests/unit/reasoning/cot/test_enhanced_cot_advanced.py", "tests/unit/reasoning/cot/test_enhanced_cot_basic.py", "tests/unit/reasoning/cot/run_tests.py", "tests/unit/reasoning/cot/benchmark_simple.py", "tests/unit/reasoning/cot/test_utils.py", "tests/unit/reasoning/cot/test_enhanced_cot_performance.py", "tests/unit/reasoning/cot/test_enhanced_cot_language.py", "tests/unit/reasoning/cot/test_enhanced_cot_reasoning.py", "tests/unit/reasoning/tot/test_tot_error_analysis.py", "tests/unit/reasoning/tot/test_tot_advanced.py", "tests/unit/reasoning/tot/test_tot.py", "tests/unit/reasoning/tot/test_advanced_evaluator.py", "tests/unit/reasoning/tot/test_tree_of_thought.py", "tests/unit/reasoning/tot/test_tot_parameter_adjustment.py", "tests/unit/reasoning/tot/test_advanced_error_analysis.py", "tests/unit/reasoning/tot/test_tot_fixed.py", "tests/unit/reasoning/tot/test_parameter_optimizer.py", "tests/unit/reasoning/tot/test_tot_simple.py", "tests/unit/reasoning/tot/test_tot_minimal.py", "tests/unit/reasoning/tot/test_tot_advanced_evaluation.py", "tests/unit/reasoning/tot/__init__.py", "tests/unit/reasoning/tot/test_tot_optimized_advanced.py", "tests/unit/reasoning/tot/test_tot_basic.py", "tests/unit/reasoning/tot/test_advanced_tot_error_analysis.py", "tests/unit/reasoning/tot/test_tot_optimized.py", "tests/unit/reasoning/trajectory_scaling/test_base_scaler.py", "tests/unit/reasoning/combined/test_multi_query_tot_rag_comprehensive.py", "tests/unit/reasoning/combined/test_rag_tot_cot.py", "tests/unit/reasoning/combined/__init__.py", "tests/unit/reasoning/combined/test_multi_query_rag.py", "tests/unit/reasoning/combined/test_rag_tot_cot_unittest.py", "tests/unit/reasoning/combined/test_multi_query_tot_rag.py", "tests/unit/reasoning/react/test_react_thompson_sampling.py", "tests/unit/reasoning/react/test_react_rag.py", "tests/unit/reasoning/react/test_adaptive_react_feedback.py", "tests/unit/reasoning/react/test_adaptive_react_feedback_extensions.py", "tests/unit/reasoning/react/test_react_local.py", "tests/unit/reasoning/react/test_react_feedback_types.py", "tests/unit/reasoning/react/test_react_reinforcement_optimization.py", "tests/unit/reasoning/react/test_react_rag_advanced.py", "tests/unit/reasoning/react/test_thompson_sampling.py", "tests/unit/reasoning/react/test_react_feedback_storage.py", "tests/unit/reasoning/react/__init__.py", "tests/unit/reasoning/react/test_react_basic.py", "tests/unit/reasoning/react/test_react_advanced_features.py", "tests/unit/reasoning/react/test_react_advanced.py", "tests/unit/multilingual/test_vietnamese_embeddings.py", "tests/unit/retrieval/test_weaviate_vector_store.py", "tests/unit/retrieval/test_faiss_vector_store.py", "tests/unit/retrieval/test_pinecone_vector_store.py", "tests/unit/retrieval/test_abstract_vector_store.py", "tests/unit/retrieval/document_chunking/test_base_chunker.py", "tests/unit/retrieval/document_chunking/__init__.py", "tests/unit/retrieval/document_chunking/test_smart_chunker.py", "tests/unit/retrieval/vector_store/test_milvus_vector_store.py", "tests/unit/providers/__init__.py", "tests/unit/providers/test_mistral_provider.py", "tests/unit/providers/test_cohere_provider.py", "tests/unit/utils/test_performance_metrics.py", "tests/unit/utils/test_intelligent_error_detection.py", "tests/unit/utils/test_text_processing.py", "tests/unit/utils/test_feedback_persistence.py", "tests/unit/utils/test_advanced_recovery_strategies.py", "tests/unit/utils/test_tool_selection_advanced.py", "tests/unit/utils/test_context_based_tool_selector.py", "tests/unit/utils/test_react_feedback_improvements.py", "tests/unit/utils/test_structured_logging.py", "tests/unit/utils/test_react_feedback_reinforcement.py", "tests/unit/utils/test_error_recovery_strategies.py", "tests/unit/utils/test_error_learning.py", "tests/unit/utils/test_react_feedback.py", "tests/unit/utils/test_cache_memory_manager.py", "tests/unit/utils/test_reinforcement_learning.py", "tests/unit/utils/test_simple.py", "tests/unit/utils/test_alerting.py", "tests/unit/utils/test_distributed_tracing.py", "tests/unit/visualization/test_tot_rag_visualizer.py", "tests/unit/evaluation/test_vietnamese_benchmark.py", "tests/unit/evaluation/test_integrated_evaluator.py", "tests/unit/evaluation/test_enhanced_vietnamese_rl_evaluator.py", "tests/unit/agents/test_web_search_agent_base.py", "tests/unit/agents/test_web_search_agent_cache_detailed.py", "tests/unit/agents/test_web_search_error_recovery.py", "tests/unit/agents/test_web_search_memory_optimizer.py", "tests/unit/agents/test_web_search_cache_enhanced.py", "tests/unit/agents/__init__.py", "tests/unit/agents/test_web_search_agent_cache.py", "tests/unit/agents/test_web_search_cache.py", "tests/unit/agents/test_web_search_vietnamese.py", "tests/unit/models/test_qwq_32b.py", "tests/unit/models/test_deepseek_r1.py", "tests/unit/models/test_gpt_o1.py", "tests/unit/models/test_base_reasoning_model.py", "tests/unit/optimization/__init__.py", "tests/unit/optimization/test_vietnamese_model_quantization.py", "tests/unit/optimization/lora/test_lora_evaluator.py", "tests/unit/optimization/lora/test_base_lora.py", "tests/unit/optimization/lora/__init__.py", "tests/unit/optimization/caching/test_semantic_cache.py", "tests/unit/optimization/caching/test_eviction_policies.py", "tests/unit/optimization/caching/test_adaptive_search_cache_detailed.py", "tests/unit/optimization/caching/test_predictive_cache.py", "tests/unit/optimization/caching/test_cache_warmer.py", "tests/unit/optimization/caching/test_cache_decorator.py", "tests/unit/optimization/caching/test_distributed_cache.py", "tests/unit/optimization/caching/__init__.py", "tests/unit/optimization/caching/test_adaptive_search_cache.py", "tests/unit/optimization/mixed_precision/__init__.py", "tests/unit/optimization/mixed_precision/test_mixed_precision.py", "tests/unit/optimization/quantization/test_mixed_precision_quantization.py", "tests/unit/multi_agent/test_memory_compression.py", "tests/unit/multi_agent/test_task_decomposer.py", "tests/unit/multi_agent/test_memory_versioning.py", "tests/performance/test_cache_performance.py", "tests/performance/test_provider_performance.py", "tests/performance/test_performance.py", "tests/evaluation/test_vietnamese_quality_metrics.py", "tests/test_optimization/test_parallel_inference.py", "data/feedback.db", "data/README.md", "config/prometheus.yml", "config/performance_optimizer.json", "config/grafana/provisioning/dashboards/dashboards.yml", "config/grafana/provisioning/datasources/datasource.yml", "test_results/summary.json", "test_results/book_query_result.json", "test_results/vietnamese_query_result.json", "test_results/basic_search_result.json", "test_results/simple_query_result.json", "test_results/extract_content_result.json", "test_results/location_query_result.json", "test_results/module_integration_manager_result.json", "test_results/recent_info_query_result.json", "test_results/recent_info_query_playwright_result.json", "test_results/medium_query_result.json", "test_results/academic_query_result.json", "test_results/simple_query_crawler_result.json", "test_results/complex_query_result.json"], "total_files": 2114, "python_files": 1536, "test_files": 585}, "duplicates": [{"filename": "__init__.py", "path1": "integrations/llm/__init__.py", "path2": "tests/unit/optimization/mixed_precision/__init__.py"}, {"filename": "base_evaluator.py", "path1": "credibility/evaluators/base_evaluator.py", "path2": "src/deep_research_core/evaluation/base_evaluator.py"}, {"filename": "app.py", "path1": "ui/app.py", "path2": "src/deep_research_core/api/app.py"}, {"filename": "base.html", "path1": "ui/templates/base.html", "path2": "src/deep_research_core/web/templates/base.html"}, {"filename": "index.html", "path1": "ui/templates/index.html", "path2": "src/deep_research_core/web/templates/ppo/index.html"}, {"filename": "vietnamese_search_integration.py", "path1": "utils/vietnamese_search_integration.py", "path2": "src/deep_research_core/agents/vietnamese_search_integration.py"}, {"filename": "network_utils.py", "path1": "utils/network_utils.py", "path2": "src/deep_research_core/agents/network_utils.py"}, {"filename": "vietnamese_search_methods.py", "path1": "utils/vietnamese_search_methods.py", "path2": "src/deep_research_core/agents/vietnamese_search_methods.py"}, {"filename": "logging_utils.py", "path1": "utils/logging_utils.py", "path2": "src/deep_research_core/utils/logging_utils.py"}, {"filename": "structured_logging.py", "path1": "utils/structured_logging.py", "path2": "src/deep_research_core/utils/structured_logging.py"}, {"filename": "question_complexity_evaluator.py", "path1": "utils/question_complexity_evaluator.py", "path2": "src/deep_research_core/agents/question_complexity_evaluator.py"}, {"filename": "vietnamese_utils.py", "path1": "utils/vietnamese_utils.py", "path2": "src/deep_research_core/utils/vietnamese_utils.py"}, {"filename": "content_extraction_utils.py", "path1": "utils/content_extraction_utils.py", "path2": "src/deep_research_core/utils/content_extraction_utils.py"}, {"filename": "multilingual_utils.py", "path1": "utils/multilingual_utils.py", "path2": "src/deep_research_core/utils/multilingual_utils.py"}, {"filename": "answer_quality_evaluator.py", "path1": "utils/answer_quality_evaluator.py", "path2": "src/deep_research_core/agents/answer_quality_evaluator.py"}, {"filename": "site_structure_handler.py", "path1": "utils/shared/site_structure_handler.py", "path2": "src/deep_research_core/crawlers/site_structure_handler.py"}, {"filename": "captcha_handler.py", "path1": "utils/shared/captcha_handler.py", "path2": "src/deep_research_core/utils/captcha_handler.py"}, {"filename": "README.md", "path1": "utils/shared/README.md", "path2": "data/README.md"}, {"filename": "pagination_handler.py", "path1": "utils/shared/pagination_handler.py", "path2": "src/deep_research_core/agents/pagination_handler.py"}, {"filename": "web_search_agent.py", "path1": "agents/web_search_agent.py", "path2": "src/deep_research_core/agents/web_search_agent.py"}, {"filename": "adaptive_crawler_integration.py", "path1": "agents/adaptive_crawler_integration.py", "path2": "src/deep_research_core/agents/adaptive_crawler_integration.py"}, {"filename": "extract_content_improved.py", "path1": "agents/extract_content_improved.py", "path2": "src/deep_research_core/agents/extract_content_improved.py"}, {"filename": "adaptive_crawler.py", "path1": "agents/adaptive_crawler.py", "path2": "src/deep_research_core/agents/adaptive_crawler.py"}, {"filename": "multilingual_crawler.py", "path1": "agents/multilingual_crawler.py", "path2": "src/deep_research_core/agents/multilingual_crawler.py"}, {"filename": "test_vietnamese_search.py", "path1": "tests/test_vietnamese_search.py", "path2": "src/deep_research_core/agents/test_vietnamese_search.py"}, {"filename": "vietnamese_stopwords.txt", "path1": "data/dictionaries/vietnamese_stopwords.txt", "path2": "src/deep_research_core/nlp/data/vietnamese_stopwords.txt"}, {"filename": "base.py", "path1": "integrations/llm/base.py", "path2": "src/deep_research_core/optimization/adapter/base.py"}]}, "test_analysis": {"test_dirs": ["./.pytest_cache", "./test_downloads", "./temp_test_dir", "./tests", "./test_results", "./deepresearch/.pytest_cache", "./deepresearch/test", "./deepresearch/tests", "./deepresearch/test_results", "./deepresearch/docs/testing", "./deepresearch/frontend/src/components/documents/__tests__", "./deepresearch/frontend/src/components/visualization/__tests__", "./deepresearch/frontend/src/components/debugging/__tests__", "./deepresearch/frontend/src/components/layout/__tests__", "./deepresearch/frontend/src/components/common/__tests__", "./deepresearch/frontend/src/theme/__tests__", "./deepresearch/frontend/src/utils/__tests__", "./deepresearch/frontend/node_modules/safe-regex-test", "./deepresearch/frontend/node_modules/test-exclude", "./deepresearch/frontend/node_modules/eslint-plugin-testing-library", "./deepresearch/frontend/node_modules/@testing-library", "./deepresearch/frontend/node_modules/function.prototype.name/test", "./deepresearch/frontend/node_modules/object.groupby/test", "./deepresearch/frontend/node_modules/concat-map/test", "./deepresearch/frontend/node_modules/utila/test", "./deepresearch/frontend/node_modules/select-hose/test", "./deepresearch/frontend/node_modules/unbox-primitive/test", "./deepresearch/frontend/node_modules/http-deceiver/test", "./deepresearch/frontend/node_modules/gopd/test", "./deepresearch/frontend/node_modules/jest-watch-typeahead/node_modules/@jest/test-result", "./deepresearch/frontend/node_modules/has-tostringtag/test", "./deepresearch/frontend/node_modules/@bcoe/v8-coverage/src/test", "./deepresearch/frontend/node_modules/array.prototype.flat/test", "./deepresearch/frontend/node_modules/css-select-base-adapter/test", "./deepresearch/frontend/node_modules/fast-json-stable-stringify/test", "./deepresearch/frontend/node_modules/is-date-object/test", "./deepresearch/frontend/node_modules/is-async-function/test", "./deepresearch/frontend/node_modules/hpack.js/test", "./deepresearch/frontend/node_modules/object-keys/test", "./deepresearch/frontend/node_modules/duplexer/test", "./deepresearch/frontend/node_modules/globalthis/test", "./deepresearch/frontend/node_modules/es-get-iterator/test", "./deepresearch/frontend/node_modules/lz-string/tests", "./deepresearch/frontend/node_modules/es-to-primitive/test", "./deepresearch/frontend/node_modules/object.fromentries/test", "./deepresearch/frontend/node_modules/qs/test", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__", "./deepresearch/frontend/node_modules/is-callable/test", "./deepresearch/frontend/node_modules/es-set-tostringtag/test", "./deepresearch/frontend/node_modules/string.prototype.repeat/tests", "./deepresearch/frontend/node_modules/which-collection/test", "./deepresearch/frontend/node_modules/data-view-byte-offset/test", "./deepresearch/frontend/node_modules/data-view-buffer/test", "./deepresearch/frontend/node_modules/possible-typed-array-names/test", "./deepresearch/frontend/node_modules/own-keys/test", "./deepresearch/frontend/node_modules/es-array-method-boxes-properly/test", "./deepresearch/frontend/node_modules/side-channel-map/test", "./deepresearch/frontend/node_modules/is-symbol/test", "./deepresearch/frontend/node_modules/isexe/test", "./deepresearch/frontend/node_modules/side-channel-weakmap/test", "./deepresearch/frontend/node_modules/text-table/test", "./deepresearch/frontend/node_modules/is-weakref/test", "./deepresearch/frontend/node_modules/svgo/node_modules/domutils/test", "./deepresearch/frontend/node_modules/svgo/node_modules/domutils/test/tests", "./deepresearch/frontend/node_modules/function-bind/test", "./deepresearch/frontend/node_modules/events/tests", "./deepresearch/frontend/node_modules/string.prototype.matchall/test", "./deepresearch/frontend/node_modules/is-finalizationregistry/test", "./deepresearch/frontend/node_modules/sprintf-js/test", "./deepresearch/frontend/node_modules/prettier-linter-helpers/test", "./deepresearch/frontend/node_modules/get-intrinsic/test", "./deepresearch/frontend/node_modules/es-shim-unscopables/test", "./deepresearch/frontend/node_modules/is-map/test", "./deepresearch/frontend/node_modules/is-string/test", "./deepresearch/frontend/node_modules/for-each/test", "./deepresearch/frontend/node_modules/safe-regex-test/test", "./deepresearch/frontend/node_modules/is-weakmap/test", "./deepresearch/frontend/node_modules/typed-array-byte-offset/test", "./deepresearch/frontend/node_modules/@webassemblyjs/utf8/test", "./deepresearch/frontend/node_modules/typedarray-to-buffer/test", "./deepresearch/frontend/node_modules/resolve/test", "./deepresearch/frontend/node_modules/html-escaper/test", "./deepresearch/frontend/node_modules/dom-helpers/hyphenateStyle", "./deepresearch/frontend/node_modules/object.values/test", "./deepresearch/frontend/node_modules/side-channel/test", "./deepresearch/frontend/node_modules/string.prototype.includes/tests", "./deepresearch/frontend/node_modules/jake/test", "./deepresearch/frontend/node_modules/performance-now/test", "./deepresearch/frontend/node_modules/string.prototype.trimend/test", "./deepresearch/frontend/node_modules/postcss-calc/src/__tests__", "./deepresearch/frontend/node_modules/typed-array-byte-length/test", "./deepresearch/frontend/node_modules/shell-quote/test", "./deepresearch/frontend/node_modules/is-shared-array-buffer/test", "./deepresearch/frontend/node_modules/stop-iteration-iterator/test", "./deepresearch/frontend/node_modules/is-data-view/test", "./deepresearch/frontend/node_modules/rw/test", "./deepresearch/frontend/node_modules/has-proto/test", "./deepresearch/frontend/node_modules/object.getownpropertydescriptors/test", "./deepresearch/frontend/node_modules/side-channel-list/test", "./deepresearch/frontend/node_modules/set-proto/test", "./deepresearch/frontend/node_modules/commondir/test", "./deepresearch/frontend/node_modules/iterator.prototype/test", "./deepresearch/frontend/node_modules/array.prototype.reduce/test", "./deepresearch/frontend/node_modules/bfj/test", "./deepresearch/frontend/node_modules/internal-slot/test", "./deepresearch/frontend/node_modules/is-generator-function/test", "./deepresearch/frontend/node_modules/setprototypeof/test", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests", "./deepresearch/frontend/node_modules/is-typed-array/test", "./deepresearch/frontend/node_modules/es-object-atoms/test", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/__tests__", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/test-redirections", "./deepresearch/frontend/node_modules/jsonpath/test", "./deepresearch/frontend/node_modules/jsonpath/node_modules/esprima/test", "./deepresearch/frontend/node_modules/arraybuffer.prototype.slice/test", "./deepresearch/frontend/node_modules/string.prototype.trim/test", "./deepresearch/frontend/node_modules/find-root/test", "./deepresearch/frontend/node_modules/array-buffer-byte-length/test", "./deepresearch/frontend/node_modules/array.prototype.tosorted/test", "./deepresearch/frontend/node_modules/selfsigned/test", "./deepresearch/frontend/node_modules/json-buffer/test", "./deepresearch/frontend/node_modules/gensync/test", "./deepresearch/frontend/node_modules/is-weakset/test", "./deepresearch/frontend/node_modules/object.assign/test", "./deepresearch/frontend/node_modules/wbuf/test", "./deepresearch/frontend/node_modules/data-view-byte-length/test", "./deepresearch/frontend/node_modules/is-array-buffer/test", "./deepresearch/frontend/node_modules/fast-uri/test", "./deepresearch/frontend/node_modules/is-number-object/test", "./deepresearch/frontend/node_modules/is-core-module/test", "./deepresearch/frontend/node_modules/dunder-proto/test", "./deepresearch/frontend/node_modules/typed-array-length/test", "./deepresearch/frontend/node_modules/safe-push-apply/test", "./deepresearch/frontend/node_modules/@types/react-dom/test-utils", "./deepresearch/frontend/node_modules/has-property-descriptors/test", "./deepresearch/frontend/node_modules/call-bound/test", "./deepresearch/frontend/node_modules/which-builtin-type/test", "./deepresearch/frontend/node_modules/tryer/test", "./deepresearch/frontend/node_modules/damerau-levenshtein/test", "./deepresearch/frontend/node_modules/object-inspect/test", "./deepresearch/frontend/node_modules/static-eval/test", "./deepresearch/frontend/node_modules/which-typed-array/test", "./deepresearch/frontend/node_modules/math-intrinsics/test", "./deepresearch/frontend/node_modules/tsconfig-paths/src/__tests__", "./deepresearch/frontend/node_modules/fastq/test", "./deepresearch/frontend/node_modules/exit/test", "./deepresearch/frontend/node_modules/get-proto/test", "./deepresearch/frontend/node_modules/obuf/test", "./deepresearch/frontend/node_modules/eslint-plugin-testing-library/create-testing-library-rule", "./deepresearch/frontend/node_modules/handle-thing/test", "./deepresearch/frontend/node_modules/call-bind/test", "./deepresearch/frontend/node_modules/has-symbols/test", "./deepresearch/frontend/node_modules/@jest/test-sequencer", "./deepresearch/frontend/node_modules/@jest/test-result", "./deepresearch/frontend/node_modules/is-set/test", "./deepresearch/frontend/node_modules/has-bigints/test", "./deepresearch/frontend/node_modules/object-is/test", "./deepresearch/frontend/node_modules/typed-array-buffer/test", "./deepresearch/frontend/node_modules/pretty-error/test", "./deepresearch/frontend/node_modules/deep-is/test", "./deepresearch/frontend/node_modules/array-includes/test", "./deepresearch/frontend/node_modules/eslint-plugin-react/node_modules/resolve/test", "./deepresearch/frontend/node_modules/object.entries/test", "./deepresearch/frontend/node_modules/array.prototype.flatmap/test", "./deepresearch/frontend/node_modules/array.prototype.findlast/test", "./deepresearch/frontend/node_modules/minimist/test", "./deepresearch/frontend/node_modules/es-errors/test", "./deepresearch/frontend/node_modules/async-function/test", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/__tests__", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/__tests__/vitest", "./deepresearch/frontend/node_modules/functions-have-names/test", "./deepresearch/frontend/node_modules/string.prototype.trimstart/test", "./deepresearch/frontend/node_modules/tsutils/node_modules/tslib/test", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__", "./deepresearch/frontend/node_modules/reflect.getprototypeof/test", "./deepresearch/frontend/node_modules/call-bind-apply-helpers/test", "./deepresearch/frontend/node_modules/available-typed-arrays/test", "./deepresearch/frontend/node_modules/get-symbol-description/test", "./deepresearch/frontend/node_modules/es-define-property/test", "./deepresearch/frontend/node_modules/array.prototype.findlastindex/test", "./deepresearch/frontend/node_modules/which-boxed-primitive/test", "./deepresearch/frontend/node_modules/deep-equal/test", "./deepresearch/frontend/node_modules/supports-preserve-symlinks-flag/test", "./deepresearch/frontend/node_modules/is-regex/test", "./deepresearch/frontend/node_modules/es-iterator-helpers/test", "./deepresearch/frontend/node_modules/is-boolean-object/test", "./deepresearch/frontend/node_modules/safe-array-concat/test", "./deepresearch/frontend/node_modules/json-stable-stringify-without-jsonify/test", "./deepresearch/frontend/node_modules/is-arguments/test", "./deepresearch/frontend/node_modules/regexp.prototype.flags/test", "./deepresearch/frontend/node_modules/define-data-property/test", "./deepresearch/frontend/node_modules/spdy/test", "./deepresearch/frontend/node_modules/is-bigint/test", "./deepresearch/src/tests", "./deepresearch/src/deep_research_core/agents/tests", "./deepresearch/tests/test_multi_agent", "./deepresearch/tests/test_optimization", "./src/deep_research_core/tests", "./tests/temp_test"], "test_files": ["./test_extract_videos.py", "./test_comprehensive_web_search_agent.py", "./test_websearch_real_functionality.py", "./simple_test.py", "./test_extract_audio.py", "./test_consolidated_simple.py", "./test_extract_images.py", "./test_file_processor.py", "./test_performance_benchmark.py", "./simple_file_processor_test.py", "./test_vietnamese_utils.py", "./test_final_comprehensive_integration.py", "./test_adaptive_crawler_consolidated.py", "./test_extract_files.py", "./test_deep_crawl.py", "./comprehensive_test_web_search_agent.py", "./test_step10_integration_manager.py", "./detailed_test.py", "./quick_search_test.py", "./test_results.json", "./test_simple_import.py", "./test_step8_site_structure.py", "./test_web_search_agent_local_features.py", "./test_searxng_local_only.py", "./minimal_test.py", "./test_final_status_check.py", "./test_real_search.py", "./test_web_search_agent_merged_simple.py", "./test_consolidation_completion.py", "./test_user_agent_manager.py", "./test_searxng_docker.py", "./test_step1_integration.py", "./test_consolidated_agent.py", "./test_results.md", "./test_simple_merge.py", "./test_searxng_api.py", "./test_modules.py", "./simple_search_test.py", "./TASK_4_TESTING_DOCUMENTATION.md", "./test_adaptive_crawler_consolidated_merged.py", "./test_adaptive_crawler_integration.py", "./test_web_search_agent_structure.py", "./test_web_search_agent_local_merged.py", "./test_import_adaptive_crawler.py", "./test_site_structure_handler.py", "./test_web_search_agent_merged.py", "./direct_test.py", "./test_utils_modules.py", "./test_searxng_priority.py", "./test_simple_functionality.py", "./test_minimal_agent.py", "./test_step9_language_handler.py", "./test_new_features.py", "./deepresearch/run_all_tests.py", "./deepresearch/test_model_loader.py", "./deepresearch/test_check_ddg_params.py", "./deepresearch/test_reasoning_basic.py", "./deepresearch/test_reasoning_edge_cases.py", "./deepresearch/test_web_search_agent_local_performance.py", "./deepresearch/simple_test.py", "./deepresearch/test_direct_crawlee.py", "./deepresearch/test_crossref_openalex_fallback.py", "./deepresearch/test_validator.py", "./deepresearch/test_web_search_agent_local_advanced.py", "./deepresearch/test_deep_crawler_enhanced.py", "./deepresearch/run_test_with_output.py", "./deepresearch/pytest.ini", "./deepresearch/test_local_agent.py", "./deepresearch/test_web_imports.py", "./deepresearch/test_file_processor.py", "./deepresearch/comprehensive_test_web_search_agent_local.py", "./deepresearch/test_crawl_standalone.py", "./deepresearch/test_torch_cuda.py", "./deepresearch/test_simple_module.py", "./deepresearch/test_tot_empty_query.py", "./deepresearch/test_torch_transformers.py", "./deepresearch/web_search_improved_test.json", "./deepresearch/test_imports.py", "./deepresearch/test_searxng_crawlee.py", "./deepresearch/test_web_search_agent_local_large_scale.py", "./deepresearch/test_crawlee.py", "./deepresearch/test_search_optimizer.py", "./deepresearch/test_error_recovery.py", "./deepresearch/test_deep_crawl.py", "./deepresearch/test_crawlee_playwright_search.py", "./deepresearch/test_searxng_connection.py", "./deepresearch/test_openalex_crossref_fallback.py", "./deepresearch/README_TESTING.md", "./deepresearch/test_query_optimizer_integration.py", "./deepresearch/test_standalone.py", "./deepresearch/test_all_modules.py", "./deepresearch/test_100_requests.py", "./deepresearch/test_book_search_direct.py", "./deepresearch/test_reasoning_modules.py", "./deepresearch/test_searxng_crawlee_simple.js", "./deepresearch/test_file_download_direct.py", "./deepresearch/test_websearch_evaluation.py", "./deepresearch/test_improved_web_search_agent.py", "./deepresearch/test_advanced_crawlee.py", "./deepresearch/test_search_local_limit.py", "./deepresearch/test_merged_agent.py", "./deepresearch/test_enhanced_agent.py", "./deepresearch/test_performance_nlp.py", "./deepresearch/test_web_search.py", "./deepresearch/simple_query_optimizer_test.py", "./deepresearch/test_crawlee_playwright_output.py", "./deepresearch/test_query_decomposer.py", "./deepresearch/move_tests.py", "./deepresearch/test_reasoning_only.py", "./deepresearch/test_restructuring_summary.md", "./deepresearch/test_tot_simple.py", "./deepresearch/test_security_enhancements.py", "./deepresearch/test_list.txt", "./deepresearch/test_deep_crawl_basic.py", "./deepresearch/test_reasoning_components.py", "./deepresearch/run_rl_tuning_tests.py", "./deepresearch/test_web_search_agent_local_comprehensive.py", "./deepresearch/test_outcome_based.py", "./deepresearch/test_deep_crawl_final.py", "./deepresearch/hello_test.py", "./deepresearch/test_specialized_search.py", "./deepresearch/test_search_simple.py", "./deepresearch/test_simple_deep_crawler.py", "./deepresearch/test_web_search_agent_local.py", "./deepresearch/test_academic_search.py", "./deepresearch/test_search_flow.py", "./deepresearch/test_websearch_fixed.py", "./deepresearch/test_model_loader_simple.py", "./deepresearch/test_captcha_solver.py", "./deepresearch/simple_test_improved.py", "./deepresearch/test_crawl_methods.py", "./deepresearch/run_tests.py", "./deepresearch/web_search_test_results.json", "./deepresearch/test_verl_framework.py", "./deepresearch/test_web_search_agent_local_custom.py", "./deepresearch/test_restructuring.md", "./deepresearch/test_deep_crawler_with_file_download.py", "./deepresearch/simple_test_fixed.py", "./deepresearch/run_all_web_search_tests.py", "./deepresearch/search_test_results.json", "./deepresearch/test_source_attribution.py", "./deepresearch/test_check_ddgs.py", "./deepresearch/test_module.py", "./deepresearch/test_query_analyzer.py", "./deepresearch/test_file_extraction.py", "./deepresearch/test_extended_document_extractor.py", "./deepresearch/REWARDS_TEST_STATUS.md", "./deepresearch/test_decide_method.py", "./deepresearch/test_prm_reward_model.py", "./deepresearch/test_modules_simple.py", "./deepresearch/test_output.txt", "./deepresearch/test_searxng_api.py", "./deepresearch/test_modules.py", "./deepresearch/test_multilingual_search.py", "./deepresearch/web_search_enhanced_test.json", "./deepresearch/test_optimizer.py", "./deepresearch/simple_search_test.py", "./deepresearch/test_tot_basic.py", "./deepresearch/test_deep_crawl_minimal.py", "./deepresearch/test_file_download.py", "./deepresearch/test_rl_modules.py", "./deepresearch/test_adaptive_crawler_integration.py", "./deepresearch/test_all_integrations.py", "./deepresearch/test_standalone_improved.py", "./deepresearch/test_tinyzero_framework.py", "./deepresearch/search_test_results.log", "./deepresearch/test_query_optimizer.py", "./deepresearch/test_simple.py", "./deepresearch/test_web_search_agent_local_advanced_detailed.py", "./deepresearch/test_web_search_agent_local_error_handling.py", "./deepresearch/test_searxng_agent.py", "./deepresearch/test_cache_mechanism.py", "./deepresearch/run_test_web_search.py", "./deepresearch/test_specific_query.py", "./deepresearch/test_search_methods.py", "./deepresearch/test_web_search_agent_local_improvements.py", "./deepresearch/test_web_search_agent_mock.py", "./deepresearch/test_deep_crawl_improved.py", "./deepresearch/test_openalex_crossref.py", "./deepresearch/test_query_optimizer_standalone.py", "./deepresearch/test_improved_web_search_agent_fix.py", "./deepresearch/web_search_comprehensive_test.json", "./deepresearch/test_decide_method_advanced.py", "./deepresearch/test_reasoning_comprehensive.py", "./deepresearch/test_deep_crawl_improved_simple.py", "./deepresearch/RL_TUNING_TEST_GUIDE.md", "./deepresearch/test_search.py", "./deepresearch/test_query_analyzer_direct.py", "./deepresearch/__pycache__/test_error_recovery.cpython-310.pyc", "./deepresearch/__pycache__/test_web_search.cpython-310.pyc", "./deepresearch/__pycache__/test_local_agent.cpython-310.pyc", "./deepresearch/__pycache__/test_standalone.cpython-310.pyc", "./deepresearch/__pycache__/test_adaptive_crawler_integration.cpython-310.pyc", "./deepresearch/__pycache__/test_standalone_improved.cpython-310.pyc", "./deepresearch/__pycache__/test_web_search_agent_local.cpython-310.pyc", "./deepresearch/__pycache__/test_file_download_direct.cpython-310.pyc", "./deepresearch/docs/regression_testing_guide.md", "./deepresearch/docs/testing_guide.md", "./deepresearch/docs/test_performance_optimization.md", "./deepresearch/docs/testing/RL_TUNING_TESTS.md", "./deepresearch/examples/test_openrouter_cot.py", "./deepresearch/examples/test_web_search_flow.py", "./deepresearch/examples/test_websearch_auto.py", "./deepresearch/examples/test_adaptive_cot.py", "./deepresearch/examples/test_moonlight.py", "./deepresearch/examples/test_sqlite_vector_rag_hierarchical.py", "./deepresearch/examples/test_openrouter.py", "./deepresearch/examples/test_sqlite_vector_rag_adaptive.py", "./deepresearch/examples/test_sqlite_vector_rag_openrouter_simple.py", "./deepresearch/examples/test_system_with_openrouter.py", "./deepresearch/examples/test_vietnamese_search.py", "./deepresearch/examples/test_sqlite_vector_rag_simple.py", "./deepresearch/examples/test_cotrag.py", "./deepresearch/examples/test_milvus_simple.py", "./deepresearch/examples/test_sqlite_vector_rag_ann.py", "./deepresearch/examples/test_milvus_rag_fixed.py", "./deepresearch/examples/test_cohere_mistral_providers.py", "./deepresearch/examples/test_sqlite_vector_rag_semantic.py", "./deepresearch/examples/test_enhanced_cot.py", "./deepresearch/examples/test_milvus_client_rag.py", "./deepresearch/examples/test_sqlite_rag.py", "./deepresearch/examples/test_milvus_embedded_rag.py", "./deepresearch/examples/test_vietnamese_embeddings.py", "./deepresearch/examples/test_sqlite_vector_rag_openrouter.py", "./deepresearch/examples/test_websearch_crawlee.py", "./deepresearch/examples/test_sqlite_vector_rag_rtree.py", "./deepresearch/examples/test_cot_rag_integration.py", "./deepresearch/examples/test_sqlite_vector_rag_auto_update.py", "./deepresearch/examples/test_sqlite_vector_rag_improved.py", "./deepresearch/examples/test_vietnamese_support.py", "./deepresearch/examples/test_sqlite_vector_rag_enhanced.py", "./deepresearch/examples/test_cot_rag.py", "./deepresearch/examples/test_searxng_local.py", "./deepresearch/examples/test_milvus_lite_rag.py", "./deepresearch/examples/test_sqlite_vector_rag_new.py", "./deepresearch/examples/test_domain_evaluation.py", "./deepresearch/examples/test_cot_rag_features.py", "./deepresearch/examples/test_cotrag_simple.py", "./deepresearch/examples/test_sqlite_vector_rag_features.py", "./deepresearch/examples/test_sqlite_vector_rag_evaluation.py", "./deepresearch/examples/test_milvus_updated.py", "./deepresearch/examples/test_cotrag_comparison.py", "./deepresearch/examples/test_milvus_rag.py", "./deepresearch/examples/test_search_methods.py", "./deepresearch/examples/test_smooth_cot.py", "./deepresearch/examples/test_simple_rag.py", "./deepresearch/examples/test_openrouter_minimal.py", "./deepresearch/examples/test_websearch.py", "./deepresearch/examples/test_sqlite_vector_rag.py", "./deepresearch/examples/__pycache__/test_domain_evaluation.cpython-310-pytest-8.3.4.pyc", "./deepresearch/examples/__pycache__/test_enhanced_cot.cpython-310-pytest-8.3.4.pyc", "./deepresearch/examples/__pycache__/test_cot_rag.cpython-310-pytest-8.3.4.pyc", "./deepresearch/examples/__pycache__/test_cotrag_comparison.cpython-310-pytest-8.3.4.pyc", "./deepresearch/examples/__pycache__/test_cot_rag_integration.cpython-310-pytest-8.3.4.pyc", "./deepresearch/examples/__pycache__/test_cot_rag_features.cpython-310-pytest-8.3.4.pyc", "./deepresearch/examples/__pycache__/test_cotrag.cpython-310-pytest-8.3.4.pyc", "./deepresearch/examples/__pycache__/test_adaptive_cot.cpython-310-pytest-8.3.4.pyc", "./deepresearch/examples/__pycache__/test_cotrag_simple.cpython-310-pytest-8.3.4.pyc", "./deepresearch/examples/__pycache__/test_cohere_mistral_providers.cpython-310-pytest-8.3.4.pyc", "./deepresearch/frontend/src/setupTests.ts", "./deepresearch/frontend/src/pages/ResponsiveTestPage.tsx", "./deepresearch/frontend/src/components/documents/__tests__/DocumentUploader.test.tsx", "./deepresearch/frontend/src/components/documents/__tests__/CollectionManager.test.tsx", "./deepresearch/frontend/src/components/visualization/__tests__/TreeVisualizer.test.tsx", "./deepresearch/frontend/src/components/visualization/__tests__/CoTVisualizer.test.tsx", "./deepresearch/frontend/src/components/debugging/__tests__/DebugConsole.test.tsx", "./deepresearch/frontend/src/components/layout/__tests__/Sidebar.test.tsx", "./deepresearch/frontend/src/components/layout/__tests__/Footer.test.tsx", "./deepresearch/frontend/src/components/layout/__tests__/MainLayout.test.tsx", "./deepresearch/frontend/src/components/layout/__tests__/AppHeader.test.tsx", "./deepresearch/frontend/src/components/common/__tests__/ThemeToggle.test.tsx", "./deepresearch/frontend/src/components/common/__tests__/SearchInput.test.tsx", "./deepresearch/frontend/src/components/common/__tests__/ContentCard.test.tsx", "./deepresearch/frontend/src/components/common/__tests__/LoadingIndicator.test.tsx", "./deepresearch/frontend/src/components/common/__tests__/ResponsiveCard.test.tsx", "./deepresearch/frontend/src/components/common/__tests__/CodeBlock.test.tsx", "./deepresearch/frontend/src/components/common/__tests__/FormInput.test.tsx", "./deepresearch/frontend/src/theme/__tests__/ThemeContext.test.tsx", "./deepresearch/frontend/src/utils/__tests__/ResponsiveUtils.test.tsx", "./deepresearch/frontend/node_modules/function.prototype.name/test/tests.js", "./deepresearch/frontend/node_modules/object.groupby/test/tests.js", "./deepresearch/frontend/node_modules/requires-port/test.js", "./deepresearch/frontend/node_modules/select-hose/test/api-test.js", "./deepresearch/frontend/node_modules/http-deceiver/test/api-test.js", "./deepresearch/frontend/node_modules/react-i18next/vitest.workspace.typescript.mts", "./deepresearch/frontend/node_modules/jest-watch-typeahead/testname.js", "./deepresearch/frontend/node_modules/has-tostringtag/test/tests.js", "./deepresearch/frontend/node_modules/array.prototype.flat/test/tests.js", "./deepresearch/frontend/node_modules/fast-json-stable-stringify/benchmark/test.json", "./deepresearch/frontend/node_modules/d3-array/src/greatest.js", "./deepresearch/frontend/node_modules/d3-array/src/greatestIndex.js", "./deepresearch/frontend/node_modules/hpack.js/test/decompressor-test.js", "./deepresearch/frontend/node_modules/hpack.js/test/encoder-test.js", "./deepresearch/frontend/node_modules/hpack.js/test/compressor-test.js", "./deepresearch/frontend/node_modules/hpack.js/test/decoder-test.js", "./deepresearch/frontend/node_modules/hpack.js/node_modules/isarray/test.js", "./deepresearch/frontend/node_modules/randombytes/test.js", "./deepresearch/frontend/node_modules/ajv/scripts/prepare-tests", "./deepresearch/frontend/node_modules/globalthis/test/tests.js", "./deepresearch/frontend/node_modules/object.fromentries/test/tests.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/index-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isNonLiteralProperty-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/getSuggestion-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isContentEditable-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/hasAccessibleChild-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/getTabIndex-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isNonInteractiveElement-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isAbstractRole-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isDisabledElement-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/getAccessibleChildText-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/getElementType-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/mayHaveAccessibleLabel-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/schemas-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/getComputedRole-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/getExplicitRole-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/mayContainChildComponent-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isNonInteractiveRole-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/getImplicitRole-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isInteractiveRole-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isSemanticRoleElement-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isFocusable-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isDOMElement-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/isInteractiveElement-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/attributesComparator-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/parserOptionsMapper-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/implicitRoles/input-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/implicitRoles/menuitem-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/util/implicitRoles/menu-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-redundant-roles-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/alt-text-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-distracting-elements-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/media-has-caption-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/role-supports-aria-props-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/anchor-ambiguous-text-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/aria-activedescendant-has-tabindex-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/aria-unsupported-elements-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/scope-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/anchor-is-valid-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-noninteractive-tabindex-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/autocomplete-valid-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-access-key-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/prefer-tag-over-role-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/aria-role-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/anchor-has-content-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/click-events-have-key-events-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/aria-proptypes-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/aria-props-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-onchange-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-aria-hidden-on-focusable-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/label-has-associated-control-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/label-has-for-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/iframe-has-title-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/tabindex-no-positive-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/role-has-required-aria-props-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-static-element-interactions-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/mouse-events-have-key-events-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/heading-has-content-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-interactive-element-to-noninteractive-role-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/html-has-lang-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/img-redundant-alt-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-autofocus-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/lang-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-noninteractive-element-interactions-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/interactive-supports-focus-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/accessible-emoji-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/no-noninteractive-element-to-interactive-role-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-jsx-a11y/__tests__/src/rules/control-has-associated-label-test.js", "./deepresearch/frontend/node_modules/async/internal/createTester.js", "./deepresearch/frontend/node_modules/safer-buffer/tests.js", "./deepresearch/frontend/node_modules/string.prototype.repeat/tests/tests.js", "./deepresearch/frontend/node_modules/hoopy/test.js", "./deepresearch/frontend/node_modules/core-js-pure/modules/es.regexp.test.js", "./deepresearch/frontend/node_modules/core-js-pure/es/regexp/test.js", "./deepresearch/frontend/node_modules/core-js-pure/full/regexp/test.js", "./deepresearch/frontend/node_modules/core-js-pure/stable/regexp/test.js", "./deepresearch/frontend/node_modules/core-js-pure/actual/regexp/test.js", "./deepresearch/frontend/node_modules/core-js-pure/features/regexp/test.js", "./deepresearch/frontend/node_modules/svgo/plugins/addAttributesToSVGElement.js", "./deepresearch/frontend/node_modules/svgo/node_modules/color-name/test.js", "./deepresearch/frontend/node_modules/core-js/modules/es.regexp.test.js", "./deepresearch/frontend/node_modules/core-js/es/regexp/test.js", "./deepresearch/frontend/node_modules/core-js/full/regexp/test.js", "./deepresearch/frontend/node_modules/core-js/stable/regexp/test.js", "./deepresearch/frontend/node_modules/core-js/actual/regexp/test.js", "./deepresearch/frontend/node_modules/core-js/features/regexp/test.js", "./deepresearch/frontend/node_modules/string.prototype.matchall/test/tests.js", "./deepresearch/frontend/node_modules/sprintf-js/test/test.js", "./deepresearch/frontend/node_modules/prettier-linter-helpers/test/index.test.js", "./deepresearch/frontend/node_modules/for-each/test/test.js", "./deepresearch/frontend/node_modules/raf/test.js", "./deepresearch/frontend/node_modules/@apideck/better-ajv-errors/src/index.test.ts", "./deepresearch/frontend/node_modules/dom-helpers/cjs/hyphenateStyle.js", "./deepresearch/frontend/node_modules/dom-helpers/cjs/hyphenateStyle.d.ts", "./deepresearch/frontend/node_modules/dom-helpers/esm/hyphenateStyle.js", "./deepresearch/frontend/node_modules/dom-helpers/esm/hyphenateStyle.d.ts", "./deepresearch/frontend/node_modules/reusify/test.js", "./deepresearch/frontend/node_modules/object.values/test/tests.js", "./deepresearch/frontend/node_modules/string.prototype.includes/.github/workflows/node-pretest.yml", "./deepresearch/frontend/node_modules/string.prototype.includes/tests/tests.js", "./deepresearch/frontend/node_modules/dotenv/types/test.ts", "./deepresearch/frontend/node_modules/mime/src/test.js", "./deepresearch/frontend/node_modules/string.prototype.trimend/test/tests.js", "./deepresearch/frontend/node_modules/rw/test/run-tests", "./deepresearch/frontend/node_modules/object.getownpropertydescriptors/test/tests.js", "./deepresearch/frontend/node_modules/array.prototype.reduce/test/tests.js", "./deepresearch/frontend/node_modules/react-scripts/scripts/test.js", "./deepresearch/frontend/node_modules/psl/types/test.ts", "./deepresearch/frontend/node_modules/eslint-plugin-jest/docs/rules/no-commented-out-tests.md", "./deepresearch/frontend/node_modules/eslint-plugin-jest/docs/rules/no-focused-tests.md", "./deepresearch/frontend/node_modules/eslint-plugin-jest/docs/rules/consistent-test-it.md", "./deepresearch/frontend/node_modules/eslint-plugin-jest/docs/rules/no-test-prefixes.md", "./deepresearch/frontend/node_modules/eslint-plugin-jest/docs/rules/no-disabled-tests.md", "./deepresearch/frontend/node_modules/eslint-plugin-jest/docs/rules/no-test-return-statement.md", "./deepresearch/frontend/node_modules/underscore/modules/_tagTester.js", "./deepresearch/frontend/node_modules/underscore/amd/_tagTester.js", "./deepresearch/frontend/node_modules/underscore/cjs/_tagTester.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/run_tests.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/public-path.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/amd-function-name.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/import-meta-worker.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/url-import-meta-worker.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/dynamic-import.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/import-worker-url-custom-scheme.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/asset-in-worker.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/import-worker-url.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/module-worker.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/more-workers.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/import-meta.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/worker.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/single-default.test.js", "./deepresearch/frontend/node_modules/@surma/rollup-plugin-off-main-thread/tests/simple-bundle.test.js", "./deepresearch/frontend/node_modules/is-typedarray/test.js", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/__tests__/require-es6-export-test.js", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/__tests__/require-es6-import-test.js", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/__tests__/import-vanilla-test.js", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/__tests__/import-es6-import-test.js", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/__tests__/require-es6-import-export-test.js", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/__tests__/import-es6-import-export-test.js", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/__tests__/import-es6-export-test.js", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/__tests__/require-vanilla-test.js", "./deepresearch/frontend/node_modules/identity-obj-proxy/src/__tests__/index-test.js", "./deepresearch/frontend/node_modules/jsonpath/node_modules/esprima/test/test.js", "./deepresearch/frontend/node_modules/arraybuffer.prototype.slice/test/tests.js", "./deepresearch/frontend/node_modules/string.prototype.trim/test/tests.js", "./deepresearch/frontend/node_modules/find-root/test/test.js", "./deepresearch/frontend/node_modules/glob-to-regexp/test.js", "./deepresearch/frontend/node_modules/array.prototype.tosorted/test/tests.js", "./deepresearch/frontend/node_modules/selfsigned/test/tests.js", "./deepresearch/frontend/node_modules/gensync/test/index.test.js", "./deepresearch/frontend/node_modules/es-abstract/2017/TestIntegrityLevel.js", "./deepresearch/frontend/node_modules/es-abstract/2017/ToDateString.js", "./deepresearch/frontend/node_modules/es-abstract/2017/RawBytesToNumber.js", "./deepresearch/frontend/node_modules/es-abstract/2015/TestIntegrityLevel.js", "./deepresearch/frontend/node_modules/es-abstract/2015/ToDateString.js", "./deepresearch/frontend/node_modules/es-abstract/2018/TestIntegrityLevel.js", "./deepresearch/frontend/node_modules/es-abstract/2018/ToDateString.js", "./deepresearch/frontend/node_modules/es-abstract/2018/RawBytesToNumber.js", "./deepresearch/frontend/node_modules/es-abstract/2018/DateString.js", "./deepresearch/frontend/node_modules/es-abstract/2024/TestIntegrityLevel.js", "./deepresearch/frontend/node_modules/es-abstract/2024/ToDateString.js", "./deepresearch/frontend/node_modules/es-abstract/2024/RawBytesToNumeric.js", "./deepresearch/frontend/node_modules/es-abstract/2024/DateString.js", "./deepresearch/frontend/node_modules/es-abstract/2023/TestIntegrityLevel.js", "./deepresearch/frontend/node_modules/es-abstract/2023/ToDateString.js", "./deepresearch/frontend/node_modules/es-abstract/2023/RawBytesToNumeric.js", "./deepresearch/frontend/node_modules/es-abstract/2023/DateString.js", "./deepresearch/frontend/node_modules/es-abstract/2020/TestIntegrityLevel.js", "./deepresearch/frontend/node_modules/es-abstract/2020/ToDateString.js", "./deepresearch/frontend/node_modules/es-abstract/2020/RawBytesToNumeric.js", "./deepresearch/frontend/node_modules/es-abstract/2020/DateString.js", "./deepresearch/frontend/node_modules/es-abstract/helpers/regexTester.js", "./deepresearch/frontend/node_modules/es-abstract/2019/TestIntegrityLevel.js", "./deepresearch/frontend/node_modules/es-abstract/2019/ToDateString.js", "./deepresearch/frontend/node_modules/es-abstract/2019/RawBytesToNumber.js", "./deepresearch/frontend/node_modules/es-abstract/2019/DateString.js", "./deepresearch/frontend/node_modules/es-abstract/2022/TestIntegrityLevel.js", "./deepresearch/frontend/node_modules/es-abstract/2022/ToDateString.js", "./deepresearch/frontend/node_modules/es-abstract/2022/RawBytesToNumeric.js", "./deepresearch/frontend/node_modules/es-abstract/2022/DateString.js", "./deepresearch/frontend/node_modules/es-abstract/2021/TestIntegrityLevel.js", "./deepresearch/frontend/node_modules/es-abstract/2021/ToDateString.js", "./deepresearch/frontend/node_modules/es-abstract/2021/RawBytesToNumeric.js", "./deepresearch/frontend/node_modules/es-abstract/2021/DateString.js", "./deepresearch/frontend/node_modules/es-abstract/2016/TestIntegrityLevel.js", "./deepresearch/frontend/node_modules/es-abstract/2016/ToDateString.js", "./deepresearch/frontend/node_modules/object.assign/test/tests.js", "./deepresearch/frontend/node_modules/wbuf/test/wbuf-test.js", "./deepresearch/frontend/node_modules/fast-uri/types/index.test-d.ts", "./deepresearch/frontend/node_modules/fast-uri/.github/tests_checker.yml", "./deepresearch/frontend/node_modules/fast-uri/test/parse.test.js", "./deepresearch/frontend/node_modules/fast-uri/test/util.test.js", "./deepresearch/frontend/node_modules/fast-uri/test/uri-js.test.js", "./deepresearch/frontend/node_modules/fast-uri/test/resolve.test.js", "./deepresearch/frontend/node_modules/fast-uri/test/equal.test.js", "./deepresearch/frontend/node_modules/fast-uri/test/serialize.test.js", "./deepresearch/frontend/node_modules/fast-uri/test/ajv.test.js", "./deepresearch/frontend/node_modules/fast-uri/test/compatibility.test.js", "./deepresearch/frontend/node_modules/node-int64/test.js", "./deepresearch/frontend/node_modules/@types/node/test.d.ts", "./deepresearch/frontend/node_modules/damerau-levenshtein/test/test.js", "./deepresearch/frontend/node_modules/object-inspect/test-core-js.js", "./deepresearch/frontend/node_modules/object-inspect/test/quoteStyle.js", "./deepresearch/frontend/node_modules/multicast-dns/test.js", "./deepresearch/frontend/node_modules/tsconfig-paths/src/__tests__/tsconfig-loader.test.ts", "./deepresearch/frontend/node_modules/tsconfig-paths/src/__tests__/match-path-async.test.ts", "./deepresearch/frontend/node_modules/tsconfig-paths/src/__tests__/config-loader.test.ts", "./deepresearch/frontend/node_modules/tsconfig-paths/src/__tests__/filesystem.test.ts", "./deepresearch/frontend/node_modules/tsconfig-paths/src/__tests__/match-path-sync.test.ts", "./deepresearch/frontend/node_modules/tsconfig-paths/src/__tests__/try-path.test.ts", "./deepresearch/frontend/node_modules/tsconfig-paths/src/__tests__/mapping-entry.test.ts", "./deepresearch/frontend/node_modules/fastq/test/test.js", "./deepresearch/frontend/node_modules/exit/test/exit_test.js", "./deepresearch/frontend/node_modules/obuf/test/buffer-test.js", "./deepresearch/frontend/node_modules/eslint-plugin-testing-library/rules/consistent-data-testid.js", "./deepresearch/frontend/node_modules/eslint-plugin-testing-library/create-testing-library-rule/detect-testing-library-utils.js", "./deepresearch/frontend/node_modules/handle-thing/test/api-test.js", "./deepresearch/frontend/node_modules/has-symbols/test/tests.js", "./deepresearch/frontend/node_modules/object-is/test/tests.js", "./deepresearch/frontend/node_modules/array-includes/test/tests.js", "./deepresearch/frontend/node_modules/postcss-clamp/index.test.js", "./deepresearch/frontend/node_modules/babel-preset-react-app/test.js", "./deepresearch/frontend/node_modules/object.entries/test/tests.js", "./deepresearch/frontend/node_modules/array.prototype.flatmap/test/tests.js", "./deepresearch/frontend/node_modules/react-dom/test-utils.js", "./deepresearch/frontend/node_modules/react-dom/umd/react-dom-test-utils.development.js", "./deepresearch/frontend/node_modules/react-dom/umd/react-dom-test-utils.production.min.js", "./deepresearch/frontend/node_modules/react-dom/cjs/react-dom-test-utils.development.js", "./deepresearch/frontend/node_modules/react-dom/cjs/react-dom-test-utils.production.min.js", "./deepresearch/frontend/node_modules/array.prototype.findlast/test/tests.js", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/vitest.d.ts", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/vitest.js", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/vitest.d.ts", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/__tests__/bun/bun-types.test.ts", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/__tests__/bun/bun-custom-expect-types.test.ts", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/__tests__/jest-globals/jest-globals-custom-expect-types.test.ts", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/__tests__/jest-globals/jest-globals-types.test.ts", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/__tests__/vitest/vitest-custom-expect-types.test.ts", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/__tests__/vitest/vitest-types.test.ts", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/__tests__/jest/jest-custom-expect-types.test.ts", "./deepresearch/frontend/node_modules/@testing-library/jest-dom/types/__tests__/jest/jest-types.test.ts", "./deepresearch/frontend/node_modules/string.prototype.trimstart/test/tests.js", "./deepresearch/frontend/node_modules/postcss-svgo/node_modules/svgo/plugins/addAttributesToSVGElement.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/hasProp-test.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/getPropValue-flowparser-test.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/elementType-test.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/eventHandlers-test.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/getPropValue-babelparser-test.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/getProp-parser-test.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/getProp-test.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/getPropLiteralValue-flowparser-test.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/propName-test.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/getPropLiteralValue-babelparser-test.js", "./deepresearch/frontend/node_modules/jsx-ast-utils/__tests__/src/index-test.js", "./deepresearch/frontend/node_modules/reflect.getprototypeof/test/tests.js", "./deepresearch/frontend/node_modules/@mui/icons-material/EditAttributesTwoTone.d.ts", "./deepresearch/frontend/node_modules/@mui/icons-material/TipsAndUpdatesTwoTone.js", "./deepresearch/frontend/node_modules/@mui/icons-material/SpeakerNotesTwoTone.d.ts", "./deepresearch/frontend/node_modules/@mui/icons-material/NotesTwoTone.d.ts", "./deepresearch/frontend/node_modules/@mui/icons-material/NotesTwoTone.js", "./deepresearch/frontend/node_modules/@mui/icons-material/SpeakerNotesTwoTone.js", "./deepresearch/frontend/node_modules/@mui/icons-material/TipsAndUpdatesTwoTone.d.ts", "./deepresearch/frontend/node_modules/@mui/icons-material/EditAttributesTwoTone.js", "./deepresearch/frontend/node_modules/@mui/icons-material/esm/TipsAndUpdatesTwoTone.js", "./deepresearch/frontend/node_modules/@mui/icons-material/esm/NotesTwoTone.js", "./deepresearch/frontend/node_modules/@mui/icons-material/esm/SpeakerNotesTwoTone.js", "./deepresearch/frontend/node_modules/@mui/icons-material/esm/EditAttributesTwoTone.js", "./deepresearch/frontend/node_modules/@mui/material/modern/styles/createStyles.js", "./deepresearch/frontend/node_modules/@mui/material/legacy/styles/createStyles.js", "./deepresearch/frontend/node_modules/@mui/material/node/styles/createStyles.js", "./deepresearch/frontend/node_modules/@mui/material/styles/createStyles.js", "./deepresearch/frontend/node_modules/@mui/material/styles/createStyles.d.ts", "./deepresearch/frontend/node_modules/@mui/system/createStyled.d.ts", "./deepresearch/frontend/node_modules/@mui/system/createStyled.js", "./deepresearch/frontend/node_modules/@mui/system/modern/createStyled.js", "./deepresearch/frontend/node_modules/@mui/system/modern/Stack/createStack.js", "./deepresearch/frontend/node_modules/@mui/system/Stack/createStack.d.ts", "./deepresearch/frontend/node_modules/@mui/system/Stack/createStack.js", "./deepresearch/frontend/node_modules/@mui/system/legacy/createStyled.js", "./deepresearch/frontend/node_modules/@mui/system/legacy/Stack/createStack.js", "./deepresearch/frontend/node_modules/@mui/system/esm/createStyled.js", "./deepresearch/frontend/node_modules/@mui/system/esm/Stack/createStack.js", "./deepresearch/frontend/node_modules/array.prototype.findlastindex/test/tests.js", "./deepresearch/frontend/node_modules/es-iterator-helpers/test/tests.js", "./deepresearch/frontend/node_modules/es-iterator-helpers/test/helpers/testIterator.js", "./deepresearch/frontend/node_modules/thunky/test.js", "./deepresearch/frontend/node_modules/coa/node_modules/color-name/test.js", "./deepresearch/frontend/node_modules/regexp.prototype.flags/test/tests.js", "./deepresearch/frontend/node_modules/@babel/runtime/helpers/classCheckPrivateStaticFieldDescriptor.js", "./deepresearch/frontend/node_modules/@babel/runtime/helpers/classCheckPrivateStaticAccess.js", "./deepresearch/frontend/node_modules/@babel/runtime/helpers/esm/classCheckPrivateStaticFieldDescriptor.js", "./deepresearch/frontend/node_modules/@babel/runtime/helpers/esm/classCheckPrivateStaticAccess.js", "./deepresearch/frontend/node_modules/spdy/test/server-test.js", "./deepresearch/frontend/node_modules/spdy/test/client-test.js", "./deepresearch/frontend/node_modules/proxy-from-env/test.js", "./deepresearch/src/deep_research_core/utils/test_utils.py", "./deepresearch/src/deep_research_core/utils/__pycache__/test_utils.cpython-310.pyc", "./deepresearch/src/deep_research_core/agents/test_vietnamese_search.py", "./deepresearch/src/deep_research_core/agents/test_plugin_integration.py", "./deepresearch/src/deep_research_core/agents/test_web_search_agent_merged.py", "./deepresearch/src/deep_research_core/agents/tests/test_web_search_agent_integration.py", "./deepresearch/src/deep_research_core/agents/tests/test_web_search_agent_simple.py", "./deepresearch/src/deep_research_core/agents/tests/test_web_search_query_analysis.py", "./deepresearch/src/deep_research_core/agents/tests/test_web_search_agent_simple_features.py", "./deepresearch/src/deep_research_core/agents/tests/integration/test_web_search_integration.py", "./deepresearch/src/deep_research_core/agents/tests/unit/test_web_search_agent_optimizations.py", "./deepresearch/src/deep_research_core/agents/tests/unit/test_improvements.py", "./deepresearch/src/deep_research_core/agents/tests/unit/test_vietnamese_nlp.py", "./deepresearch/src/deep_research_core/agents/tests/unit/test_web_search_agent_comprehensive.py", "./deepresearch/src/deep_research_core/agents/tests/unit/test_rate_limiter.py", "./deepresearch/src/deep_research_core/agents/tests/unit/test_utils.py", "./deepresearch/src/deep_research_core/agents/tests/unit/test_web_search_agent_improvements.py", "./deepresearch/src/deep_research_core/agents/tests/unit/__pycache__/test_rate_limiter.cpython-310.pyc", "./deepresearch/src/deep_research_core/agents/tests/performance/test_websearch_performance.py", "./deepresearch/src/deep_research_core/web/templates/feedback/ab_testing.html", "./deepresearch/src/deep_research_core/web/templates/feedback/create_ab_test.html", "./deepresearch/src/deep_research_core/web/templates/feedback/view_ab_test.html", "./deepresearch/src/deep_research_core/web/templates/api/test.html", "./deepresearch/src/deep_research_core/web/templates/api/test_result.html", "./deepresearch/src/deep_research_core/web/utils/ab_testing.py", "./deepresearch/src/scripts/test_web_search_improvements.py", "./deepresearch/src/tests/test_adaptive_kv_cache.py", "./deepresearch/src/tests/test_consensus.py", "./deepresearch/src/tests/test_token_importance.py", "./deepresearch/src/tests/test_kv_cache_pruning.py", "./deepresearch/temp_implementation/openrouter_dpo_test.py", "./deepresearch/scripts/analyze_test_performance.py", "./deepresearch/scripts/run_tests.sh", "./deepresearch/scripts/run_regression_tests.sh", "./deepresearch/test/test_advanced_vietnamese_semantic.py", "./deepresearch/test/test_network_manager.py", "./deepresearch/tests/run_all_tests.py", "./deepresearch/tests/test_vietnamese_diacritic.py", "./deepresearch/tests/test_cot_optimization.py", "./deepresearch/tests/test_chunked_attention.py", "./deepresearch/tests/test_captcha_handler.py", "./deepresearch/tests/test_result_analyzer.py", "./deepresearch/tests/test_adaptive_rate_limiter.py", "./deepresearch/tests/test_domain_roles.py", "./deepresearch/tests/test_adaptive_crawler_captcha_integration_full.py", "./deepresearch/tests/test_enhanced_web_search_agent.py", "./deepresearch/tests/test_gradient_accumulation.py", "./deepresearch/tests/test_gradient_checkpointing.py", "./deepresearch/tests/test_rag_prompt_templates.py", "./deepresearch/tests/test_llm_integration.py", "./deepresearch/tests/test_vietnamese_dialect.py", "./deepresearch/tests/test_vietnamese_environment_support.py", "./deepresearch/tests/test_bot_avoidance.py", "./deepresearch/tests/test_search_utils.py", "./deepresearch/tests/test_ragtotcot_weight_optimizer.py", "./deepresearch/tests/test_web_search_agent_local_additional.py", "./deepresearch/tests/test_vietnamese_utils.py", "./deepresearch/tests/test_file_format_handler.py", "./deepresearch/tests/test_rag_tot_cot_integration.py", "./deepresearch/tests/test_captcha_fallback.py", "./deepresearch/tests/test_domain_specific_analyzer.py", "./deepresearch/tests/test_web_search_agent_local_async.py", "./deepresearch/tests/test_web_search_agent_local_multimedia.py", "./deepresearch/tests/test_error_recovery.py", "./deepresearch/tests/test_vietnamese_trajectory_collection.py", "./deepresearch/tests/test_adaptive_scraper.py", "./deepresearch/tests/test_vietnamese_compound_processor.py", "./deepresearch/tests/test_performance_optimization.py", "./deepresearch/tests/test_rl_model_paradigm_vietnamese.py", "./deepresearch/tests/test_adaptive_crawler_upgrade.py", "./deepresearch/tests/test_error_recovery_integration.py", "./deepresearch/tests/test_enhanced_result_ranker.py", "./deepresearch/tests/test_improved_search.py", "./deepresearch/tests/README_TESTS.md", "./deepresearch/tests/test_qa_evaluator.py", "./deepresearch/tests/test_spa_handling.py", "./deepresearch/tests/test_agent_environment.py", "./deepresearch/tests/test_content_summarizer.py", "./deepresearch/tests/test_adaptive_crawler_basic.py", "./deepresearch/tests/test_search_result_optimizer.py", "./deepresearch/tests/test_improvements.py", "./deepresearch/tests/test_timeout_recovery.py", "./deepresearch/tests/test_enhanced_cache.py", "./deepresearch/tests/test_sqlite_vector_store.py", "./deepresearch/tests/test_enhanced_cot_advanced.py", "./deepresearch/tests/test_specialized_extractors.py", "./deepresearch/tests/test_rag_tot_cot.py", "./deepresearch/tests/test_query_memory_system.py", "./deepresearch/tests/run_rl_tuning_tests.py", "./deepresearch/tests/test_vietnamese_support.py", "./deepresearch/tests/test_shared_memory_query.py", "./deepresearch/tests/test_error_handling.py", "./deepresearch/tests/test_resource_manager.py", "./deepresearch/tests/test_specialized_search.py", "./deepresearch/tests/test_react.py", "./deepresearch/tests/test_multilingual_crawler.py", "./deepresearch/tests/test_plugin_manager.py", "./deepresearch/tests/test_nlp_analyzers.py", "./deepresearch/tests/test_cache_manager.py", "./deepresearch/tests/test_captcha_handler_integration.py", "./deepresearch/tests/test_infinite_scroll.py", "./deepresearch/tests/test_advanced_features.py", "./deepresearch/tests/test_integrated_web_search_agent_fixes.py", "./deepresearch/tests/test_web_search_agent_local_file_extraction.py", "./deepresearch/tests/test_content_extraction_utils.py", "./deepresearch/tests/test_totrag_enhanced.py", "./deepresearch/tests/test_vietnamese_rl_adapter.py", "./deepresearch/tests/test_query_decomposer_integration.py", "./deepresearch/tests/test_feedback_loop.py", "./deepresearch/tests/test_adaptive_crawler_captcha_integration.py", "./deepresearch/tests/test_query_analyzer_cache.py", "./deepresearch/tests/test_cotrag_enhanced.py", "./deepresearch/tests/test_rate_limiter.py", "./deepresearch/tests/test_query_analyzer.py", "./deepresearch/tests/test_file_extraction.py", "./deepresearch/tests/test_ajax_handling.py", "./deepresearch/tests/test_performance_profiler.py", "./deepresearch/tests/test_vietnamese_dialect_processor_integration.py", "./deepresearch/tests/test_vietnamese_rl_evaluator.py", "./deepresearch/tests/test_vietnamese_agent_support.py", "./deepresearch/tests/test_integrated_web_search_agent.py", "./deepresearch/tests/test_plugins.py", "./deepresearch/tests/test_enhanced_search_agent.py", "./deepresearch/tests/test_adaptive_crawler_integration.py", "./deepresearch/tests/test_adaptive_crawler_file_extraction.py", "./deepresearch/tests/test_simple.py", "./deepresearch/tests/test_semantic_analyzer.py", "./deepresearch/tests/test_web_search_agent_local_comprehensive_all.py", "./deepresearch/tests/test_fallback_methods.py", "./deepresearch/tests/test_web_search_agent_local_improvements.py", "./deepresearch/tests/test_vietnamese_domain_compounds.py", "./deepresearch/tests/test_cot_evaluator.py", "./deepresearch/tests/test_question_complexity_evaluator.py", "./deepresearch/tests/test_form_handling.py", "./deepresearch/tests/test_vietnamese_support_integration.py", "./deepresearch/tests/test_robots_parser.py", "./deepresearch/tests/test_llm_integration_vietnamese.py", "./deepresearch/tests/test_dynamic_content_handler.py", "./deepresearch/tests/test_new_features.py", "./deepresearch/tests/test_multi_agent/test_enhanced_bayesian_consensus.py", "./deepresearch/tests/test_multi_agent/test_task_decomposer_simple.py", "./deepresearch/tests/test_multi_agent/test_consensus.py", "./deepresearch/tests/test_multi_agent/test_bayesian_consensus.py", "./deepresearch/tests/test_multi_agent/test_task_decomposer.py", "./deepresearch/tests/test_multi_agent/test_multi_round_consensus.py", "./deepresearch/tests/reasoning/integrated/test_cot_rag_integration.py", "./deepresearch/tests/reasoning/integrated/test_tot_rag_integration.py", "./deepresearch/tests/integration/test_search_cache_integration.py", "./deepresearch/tests/integration/test_reasoning_rl_integration.py", "./deepresearch/tests/integration/test_milvus_vector_store.py", "./deepresearch/tests/integration/test_cohere_mistral_integration.py", "./deepresearch/tests/integration/rl_tuning/sft/test_deepseek_sft_integration.py", "./deepresearch/tests/integration/rl_tuning/frameworks/test_frameworks_integration.py", "./deepresearch/tests/integration/rl_tuning/frameworks/__pycache__/test_frameworks_integration.cpython-310.pyc", "./deepresearch/tests/integration/reasoning/test_rag_integration.py", "./deepresearch/tests/integration/reasoning/test_adaptive_cot_integration.py", "./deepresearch/tests/integration/reasoning/test_vector_stores_integration.py", "./deepresearch/tests/integration/reasoning/test_enhanced_rag_tot_cot_integration.py", "./deepresearch/tests/integration/__pycache__/test_search_cache_integration.cpython-310.pyc", "./deepresearch/tests/integration/__pycache__/test_milvus_vector_store.cpython-310.pyc", "./deepresearch/tests/integration/__pycache__/test_reasoning_rl_integration.cpython-310.pyc", "./deepresearch/tests/integration/__pycache__/test_cohere_mistral_integration.cpython-310.pyc", "./deepresearch/tests/integration/providers/test_provider_integration.py", "./deepresearch/tests/integration/providers/__pycache__/test_provider_integration.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_adaptive_crawler_upgrade.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_infinite_scroll.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_enhanced_search_agent.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_plugin_manager.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_adaptive_scraper.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_ajax_handling.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_adaptive_crawler_file_extraction.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_specialized_search.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_fallback_methods.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_content_extraction_utils.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_captcha_handler_integration.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_spa_handling.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_domain_specific_analyzer.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_cache_manager.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_cot_evaluator.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_domain_roles.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_error_handling.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_adaptive_crawler_captcha_integration_full.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_web_search_agent_local_improvements.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_adaptive_crawler_integration.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_improvements.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_agent_environment.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_captcha_handler.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_enhanced_result_ranker.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_dynamic_content_handler.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_cot_optimization.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_adaptive_rate_limiter.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_adaptive_crawler_captcha_integration.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_web_search_agent_local_comprehensive_all.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_enhanced_cache.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_chunked_attention.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_cotrag_enhanced.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_bot_avoidance.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_file_extraction.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_advanced_features.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_enhanced_web_search_agent.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_captcha_fallback.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_enhanced_cot_advanced.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_content_summarizer.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_web_search_agent_simple.cpython-310.pyc", "./deepresearch/tests/__pycache__/test_adaptive_crawler_basic.cpython-310.pyc", "./deepresearch/tests/unit/test_hyde_retriever.py", "./deepresearch/tests/unit/rag/document_processing/test_context_processor.py", "./deepresearch/tests/unit/rag/document_processing/test_semantic_chunker.py", "./deepresearch/tests/unit/rag/search/test_hybrid_search.py", "./deepresearch/tests/unit/rag/search/test_keyword_search.py", "./deepresearch/tests/unit/rl_tuning/test_rl_edge_cases.py", "./deepresearch/tests/unit/rl_tuning/test_agent_environment.py", "./deepresearch/tests/unit/rl_tuning/test_rl_modules_comprehensive.py", "./deepresearch/tests/unit/rl_tuning/sft/test_data_augmentation.py", "./deepresearch/tests/unit/rl_tuning/sft/test_evaluation.py", "./deepresearch/tests/unit/rl_tuning/sft/test_sft_base.py", "./deepresearch/tests/unit/rl_tuning/sft/test_deepseek_sft.py", "./deepresearch/tests/unit/rl_tuning/sft/test_qwq_sft.py", "./deepresearch/tests/unit/rl_tuning/prm/test_base.py", "./deepresearch/tests/unit/rl_tuning/prm/test_preference_model.py", "./deepresearch/tests/unit/rl_tuning/prm/test_data_utils.py", "./deepresearch/tests/unit/rl_tuning/prm/test_reward_model.py", "./deepresearch/tests/unit/rl_tuning/prm/test_openai_reward_model.py", "./deepresearch/tests/unit/rl_tuning/prm/test_anthropic_reward_model.py", "./deepresearch/tests/unit/rl_tuning/prm/test_deepseek_reward_model.py", "./deepresearch/tests/unit/rl_tuning/action_space_awareness/test_action_space_awareness.py", "./deepresearch/tests/unit/rl_tuning/frameworks/test_tinyzero_integration.py", "./deepresearch/tests/unit/rl_tuning/frameworks/test_openr1_integration.py", "./deepresearch/tests/unit/rl_tuning/frameworks/test_verl_integration.py", "./deepresearch/tests/unit/rl_tuning/frameworks/test_trlx_integration.py", "./deepresearch/tests/unit/rl_tuning/frameworks/test_framework_factory.py", "./deepresearch/tests/unit/rl_tuning/grpo/test_openrouter_grpo.py", "./deepresearch/tests/unit/rl_tuning/grpo/test_qwq_grpo.py", "./deepresearch/tests/unit/rl_tuning/grpo/test_vietnamese_reward.py", "./deepresearch/tests/unit/rl_tuning/grpo/test_gemini_grpo.py", "./deepresearch/tests/unit/rl_tuning/grpo/test_reward_functions.py", "./deepresearch/tests/unit/rl_tuning/grpo/test_huggingface_grpo.py", "./deepresearch/tests/unit/rl_tuning/grpo/test_mistral_grpo.py", "./deepresearch/tests/unit/rl_tuning/grpo/test_cohere_grpo.py", "./deepresearch/tests/unit/rl_tuning/grpo/test_deepseek_grpo.py", "./deepresearch/tests/unit/rl_tuning/adapters/test_verl_adapter.py", "./deepresearch/tests/unit/rl_tuning/model_paradigm/test_rl_tuner.py", "./deepresearch/tests/unit/rl_tuning/model_paradigm/test_rl_model_paradigm.py", "./deepresearch/tests/unit/rl_tuning/model_paradigm/test_performance_optimizer.py", "./deepresearch/tests/unit/rl_tuning/model_paradigm/test_complex_case_handler.py", "./deepresearch/tests/unit/rl_tuning/model_paradigm/test_vietnamese_support.py", "./deepresearch/tests/unit/rl_tuning/benchmarks/test_agent_benchmark_evaluator.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_gemini_dpo.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_preprocessing.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_base.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_preference_model.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_openai_dpo.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_dpo_dataset.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_openrouter_dpo.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_loss_functions.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_trainer.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_dpo_utils.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_dpo_trainer.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_loss.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_callbacks.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_dataset.py", "./deepresearch/tests/unit/rl_tuning/dpo/test_anthropic_dpo.py", "./deepresearch/tests/unit/rl_tuning/rewards/test_agent_reward_model.py", "./deepresearch/tests/unit/rl_tuning/trajectories/test_trajectory_collector.py", "./deepresearch/tests/unit/rl_tuning/trajectories/test_trajectory_scaler.py", "./deepresearch/tests/unit/rl_tuning/action_space/test_action_space_awareness.py", "./deepresearch/tests/unit/rl_tuning/action_space/test_action_space_manager.py", "./deepresearch/tests/unit/rl_tuning/ppo/test_anthropic_ppo.py", "./deepresearch/tests/unit/rl_tuning/ppo/test_base.py", "./deepresearch/tests/unit/rl_tuning/ppo/test_advantage_utils.py", "./deepresearch/tests/unit/rl_tuning/ppo/test_openai_ppo.py", "./deepresearch/tests/unit/rl_tuning/ppo/test_openrouter_ppo.py", "./deepresearch/tests/unit/rl_tuning/ppo/test_qwq_ppo.py", "./deepresearch/tests/unit/rl_tuning/ppo/test_deepseek_ppo.py", "./deepresearch/tests/unit/reasoning/test_evidence_verifier.py", "./deepresearch/tests/unit/reasoning/test_cotrag_advanced.py", "./deepresearch/tests/unit/reasoning/test_adaptive_cot.py", "./deepresearch/tests/unit/reasoning/test_multi_stage_reasoner.py", "./deepresearch/tests/unit/reasoning/test_cotrag_error_analysis.py", "./deepresearch/tests/unit/reasoning/test_enhanced_ragtotcot_weight_optimizer.py", "./deepresearch/tests/unit/reasoning/test_enhanced_cotrag.py", "./deepresearch/tests/unit/reasoning/test_source_attribution_enhanced.py", "./deepresearch/tests/unit/reasoning/test_pinecone_rag.py", "./deepresearch/tests/unit/reasoning/test_recursive_tot.py", "./deepresearch/tests/unit/reasoning/test_real_time_retriever.py", "./deepresearch/tests/unit/reasoning/test_cotrag_weight_adjustment.py", "./deepresearch/tests/unit/reasoning/test_multi_source_validator.py", "./deepresearch/tests/unit/reasoning/test_cotrag_vietnamese_embedding_adapter.py", "./deepresearch/tests/unit/reasoning/test_cotrag_irrelevant_docs.py", "./deepresearch/tests/unit/reasoning/test_knowledge_graph_integration.py", "./deepresearch/tests/unit/reasoning/test_base_rag.py", "./deepresearch/tests/unit/reasoning/test_cotrag_advanced_strategies.py", "./deepresearch/tests/unit/reasoning/test_rag_error_handler.py", "./deepresearch/tests/unit/reasoning/test_knowledge_graph_rag.py", "./deepresearch/tests/unit/reasoning/test_weaviate_rag.py", "./deepresearch/tests/unit/reasoning/test_react_error_recovery.py", "./deepresearch/tests/unit/reasoning/test_query_decomposer.py", "./deepresearch/tests/unit/reasoning/test_tot_rag.py", "./deepresearch/tests/unit/reasoning/test_faiss_rag.py", "./deepresearch/tests/unit/reasoning/test_relevance_scorer.py", "./deepresearch/tests/unit/reasoning/test_cot_rag.py", "./deepresearch/tests/unit/reasoning/test_source_attribution.py", "./deepresearch/tests/unit/reasoning/test_cotrag_enhanced.py", "./deepresearch/tests/unit/reasoning/test_cotrag_comprehensive.py", "./deepresearch/tests/unit/reasoning/test_cotrag_adaptive_learning.py", "./deepresearch/tests/unit/reasoning/test_multi_query_decomposition.py", "./deepresearch/tests/unit/reasoning/test_enhanced_rag_tot_cot.py", "./deepresearch/tests/unit/reasoning/test_query_understanding.py", "./deepresearch/tests/unit/reasoning/test_self_reflection.py", "./deepresearch/tests/unit/reasoning/test_milvus_rag.py", "./deepresearch/tests/unit/reasoning/test_sqlite_vector_rag.py", "./deepresearch/tests/unit/reasoning/formats/test_evaluator.py", "./deepresearch/tests/unit/reasoning/formats/test_outcome_based.py", "./deepresearch/tests/unit/reasoning/formats/test_react.py", "./deepresearch/tests/unit/reasoning/formats/test_react_reasoner.py", "./deepresearch/tests/unit/reasoning/query_classification/test_ml_classifier.py", "./deepresearch/tests/unit/reasoning/cot/conftest.py", "./deepresearch/tests/unit/reasoning/cot/test_enhanced_cot_error.py", "./deepresearch/tests/unit/reasoning/cot/test_enhanced_cot.py", "./deepresearch/tests/unit/reasoning/cot/test_enhanced_cot_advanced.py", "./deepresearch/tests/unit/reasoning/cot/test_enhanced_cot_basic.py", "./deepresearch/tests/unit/reasoning/cot/run_tests.py", "./deepresearch/tests/unit/reasoning/cot/test_utils.py", "./deepresearch/tests/unit/reasoning/cot/test_enhanced_cot_performance.py", "./deepresearch/tests/unit/reasoning/cot/test_enhanced_cot_language.py", "./deepresearch/tests/unit/reasoning/cot/test_enhanced_cot_reasoning.py", "./deepresearch/tests/unit/reasoning/tot/test_tot_error_analysis.py", "./deepresearch/tests/unit/reasoning/tot/test_tot_advanced.py", "./deepresearch/tests/unit/reasoning/tot/test_tot.py", "./deepresearch/tests/unit/reasoning/tot/test_advanced_evaluator.py", "./deepresearch/tests/unit/reasoning/tot/test_tree_of_thought.py", "./deepresearch/tests/unit/reasoning/tot/test_tot_parameter_adjustment.py", "./deepresearch/tests/unit/reasoning/tot/test_advanced_error_analysis.py", "./deepresearch/tests/unit/reasoning/tot/test_tot_fixed.py", "./deepresearch/tests/unit/reasoning/tot/test_parameter_optimizer.py", "./deepresearch/tests/unit/reasoning/tot/test_tot_simple.py", "./deepresearch/tests/unit/reasoning/tot/test_tot_minimal.py", "./deepresearch/tests/unit/reasoning/tot/test_tot_advanced_evaluation.py", "./deepresearch/tests/unit/reasoning/tot/test_tot_optimized_advanced.py", "./deepresearch/tests/unit/reasoning/tot/test_tot_basic.py", "./deepresearch/tests/unit/reasoning/tot/test_advanced_tot_error_analysis.py", "./deepresearch/tests/unit/reasoning/tot/test_tot_optimized.py", "./deepresearch/tests/unit/reasoning/trajectory_scaling/test_base_scaler.py", "./deepresearch/tests/unit/reasoning/combined/test_multi_query_tot_rag_comprehensive.py", "./deepresearch/tests/unit/reasoning/combined/test_rag_tot_cot.py", "./deepresearch/tests/unit/reasoning/combined/test_multi_query_rag.py", "./deepresearch/tests/unit/reasoning/combined/test_rag_tot_cot_unittest.py", "./deepresearch/tests/unit/reasoning/combined/test_multi_query_tot_rag.py", "./deepresearch/tests/unit/reasoning/react/test_react_thompson_sampling.py", "./deepresearch/tests/unit/reasoning/react/test_react_rag.py", "./deepresearch/tests/unit/reasoning/react/test_adaptive_react_feedback.py", "./deepresearch/tests/unit/reasoning/react/test_adaptive_react_feedback_extensions.py", "./deepresearch/tests/unit/reasoning/react/test_react_local.py", "./deepresearch/tests/unit/reasoning/react/test_react_feedback_types.py", "./deepresearch/tests/unit/reasoning/react/test_react_reinforcement_optimization.py", "./deepresearch/tests/unit/reasoning/react/test_react_rag_advanced.py", "./deepresearch/tests/unit/reasoning/react/test_thompson_sampling.py", "./deepresearch/tests/unit/reasoning/react/test_react_feedback_storage.py", "./deepresearch/tests/unit/reasoning/react/test_react_basic.py", "./deepresearch/tests/unit/reasoning/react/test_react_advanced_features.py", "./deepresearch/tests/unit/reasoning/react/test_react_advanced.py", "./deepresearch/tests/unit/multilingual/test_vietnamese_embeddings.py", "./deepresearch/tests/unit/retrieval/test_weaviate_vector_store.py", "./deepresearch/tests/unit/retrieval/test_faiss_vector_store.py", "./deepresearch/tests/unit/retrieval/test_pinecone_vector_store.py", "./deepresearch/tests/unit/retrieval/test_abstract_vector_store.py", "./deepresearch/tests/unit/retrieval/document_chunking/test_base_chunker.py", "./deepresearch/tests/unit/retrieval/document_chunking/test_smart_chunker.py", "./deepresearch/tests/unit/retrieval/vector_store/test_milvus_vector_store.py", "./deepresearch/tests/unit/providers/test_mistral_provider.py", "./deepresearch/tests/unit/providers/test_cohere_provider.py", "./deepresearch/tests/unit/utils/test_performance_metrics.py", "./deepresearch/tests/unit/utils/test_intelligent_error_detection.py", "./deepresearch/tests/unit/utils/test_text_processing.py", "./deepresearch/tests/unit/utils/test_feedback_persistence.py", "./deepresearch/tests/unit/utils/test_advanced_recovery_strategies.py", "./deepresearch/tests/unit/utils/test_tool_selection_advanced.py", "./deepresearch/tests/unit/utils/test_context_based_tool_selector.py", "./deepresearch/tests/unit/utils/test_react_feedback_improvements.py", "./deepresearch/tests/unit/utils/test_structured_logging.py", "./deepresearch/tests/unit/utils/test_react_feedback_reinforcement.py", "./deepresearch/tests/unit/utils/test_error_recovery_strategies.py", "./deepresearch/tests/unit/utils/test_error_learning.py", "./deepresearch/tests/unit/utils/test_react_feedback.py", "./deepresearch/tests/unit/utils/test_cache_memory_manager.py", "./deepresearch/tests/unit/utils/test_reinforcement_learning.py", "./deepresearch/tests/unit/utils/test_simple.py", "./deepresearch/tests/unit/utils/test_alerting.py", "./deepresearch/tests/unit/utils/test_distributed_tracing.py", "./deepresearch/tests/unit/visualization/test_tot_rag_visualizer.py", "./deepresearch/tests/unit/evaluation/test_vietnamese_benchmark.py", "./deepresearch/tests/unit/evaluation/test_integrated_evaluator.py", "./deepresearch/tests/unit/evaluation/test_enhanced_vietnamese_rl_evaluator.py", "./deepresearch/tests/unit/agents/test_web_search_agent_base.py", "./deepresearch/tests/unit/agents/test_web_search_agent_cache_detailed.py", "./deepresearch/tests/unit/agents/test_web_search_error_recovery.py", "./deepresearch/tests/unit/agents/test_web_search_memory_optimizer.py", "./deepresearch/tests/unit/agents/test_web_search_cache_enhanced.py", "./deepresearch/tests/unit/agents/test_web_search_agent_cache.py", "./deepresearch/tests/unit/agents/test_web_search_cache.py", "./deepresearch/tests/unit/agents/test_web_search_vietnamese.py", "./deepresearch/tests/unit/models/test_qwq_32b.py", "./deepresearch/tests/unit/models/test_deepseek_r1.py", "./deepresearch/tests/unit/models/test_gpt_o1.py", "./deepresearch/tests/unit/models/test_base_reasoning_model.py", "./deepresearch/tests/unit/optimization/test_vietnamese_model_quantization.py", "./deepresearch/tests/unit/optimization/lora/test_lora_evaluator.py", "./deepresearch/tests/unit/optimization/lora/test_base_lora.py", "./deepresearch/tests/unit/optimization/caching/test_semantic_cache.py", "./deepresearch/tests/unit/optimization/caching/test_eviction_policies.py", "./deepresearch/tests/unit/optimization/caching/test_adaptive_search_cache_detailed.py", "./deepresearch/tests/unit/optimization/caching/test_predictive_cache.py", "./deepresearch/tests/unit/optimization/caching/test_cache_warmer.py", "./deepresearch/tests/unit/optimization/caching/test_cache_decorator.py", "./deepresearch/tests/unit/optimization/caching/test_distributed_cache.py", "./deepresearch/tests/unit/optimization/caching/test_adaptive_search_cache.py", "./deepresearch/tests/unit/optimization/mixed_precision/test_mixed_precision.py", "./deepresearch/tests/unit/optimization/quantization/test_mixed_precision_quantization.py", "./deepresearch/tests/unit/multi_agent/test_memory_compression.py", "./deepresearch/tests/unit/multi_agent/test_task_decomposer.py", "./deepresearch/tests/unit/multi_agent/test_memory_versioning.py", "./deepresearch/tests/performance/test_cache_performance.py", "./deepresearch/tests/performance/test_provider_performance.py", "./deepresearch/tests/performance/test_performance.py", "./deepresearch/tests/evaluation/test_vietnamese_quality_metrics.py", "./deepresearch/tests/test_optimization/test_parallel_inference.py", "./__pycache__/test_searxng_api.cpython-310.pyc", "./__pycache__/test_web_search_agent_local_features.cpython-310-pytest-8.3.4.pyc", "./__pycache__/test_web_search_agent_local_merged.cpython-310-pytest-8.3.4.pyc", "./__pycache__/test_web_search_agent_local_merged.cpython-310.pyc", "./src/deep_research_core/tests/test_vietnamese_search.py", "./src/deep_research_core/tests/test_credibility_evaluator.py", "./src/deep_research_core/tests/test_advanced_language_detector.py", "./tests/test_simple_language_detector.py", "./tests/test_captcha_handler.py", "./tests/test_vietnamese_diacritics.py", "./tests/test_optimized_crawlee.py", "./tests/test_vietnamese_processing.py", "./tests/test_factcheckorg_api_client.py", "./tests/test_credibility_models.py", "./tests/test_clickbait_detector.py", "./tests/test_feedback_collection.py", "./tests/test_query_analysis.py", "./tests/test_ml_feedback.py", "./tests/test_file_processor.py", "./tests/test_fact_check_integration.py", "./tests/test_multilevel_cache.py", "./tests/test_vietnamese_credibility.py", "./tests/run_stealth_test.py", "./tests/test_new_plugins.py", "./tests/test_answer_evaluation.py", "./tests/test_content_evaluation.py", "./tests/test_credibility_evaluator.py", "./tests/test_reliable_alternatives.py", "./tests/test_snopes_api_client.py", "./tests/test_fake_news_detector.py", "./tests/test_advanced_monitoring.py", "./tests/test_source_credibility.py", "./tests/test_cache_utils.py", "./tests/test_all_modules_integration.py", "./tests/test_disinformation_detector.py", "./tests/test_user_agent_manager.py", "./tests/test_politifact_api_client.py", "./tests/test_multilingual_text_processor.py", "./tests/test_feedback_integration.py", "./tests/test_advanced_content_extractor.py", "./tests/test_query_utils.py", "./tests/test_advanced_javascript_handler.py", "./tests/test_multilingual_utils.py", "./tests/test_stealth_plugin.py", "./tests/test_vietnamese_ranking.py", "./tests/test_advanced_content_analyzer.py", "./tests/__pycache__/test_multilingual_text_processor.cpython-310.pyc", "./tests/__pycache__/test_multilingual_utils.cpython-310.pyc", "./tests/__pycache__/test_optimized_crawlee.cpython-310.pyc", "./tests/__pycache__/test_user_agent_manager.cpython-310.pyc", "./tests/__pycache__/test_captcha_handler.cpython-310.pyc", "./tests/__pycache__/test_ml_feedback.cpython-310.pyc", "./tests/__pycache__/test_feedback_integration.cpython-310.pyc", "./tests/__pycache__/test_new_plugins.cpython-310.pyc", "./tests/__pycache__/test_all_modules_integration.cpython-310.pyc", "./tests/__pycache__/test_simple_language_detector.cpython-310.pyc", "./tests/__pycache__/test_advanced_monitoring.cpython-310.pyc", "./tests/unit/test_typesense_integration.py", "./tests/unit/test_elasticsearch_integration.py", "./tests/unit/test_multimedia_search.py", "./tests/unit/test_test_utils.py", "./tests/unit/test_search_engines_integration.py", "./tests/unit/test_multimedia_search_integration.py", "./tests/unit/test_web_search_agent_local.py", "./tests/unit/test_solr_integration.py", "./tests/unit/test_search_engine_selector.py", "./tests/unit/test_utils.py", "./tests/unit/test_meilisearch_integration.py", "./tests/unit/test_nlp_engine.py", "./tests/unit/__pycache__/test_adaptive_crawler_integration.cpython-310-pytest-8.3.4.pyc", "./tests/unit/__pycache__/test_utils.cpython-310.pyc", "./tests/unit/__pycache__/test_adaptive_crawler_integration_simple.cpython-310-pytest-8.3.4.pyc", "./tests/unit/__pycache__/test_multimedia_search_integration.cpython-310.pyc", "./tests/unit/__pycache__/test_test_utils.cpython-310.pyc", "./tests/unit/__pycache__/test_search_engine_selector.cpython-310.pyc", "./tests/unit/__pycache__/test_web_search_agent_local.cpython-310.pyc", "./tests/unit/__pycache__/test_vietnamese_nlp_utils.cpython-310.pyc", "./tests/unit/__pycache__/test_web_search_agent_vietnamese.cpython-310.pyc", "./tests/unit/__pycache__/test_multimedia_search.cpython-310.pyc", "./tests/utils/test_credibility_integrator.py", "./tests/utils/test_web_search_enhancer.py", "./tests/utils/test_credibility_integration.py", "./test_results/step9_language_handler_test_20250528_193908.json", "./test_results/step8_site_structure_test_20250528_193045.json", "./test_results/minimal_agent_test_20250524_015346.json", "./test_results/adaptive_crawler_consolidated_merged_test_20250524_025412.json", "./test_results/step9_language_handler_test_20250528_193814.json", "./test_results/final_comprehensive_integration_test_20250528_200019.json", "./test_results/step10_integration_manager_test_20250528_194447.json", "./test_results/adaptive_crawler_consolidated_merged_test_20250528_135108.json", "./test_results/adaptive_crawler_consolidated_test_20250524_015734.json", "./test_results/adaptive_crawler_consolidated_merged_test_20250528_141010.json", "./test_results/web_search_agent_merged_test.json", "./test_results/detailed_features_test.json", "./test_results/simple_functionality_test_20250528_124909.json"]}, "recommendations": {"priority_actions": ["Consolidate 50+ README files into organized docs/ structure", "Move all test files to unified tests/ directory", "Archive deepresearch/ after extracting useful features", "Clean up root level - move configs to config/", "Remove cache and temp files"], "file_moves": {"docs/": "All .md files except main README.md", "tests/": "All test_*.py files and test directories", "config/": "All .yml, .yaml, .json config files", "scripts/": "Utility .py scripts not part of main codebase", "archive/": "deepresearch/ directory after feature extraction"}, "estimated_reduction": "70% reduction in root level files"}}