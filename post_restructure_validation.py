#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Post-restructure validation: Kiểm tra và tối ưu hóa dự án sau khi tổ chức lại.
"""

import os
import sys
import json
import importlib.util
from datetime import datetime
from pathlib import Path

def test_main_imports():
    """Kiểm tra các import chính có hoạt động không."""
    print("🔍 Testing main imports...")
    
    import_tests = []
    
    # Test core imports
    test_cases = [
        {
            'module': 'src.deep_research_core.agents.web_search_agent_local_merged',
            'class': 'WebSearchAgentLocalMerged',
            'description': 'Main web search agent'
        },
        {
            'module': 'src.deep_research_core.agents.adaptive_crawler_consolidated_merged', 
            'class': 'AdaptiveCrawlerConsolidatedMerged',
            'description': 'Main adaptive crawler'
        },
        {
            'module': 'src.deep_research_core.utils.vietnamese_search_methods',
            'function': 'search_coccoc',
            'description': 'Vietnamese search methods'
        }
    ]
    
    for test_case in test_cases:
        try:
            # Add src to path if not already there
            if 'src' not in sys.path:
                sys.path.insert(0, 'src')
            
            module = importlib.import_module(test_case['module'])
            
            if 'class' in test_case:
                cls = getattr(module, test_case['class'])
                import_tests.append({
                    'test': test_case['description'],
                    'status': 'SUCCESS',
                    'details': f"Successfully imported {test_case['class']}"
                })
                print(f"  ✅ {test_case['description']}: {test_case['class']} imported successfully")
            
            elif 'function' in test_case:
                func = getattr(module, test_case['function'])
                import_tests.append({
                    'test': test_case['description'],
                    'status': 'SUCCESS', 
                    'details': f"Successfully imported {test_case['function']}"
                })
                print(f"  ✅ {test_case['description']}: {test_case['function']} imported successfully")
                
        except Exception as e:
            import_tests.append({
                'test': test_case['description'],
                'status': 'FAILED',
                'details': str(e)
            })
            print(f"  ❌ {test_case['description']}: {str(e)}")
    
    return import_tests

def check_requirements():
    """Kiểm tra requirements files."""
    print("\n📦 Checking requirements files...")
    
    requirements_check = []
    req_files = [
        'config_consolidated/requirements.txt',
        'config_consolidated/requirements-core.txt', 
        'config_consolidated/requirements-credibility.txt',
        'config_consolidated/requirements-llm.txt'
    ]
    
    for req_file in req_files:
        if os.path.exists(req_file):
            try:
                with open(req_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = [line.strip() for line in content.split('\n') if line.strip() and not line.startswith('#')]
                    
                requirements_check.append({
                    'file': req_file,
                    'status': 'EXISTS',
                    'packages': len(lines),
                    'sample_packages': lines[:5]  # First 5 packages
                })
                print(f"  ✅ {req_file}: {len(lines)} packages")
                
            except Exception as e:
                requirements_check.append({
                    'file': req_file,
                    'status': 'ERROR',
                    'error': str(e)
                })
                print(f"  ❌ {req_file}: {str(e)}")
        else:
            requirements_check.append({
                'file': req_file,
                'status': 'MISSING'
            })
            print(f"  ⚠️ {req_file}: Missing")
    
    return requirements_check

def validate_test_structure():
    """Kiểm tra cấu trúc test files."""
    print("\n🧪 Validating test structure...")
    
    test_validation = {
        'tests_consolidated': {'count': 0, 'files': []},
        'tests': {'count': 0, 'files': []},
        'src_tests': {'count': 0, 'files': []}
    }
    
    # Check tests_consolidated
    if os.path.exists('tests_consolidated'):
        test_files = [f for f in os.listdir('tests_consolidated') if f.endswith('.py')]
        test_validation['tests_consolidated']['count'] = len(test_files)
        test_validation['tests_consolidated']['files'] = test_files[:10]  # First 10
        print(f"  📁 tests_consolidated/: {len(test_files)} test files")
    
    # Check original tests directory
    if os.path.exists('tests'):
        test_files = []
        for root, dirs, files in os.walk('tests'):
            test_files.extend([f for f in files if f.endswith('.py')])
        test_validation['tests']['count'] = len(test_files)
        test_validation['tests']['files'] = test_files[:10]
        print(f"  📁 tests/: {len(test_files)} test files")
    
    # Check src tests
    if os.path.exists('src/deep_research_core/tests'):
        test_files = []
        for root, dirs, files in os.walk('src/deep_research_core/tests'):
            test_files.extend([f for f in files if f.endswith('.py')])
        test_validation['src_tests']['count'] = len(test_files)
        test_validation['src_tests']['files'] = test_files[:10]
        print(f"  📁 src/deep_research_core/tests/: {len(test_files)} test files")
    
    return test_validation

def check_documentation_structure():
    """Kiểm tra cấu trúc documentation."""
    print("\n📚 Checking documentation structure...")
    
    doc_structure = {
        'docs_consolidated': {'count': 0, 'categories': {}},
        'docs': {'count': 0, 'files': []}
    }
    
    # Check docs_consolidated
    if os.path.exists('docs_consolidated'):
        doc_files = [f for f in os.listdir('docs_consolidated') if f.endswith('.md')]
        doc_structure['docs_consolidated']['count'] = len(doc_files)
        
        # Categorize docs
        categories = {
            'README': [f for f in doc_files if f.startswith('README')],
            'TASK': [f for f in doc_files if f.startswith('TASK')],
            'ADAPTIVE_CRAWLER': [f for f in doc_files if 'ADAPTIVE_CRAWLER' in f],
            'WEBSEARCH': [f for f in doc_files if 'WebSearch' in f],
            'DESIGN': [f for f in doc_files if f.endswith('_design.md')],
            'OTHER': []
        }
        
        # Categorize remaining files
        categorized = set()
        for category, files in categories.items():
            if category != 'OTHER':
                categorized.update(files)
        
        categories['OTHER'] = [f for f in doc_files if f not in categorized]
        doc_structure['docs_consolidated']['categories'] = {k: len(v) for k, v in categories.items()}
        
        print(f"  📁 docs_consolidated/: {len(doc_files)} documentation files")
        for category, count in doc_structure['docs_consolidated']['categories'].items():
            if count > 0:
                print(f"    📄 {category}: {count} files")
    
    # Check original docs
    if os.path.exists('docs'):
        doc_files = [f for f in os.listdir('docs') if f.endswith('.md')]
        doc_structure['docs']['count'] = len(doc_files)
        doc_structure['docs']['files'] = doc_files
        print(f"  📁 docs/: {len(doc_files)} documentation files")
    
    return doc_structure

def create_quick_start_guide():
    """Tạo quick start guide."""
    print("\n📖 Creating quick start guide...")
    
    quick_start = """# 🚀 Quick Start Guide

## Cài đặt Dependencies

```bash
# Cài đặt dependencies cơ bản
pip install -r config_consolidated/requirements.txt

# Hoặc cài đặt theo từng loại
pip install -r config_consolidated/requirements-core.txt
pip install -r config_consolidated/requirements-credibility.txt
pip install -r config_consolidated/requirements-llm.txt
```

## Sử dụng WebSearchAgentLocal

```python
import sys
sys.path.append('src')

from deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged

# Khởi tạo agent
agent = WebSearchAgentLocalMerged()

# Tìm kiếm cơ bản
results = agent.search("biến đổi khí hậu", num_results=5)
print(results)

# Tìm kiếm với đánh giá độ tin cậy
results = agent.search(
    "tin tức mới nhất", 
    num_results=10,
    evaluate_credibility=True,
    extract_content=True
)
```

## Sử dụng AdaptiveCrawler

```python
from deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

# Khởi tạo crawler
crawler = AdaptiveCrawlerConsolidatedMerged()

# Crawl một URL
result = crawler.crawl("https://example.com")
print(result)

# Crawl nhiều URLs
urls = ["https://site1.com", "https://site2.com"]
results = crawler.crawl_multiple(urls)
```

## Chạy Tests

```bash
# Chạy tất cả tests
cd tests_consolidated/
python -m pytest

# Chạy test cụ thể
python test_web_search_agent_local_merged.py
```

## Cấu trúc Dự án

- `src/deep_research_core/` - Main codebase
- `docs_consolidated/` - Tài liệu chi tiết
- `tests_consolidated/` - Test files
- `config_consolidated/` - Configuration files
- `scripts_consolidated/` - Utility scripts
- `examples/` - Code examples

## Troubleshooting

### Import Errors
```python
# Thêm src vào Python path
import sys
sys.path.append('src')
```

### Missing Dependencies
```bash
# Cài đặt dependencies bị thiếu
pip install requests beautifulsoup4 playwright
```

### Test Failures
```bash
# Chạy tests với verbose output
python -m pytest -v tests_consolidated/
```
"""
    
    with open('QUICK_START.md', 'w', encoding='utf-8') as f:
        f.write(quick_start)
    
    print("  ✅ Created QUICK_START.md")

def generate_validation_report(import_tests, requirements_check, test_validation, doc_structure):
    """Tạo báo cáo validation."""
    print("\n📊 Generating validation report...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'validation_summary': {
            'import_tests': {
                'total': len(import_tests),
                'passed': len([t for t in import_tests if t['status'] == 'SUCCESS']),
                'failed': len([t for t in import_tests if t['status'] == 'FAILED'])
            },
            'requirements': {
                'total_files': len(requirements_check),
                'existing': len([r for r in requirements_check if r['status'] == 'EXISTS']),
                'missing': len([r for r in requirements_check if r['status'] == 'MISSING'])
            },
            'tests': {
                'tests_consolidated': test_validation['tests_consolidated']['count'],
                'tests_original': test_validation['tests']['count'],
                'src_tests': test_validation['src_tests']['count']
            },
            'documentation': {
                'docs_consolidated': doc_structure['docs_consolidated']['count'],
                'docs_original': doc_structure['docs']['count']
            }
        },
        'detailed_results': {
            'import_tests': import_tests,
            'requirements_check': requirements_check,
            'test_validation': test_validation,
            'doc_structure': doc_structure
        },
        'recommendations': []
    }
    
    # Generate recommendations
    if report['validation_summary']['import_tests']['failed'] > 0:
        report['recommendations'].append("Fix import errors in main modules")
    
    if report['validation_summary']['requirements']['missing'] > 0:
        report['recommendations'].append("Create missing requirements files")
    
    if report['validation_summary']['tests']['tests_consolidated'] == 0:
        report['recommendations'].append("Move test files to tests_consolidated/")
    
    with open('POST_RESTRUCTURE_VALIDATION_REPORT.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("  ✅ Validation report saved: POST_RESTRUCTURE_VALIDATION_REPORT.json")
    return report

def main():
    """Main validation function."""
    print("🔍 POST-RESTRUCTURE VALIDATION")
    print("=" * 50)
    
    # Step 1: Test imports
    import_tests = test_main_imports()
    
    # Step 2: Check requirements
    requirements_check = check_requirements()
    
    # Step 3: Validate test structure
    test_validation = validate_test_structure()
    
    # Step 4: Check documentation
    doc_structure = check_documentation_structure()
    
    # Step 5: Create quick start guide
    create_quick_start_guide()
    
    # Step 6: Generate report
    report = generate_validation_report(import_tests, requirements_check, test_validation, doc_structure)
    
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY:")
    print(f"  🔍 Import tests: {report['validation_summary']['import_tests']['passed']}/{report['validation_summary']['import_tests']['total']} passed")
    print(f"  📦 Requirements files: {report['validation_summary']['requirements']['existing']}/{report['validation_summary']['requirements']['total_files']} exist")
    print(f"  🧪 Test files: {report['validation_summary']['tests']['tests_consolidated']} in tests_consolidated/")
    print(f"  📚 Documentation: {report['validation_summary']['documentation']['docs_consolidated']} in docs_consolidated/")
    
    if report['recommendations']:
        print("\n💡 RECOMMENDATIONS:")
        for rec in report['recommendations']:
            print(f"  💡 {rec}")
    
    print("\n✅ Validation completed!")

if __name__ == "__main__":
    main()
