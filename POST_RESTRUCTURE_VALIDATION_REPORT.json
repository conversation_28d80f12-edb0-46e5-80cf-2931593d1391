{"timestamp": "2025-05-28T20:23:08.686492", "validation_summary": {"import_tests": {"total": 3, "passed": 3, "failed": 0}, "requirements": {"total_files": 4, "existing": 4, "missing": 0}, "tests": {"tests_consolidated": 42, "tests_original": 54, "src_tests": 3}, "documentation": {"docs_consolidated": 72, "docs_original": 18}}, "detailed_results": {"import_tests": [{"test": "Main web search agent", "status": "SUCCESS", "details": "Successfully imported WebSearchAgentLocalMerged"}, {"test": "Main adaptive crawler", "status": "SUCCESS", "details": "Successfully imported AdaptiveCrawlerConsolidatedMerged"}, {"test": "Vietnamese search methods", "status": "SUCCESS", "details": "Successfully imported search_coccoc"}], "requirements_check": [{"file": "config_consolidated/requirements.txt", "status": "EXISTS", "packages": 11, "sample_packages": ["requests>=2.25.0", "beautifulsoup4>=4.9.3", "urllib3>=1.26.0", "lxml>=4.6.3", "langdetect>=1.0.9"]}, {"file": "config_consolidated/requirements-core.txt", "status": "EXISTS", "packages": 11, "sample_packages": ["requests>=2.28.0", "beautifulsoup4>=4.11.0", "lxml>=4.9.0", "urllib3>=1.26.0", "tldextract>=3.1.0"]}, {"file": "config_consolidated/requirements-credibility.txt", "status": "EXISTS", "packages": 5, "sample_packages": ["nltk>=3.7", "textblob>=0.17.1", "scikit-learn>=1.1.1", "spacy>=3.4.0", "regex>=2022.6.2"]}, {"file": "config_consolidated/requirements-llm.txt", "status": "EXISTS", "packages": 6, "sample_packages": ["transformers>=4.30.0", "torch>=2.0.0", "tokenizers>=0.13.3", "datasets>=2.12.0", "openai>=0.27.0"]}], "test_validation": {"tests_consolidated": {"count": 42, "files": ["test_extract_videos.py", "test_comprehensive_web_search_agent.py", "test_websearch_real_functionality.py", "test_extract_audio.py", "test_consolidated_simple.py", "test_extract_images.py", "test_file_processor.py", "test_performance_benchmark.py", "test_vietnamese_utils.py", "test_final_comprehensive_integration.py"]}, "tests": {"count": 54, "files": ["test_simple_language_detector.py", "test_captcha_handler.py", "test_vietnamese_diacritics.py", "test_optimized_crawlee.py", "test_vietnamese_processing.py", "test_factcheckorg_api_client.py", "test_credibility_models.py", "test_clickbait_detector.py", "test_feedback_collection.py", "test_query_analysis.py"]}, "src_tests": {"count": 3, "files": ["test_vietnamese_search.py", "test_credibility_evaluator.py", "test_advanced_language_detector.py"]}}, "doc_structure": {"docs_consolidated": {"count": 72, "categories": {"README": 10, "TASK": 13, "ADAPTIVE_CRAWLER": 8, "WEBSEARCH": 7, "DESIGN": 8, "OTHER": 27}}, "docs": {"count": 18, "files": ["CREDIBILITY_EVALUATION.md", "VIETNAMESE_RANKING_README.md", "VIETNAMESE_DIACRITICS_README.md", "CACHE_SYSTEM_README.md", "FACTCHECKORG_API_INTEGRATION.md", "STEALTH_MODE_README.md", "SNOPES_API_INTEGRATION.md", "ADVANCED_CONTENT_EXTRACTION.md", "POLITIFACT_API_INTEGRATION.md", "credibility_guide.md", "SOURCE_CREDIBILITY_README.md", "credibility_evaluation.md", "DISINFORMATION_DETECTION.md", "stealth_mode_guide.md", "code_structure.md", "security-improvements.md", "CLICKBAIT_DETECTION.md", "VIETNAMESE_CREDIBILITY_README.md"]}}}, "recommendations": []}