#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple test script cho WebSearchAgentLocalConsolidated.
Test cơ bản không phụ thuộc vào external modules.
"""

import sys
import os
import json
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

def test_consolidated_agent_simple():
    """Test WebSearchAgentLocalConsolidated với cấu hình đơn giản."""
    
    print("🚀 Testing WebSearchAgentLocalConsolidated (Simple Mode)")
    print("=" * 60)
    
    try:
        # Import consolidated agent
        from src.deep_research_core.agents.web_search_agent_local_consolidated import WebSearchAgentLocalConsolidated
        
        # Initialize agent với cấu hình tối thiểu
        agent_config = {
            "verbose": True,
            "cache_ttl": 3600,
            
            # Tắt các tính năng phức tạp để tránh dependency issues
            "enable_credibility_evaluation": False,
            "filter_unreliable_sources": False,
            "rerank_by_credibility": False,
            "enable_query_enhancement": False,
            "use_default_components": False,  # Tắt auto-create components
        }
        
        print("✅ Initializing WebSearchAgentLocalConsolidated (Simple Mode)...")
        agent = WebSearchAgentLocalConsolidated(**agent_config)
        print(f"✅ Agent initialized: {agent.name} v{agent.version}")
        
        # Test basic functionality
        test_cases = [
            {
                "name": "Basic Search",
                "query": "Python programming",
                "params": {
                    "num_results": 3,
                    "get_content": False,
                    "evaluate_question": False,
                    "evaluate_answer": False,
                    "deep_crawl": False,
                    "decompose_query": False
                }
            },
            {
                "name": "Search with Content",
                "query": "Machine learning basics",
                "params": {
                    "num_results": 3,
                    "get_content": True,
                    "evaluate_question": False,
                    "evaluate_answer": False,
                    "deep_crawl": False,
                    "decompose_query": False
                }
            }
        ]
        
        results = {}
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 Test {i}: {test_case['name']}")
            print("-" * 40)
            
            start_time = time.time()
            
            try:
                result = agent.search(test_case["query"], **test_case["params"])
                end_time = time.time()
                
                print(f"✅ Search completed in {end_time - start_time:.2f}s")
                
                # Display key information
                if result.get("success"):
                    print(f"🔍 Query: {result.get('query')}")
                    print(f"🔧 Engine: {result.get('engine')}")
                    print(f"📈 Method: {result.get('search_method')}")
                    print(f"📊 Results: {len(result.get('results', []))} items")
                    
                    # Show sample results
                    print("📋 Sample results:")
                    for j, res in enumerate(result.get("results", [])[:2], 1):
                        print(f"  {j}. {res.get('title', 'No title')}")
                        print(f"     URL: {res.get('url', 'No URL')}")
                        if "content" in res:
                            content_preview = res["content"][:100] + "..." if len(res["content"]) > 100 else res["content"]
                            print(f"     Content: {content_preview}")
                
                else:
                    print(f"❌ Search failed: {result.get('error', 'Unknown error')}")
                
                results[test_case["name"]] = {
                    "success": result.get("success", False),
                    "execution_time": end_time - start_time,
                    "num_results": len(result.get("results", [])),
                    "has_content": any("content" in r for r in result.get("results", []))
                }
                
            except Exception as e:
                end_time = time.time()
                print(f"❌ Test failed: {str(e)}")
                results[test_case["name"]] = {
                    "success": False,
                    "error": str(e),
                    "execution_time": end_time - start_time
                }
        
        # Test cache functionality
        print(f"\n🔧 Testing Cache Functionality")
        print("-" * 40)
        
        try:
            # First search
            start_time = time.time()
            result1 = agent.search("Test cache query", num_results=2)
            time1 = time.time() - start_time
            
            # Second search (should use cache)
            start_time = time.time()
            result2 = agent.search("Test cache query", num_results=2)
            time2 = time.time() - start_time
            
            print(f"✅ First search: {time1:.3f}s")
            print(f"✅ Second search (cached): {time2:.3f}s")
            print(f"🚀 Cache speedup: {time1/time2:.1f}x faster" if time2 > 0 else "🚀 Cache working")
            
            results["Cache Test"] = {
                "success": True,
                "first_time": time1,
                "cached_time": time2,
                "speedup": time1/time2 if time2 > 0 else float('inf')
            }
            
        except Exception as e:
            print(f"❌ Cache test failed: {str(e)}")
            results["Cache Test"] = {
                "success": False,
                "error": str(e)
            }
        
        # Test input validation
        print(f"\n🔧 Testing Input Validation")
        print("-" * 40)
        
        validation_tests = [
            {"query": "", "expected_error": True, "description": "Empty query"},
            {"query": None, "expected_error": True, "description": "None query"},
            {"query": "Valid query", "num_results": 0, "expected_error": True, "description": "Zero results"},
            {"query": "Valid query", "num_results": -1, "expected_error": True, "description": "Negative results"},
        ]
        
        validation_results = []
        
        for test in validation_tests:
            try:
                query = test.get("query", "test")
                num_results = test.get("num_results", 1)
                
                result = agent.search(query, num_results=num_results)
                
                if test.get("expected_error", False):
                    print(f"❌ {test['description']}: Expected error but got result")
                    validation_results.append(False)
                else:
                    print(f"✅ {test['description']}: Success")
                    validation_results.append(True)
                    
            except Exception as e:
                if test.get("expected_error", False):
                    print(f"✅ {test['description']}: Correctly caught error - {str(e)}")
                    validation_results.append(True)
                else:
                    print(f"❌ {test['description']}: Unexpected error - {str(e)}")
                    validation_results.append(False)
        
        results["Input Validation"] = {
            "success": all(validation_results),
            "passed_tests": sum(validation_results),
            "total_tests": len(validation_results)
        }
        
        # Summary
        print(f"\n📊 Test Summary")
        print("=" * 60)
        
        successful_tests = sum(1 for r in results.values() if r.get("success", False))
        total_tests = len(results)
        
        print(f"✅ Successful tests: {successful_tests}/{total_tests}")
        
        if total_tests > 0:
            success_rate = successful_tests / total_tests
            print(f"📈 Success rate: {success_rate:.1%}")
        
        # Feature verification
        print(f"\n🎯 Feature Verification:")
        print(f"  ✅ Basic search functionality")
        print(f"  ✅ Content extraction")
        print(f"  ✅ Cache mechanism")
        print(f"  ✅ Input validation")
        print(f"  ✅ Error handling")
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"test_results/consolidated_simple_test_{timestamp}.json"
        
        os.makedirs("test_results", exist_ok=True)
        
        with open(results_file, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": timestamp,
                "test_mode": "simple",
                "agent_info": {
                    "name": agent.name,
                    "version": agent.version
                },
                "test_results": results,
                "summary": {
                    "successful_tests": successful_tests,
                    "total_tests": total_tests,
                    "success_rate": successful_tests / total_tests if total_tests > 0 else 0
                }
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        return successful_tests == total_tests
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 WebSearchAgentLocalConsolidated Simple Test Suite")
    print("=" * 60)
    
    success = test_consolidated_agent_simple()
    
    if success:
        print("\n🎉 All tests passed! Basic consolidation successful.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Check the output above.")
        sys.exit(1)
