#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test User Agent Manager Integration
"""

import sys
import os

# Add paths
sys.path.append('.')
sys.path.append('src')

def test_user_agent_manager():
    """Test User Agent Manager integration."""
    try:
        print("Testing User Agent Manager integration...")
        
        # Import the crawler
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged
        print("✅ Import successful")
        
        # Create crawler with User Agent Manager
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_user_agent_manager=True,
            user_agent_rotation_enabled=True,
            user_agent_rotation_interval=5,
            user_agent_device_type="desktop"
        )
        print("✅ Crawler created with User Agent Manager")
        
        # Test User Agent Manager status
        status = crawler.get_user_agent_status()
        print(f"User Agent Manager status: {status}")
        
        # Test getting user agents
        random_ua = crawler.get_random_user_agent()
        print(f"Random user agent: {random_ua[:50]}...")
        
        next_ua = crawler.get_next_user_agent()
        print(f"Next user agent: {next_ua[:50]}...")
        
        # Test user agent count
        count = crawler.get_user_agents_count()
        print(f"User agents count: {count}")
        
        # Test adding user agent
        test_ua = "Mozilla/5.0 (Test Browser) Test/1.0"
        add_result = crawler.add_user_agent(test_ua, "desktop")
        print(f"Add user agent result: {add_result}")
        
        # Test rotation control
        enable_result = crawler.enable_user_agent_rotation(10)
        print(f"Enable rotation result: {enable_result}")
        
        disable_result = crawler.disable_user_agent_rotation()
        print(f"Disable rotation result: {disable_result}")
        
        print("✅ All User Agent Manager tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ User Agent Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("USER AGENT MANAGER INTEGRATION TEST")
    print("=" * 60)
    
    success = test_user_agent_manager()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 USER AGENT MANAGER INTEGRATION SUCCESSFUL!")
        sys.exit(0)
    else:
        print("❌ USER AGENT MANAGER INTEGRATION FAILED!")
        sys.exit(1)
