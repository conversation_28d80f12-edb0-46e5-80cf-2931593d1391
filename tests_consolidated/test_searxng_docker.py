#!/usr/bin/env python3
"""
Ví dụ sử dụng SearXNG Docker với WebSearchAgent.
"""

import sys
import json
from deep_research_core.agents.searxng_search import search_searxng, search_with_fallback
from deep_research_core.agents.web_search_agent_local import WebSearchAgentLocal

def test_direct_searxng_api():
    """Kiểm tra trực tiếp SearXNG API."""
    print("=== Kiểm tra trực tiếp SearXNG API ===")
    
    results = search_with_fallback(
        query="Python programming",
        num_results=5,
        language="en",
        max_retries=3,
        prioritize_local=True  # Ưu tiên sử dụng SearXNG local
    )
    
    print(f"Thành công: {results.get('success')}")
    print(f"Số kết quả: {len(results.get('results', []))}")
    
    # In kết quả đầu tiên
    if results.get('results'):
        first_result = results['results'][0]
        print(f"\nKết quả đầu tiên:")
        print(f"Tiêu đề: {first_result.get('title')}")
        print(f"URL: {first_result.get('url')}")
        print(f"Nội dung: {first_result.get('content')[:100]}...")

def test_web_search_agent_local():
    """Kiểm tra WebSearchAgentLocal với SearXNG Docker."""
    print("\n=== Kiểm tra WebSearchAgentLocal với SearXNG Docker ===")
    
    agent = WebSearchAgentLocal(
        search_method="searxng",
        api_search_config={
            "engine": "searx",
            "searx_url": "http://localhost:8080",  # URL của SearXNG local
            "language": "auto"
        },
        verbose=True
    )
    
    results = agent.search("Python programming", num_results=5)
    
    print(f"Thành công: {results.get('success')}")
    print(f"Số kết quả: {len(results.get('results', []))}")
    
    # In kết quả đầu tiên
    if results.get('results'):
        first_result = results['results'][0]
        print(f"\nKết quả đầu tiên:")
        print(f"Tiêu đề: {first_result.get('title')}")
        print(f"URL: {first_result.get('url')}")
        print(f"Nội dung: {first_result.get('content')[:100]}...")

def test_vietnamese_search():
    """Kiểm tra tìm kiếm tiếng Việt với SearXNG Docker."""
    print("\n=== Kiểm tra tìm kiếm tiếng Việt với SearXNG Docker ===")
    
    agent = WebSearchAgentLocal(
        search_method="searxng",
        api_search_config={
            "engine": "searx",
            "searx_url": "http://localhost:8080",
            "language": "vi"
        },
        verbose=True
    )
    
    results = agent.search("Lập trình Python cơ bản", num_results=5)
    
    print(f"Thành công: {results.get('success')}")
    print(f"Số kết quả: {len(results.get('results', []))}")
    
    # In kết quả đầu tiên
    if results.get('results'):
        first_result = results['results'][0]
        print(f"\nKết quả đầu tiên:")
        print(f"Tiêu đề: {first_result.get('title')}")
        print(f"URL: {first_result.get('url')}")
        print(f"Nội dung: {first_result.get('content')[:100]}...")

def main():
    """Hàm chính."""
    print("Kiểm tra SearXNG Docker với WebSearchAgent\n")
    
    try:
        test_direct_searxng_api()
        test_web_search_agent_local()
        test_vietnamese_search()
    except Exception as e:
        print(f"Lỗi: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
