import unittest
from unittest.mock import patch, MagicMock
import requests
import json
import os
import sys
from bs4 import BeautifulSoup

# Thê<PERSON> thư mục gốc vào đường dẫn
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from src.deep_research_core.agents.adaptive_crawler_integration import AdaptiveCrawler
except ImportError:
    print("Không thể import AdaptiveCrawler. Đ<PERSON>m bảo đường dẫn đúng.")
    AdaptiveCrawler = None

@unittest.skipIf(AdaptiveCrawler is None, "AdaptiveCrawler không khả dụng")
class TestAdaptiveCrawler(unittest.TestCase):
    def setUp(self):
        self.crawler = AdaptiveCrawler(
            timeout=5,
            max_retries=1,
            delay=0.1
        )
        
        # HTML mẫu cho testing
        self.sample_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Trang Test</title>
            <meta name="description" content="Mô tả trang test">
            <meta name="keywords" content="test, crawler, python">
            <meta name="author" content="Test Author">
            <meta property="og:title" content="OG Title">
            <meta property="og:description" content="OG Description">
            <link rel="canonical" href="https://example.com/canonical">
        </head>
        <body>
            <main class="content">
                <h1>Tiêu đề chính</h1>
                <p>Đây là đoạn văn 1.</p>
                <p>Đây là đoạn văn 2 với nhiều nội dung hơn để đảm bảo nó đủ dài cho việc test.</p>
            </main>
            <div>
                <a href="https://example.com/page1">Link 1</a>
                <a href="/page2">Link 2</a>
                <a href="#section">Link Nội bộ</a>
                <a href="mailto:<EMAIL>">Email</a>
            </div>
        </body>
        </html>
        """
        
    @patch('requests.Session.get')
    def test_crawl_url(self, mock_get):
        # Thiết lập mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = self.sample_html
        mock_response.headers = {
            'Content-Type': 'text/html; charset=utf-8'
        }
        mock_get.return_value = mock_response
        
        # Thực hiện test
        result = self.crawler._crawl_url("https://example.com")
        
        # Kiểm tra kết quả
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["status_code"], 200)
        self.assertEqual(result["url"], "https://example.com")
        self.assertIn("html", result)
        self.assertIn("content", result)
        self.assertIn("title", result)
        self.assertEqual(result["title"], "Trang Test")
        
    def test_extract_content_from_html(self):
        content = self.crawler._extract_content_from_url("https://example.com", html=self.sample_html)
        
        self.assertEqual(content["title"], "Trang Test")
        self.assertEqual(content["description"], "Mô tả trang test")
        self.assertIn("Tiêu đề chính", content["content"])
        self.assertIn("Đây là đoạn văn 1", content["content"])
        
    def test_extract_links_from_html(self):
        links = self.crawler._extract_links_from_url("https://example.com", html=self.sample_html)
        
        self.assertEqual(len(links), 2)  # Chỉ 2 liên kết hợp lệ (không tính # và mailto:)
        self.assertEqual(links[0]["url"], "https://example.com/page1")
        self.assertEqual(links[0]["text"], "Link 1")
        self.assertEqual(links[1]["url"], "https://example.com/page2")
        self.assertEqual(links[1]["text"], "Link 2")
        
    def test_extract_metadata_from_html(self):
        soup = BeautifulSoup(self.sample_html, 'html.parser')
        metadata = self.crawler._extract_metadata_from_url("https://example.com", soup=soup)
        
        self.assertEqual(metadata["author"], "Test Author")
        self.assertEqual(metadata["keywords"], "test, crawler, python")
        self.assertEqual(metadata["og_title"], "OG Title")
        self.assertEqual(metadata["og_description"], "OG Description")
        self.assertEqual(metadata["canonical_url"], "https://example.com/canonical")
        
    @patch('requests.Session.get')
    def test_crawl_urls(self, mock_get):
        # Thiết lập mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = self.sample_html
        mock_response.headers = {
            'Content-Type': 'text/html; charset=utf-8'
        }
        mock_get.return_value = mock_response
        
        # Thực hiện test
        urls = ["https://example.com/1", "https://example.com/2"]
        results = self.crawler._crawl_urls(urls, max_workers=2)
        
        # Kiểm tra kết quả
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["status"], "success")
        self.assertEqual(results[1]["status"], "success")
        
    @patch.object(AdaptiveCrawler, '_crawl_url')
    def test_deep_crawl_with_adaptive_crawler(self, mock_crawl_url):
        # Thiết lập mock response cho URL đầu tiên
        mock_crawl_url.side_effect = [
            {
                "status": "success",
                "url": "https://example.com",
                "links": [
                    {"url": "https://example.com/page1", "text": "Page 1"},
                    {"url": "https://example.com/page2", "text": "Page 2"}
                ],
                "title": "Example Domain",
                "content": "Example content"
            },
            {
                "status": "success",
                "url": "https://example.com/page1",
                "links": [],
                "title": "Page 1",
                "content": "Page 1 content"
            },
            {
                "status": "success",
                "url": "https://example.com/page2",
                "links": [],
                "title": "Page 2",
                "content": "Page 2 content"
            }
        ]
        
        # Thực hiện test
        result = self.crawler._deep_crawl_with_adaptive_crawler(
            "https://example.com",
            max_depth=1,
            max_pages=3
        )
        
        # Kiểm tra kết quả
        self.assertEqual(result["start_url"], "https://example.com")
        self.assertEqual(len(result["pages"]), 3)
        self.assertEqual(result["stats"]["total_pages"], 3)
        
    def test_extract_domain(self):
        domain = self.crawler._extract_domain("https://example.com/path?query=123")
        self.assertEqual(domain, "example.com")
        
        domain = self.crawler._extract_domain("http://sub.example.com/")
        self.assertEqual(domain, "sub.example.com")
        
    @patch('urllib.robotparser.RobotFileParser')
    def test_is_allowed_by_robots(self, mock_robot_parser):
        # Thiết lập mock RobotFileParser
        mock_rp = MagicMock()
        mock_rp.can_fetch.return_value = False
        mock_robot_parser.return_value = mock_rp
        
        # Thiết lập crawler với respect_robots=True
        crawler = AdaptiveCrawler(respect_robots=True)
        
        # Kiểm tra khi URL không được phép bởi robots.txt
        is_allowed = crawler._is_allowed_by_robots("https://example.com/disallowed")
        self.assertFalse(is_allowed)
        
        # Thiết lập crawler với respect_robots=False
        crawler = AdaptiveCrawler(respect_robots=False)
        
        # Kiểm tra khi không quan tâm đến robots.txt
        is_allowed = crawler._is_allowed_by_robots("https://example.com/disallowed")
        self.assertTrue(is_allowed)

if __name__ == '__main__':
    unittest.main() 