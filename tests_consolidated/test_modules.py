#!/usr/bin/env python3
"""
Script kiểm tra các module đã triển khai.
"""

import os
import sys
import time

# Thêm thư mục gốc vào sys.path
sys.path.append('.')

# Import các module cần kiểm tra
try:
    print("Thử import structured_logging...")
    from src.deep_research_core.utils.structured_logging import get_logger, configure_logging
    
    print("Thử import advanced_monitoring...")
    from src.deep_research_core.utils.advanced_monitoring import (
        AdvancedMonitoring, 
        PerformanceMetrics,
        SystemMonitor
    )
    
    print("Thử import machine_learning_feedback...")
    from src.deep_research_core.utils.machine_learning_feedback import MLFeedbackLearner
    
    print("Tất cả các module đã import thành công!")
except ImportError as e:
    print(f"Lỗi import: {str(e)}")
    sys.exit(1)

# Kiểm tra structured_logging
print("\n=== Kiểm tra structured_logging ===")
logger = get_logger("test_script")
logger.info("Thử nghiệm logging")
logger.warning("Thử nghiệm warning")
logger.error("Thử nghiệm error")

# Kiểm tra PerformanceMetrics
print("\n=== Kiểm tra PerformanceMetrics ===")
metrics = PerformanceMetrics()

# Đo thời gian thực thi
start = time.time()
time.sleep(0.1)  # Giả lập một thao tác
duration = time.time() - start
metrics.record_execution_time("test_operation", duration)

# Ghi nhận cache access
metrics.record_cache_access("test_cache", True)  # Hit
metrics.record_cache_access("test_cache", False)  # Miss

# Lấy summary
summary = metrics.get_summary()
print(f"Execution time: {summary['execution_time']}")
print(f"Cache stats: {summary['cache']}")

# Kiểm tra MLFeedbackLearner (chỉ khởi tạo)
print("\n=== Kiểm tra MLFeedbackLearner ===")
temp_dir = "temp_test_dir"
os.makedirs(temp_dir, exist_ok=True)

try:
    learner = MLFeedbackLearner(
        model_dir=f"{temp_dir}/ml_models",
        feedback_data_path=f"{temp_dir}/feedback.json",
        min_samples_for_training=2,
        enable_auto_training=False
    )
    print("MLFeedbackLearner đã khởi tạo thành công!")
    
    # Thêm feedback thử nghiệm
    result = learner.add_feedback(
        query="test query",
        url="https://example.com",
        rating=5
    )
    print(f"Thêm feedback: {result}")
    
    # Lấy thống kê
    stats = learner.get_statistics()
    print(f"Stats: {stats}")
except Exception as e:
    print(f"Lỗi khi kiểm tra MLFeedbackLearner: {str(e)}")

# Kiểm tra SystemMonitor (chỉ trong thời gian ngắn)
print("\n=== Kiểm tra SystemMonitor ===")
try:
    monitor = SystemMonitor(check_interval=1)
    monitor.start_monitoring()
    print("Monitoring đã bắt đầu, chờ 2 giây...")
    time.sleep(2)
    
    # Lấy trạng thái hệ thống
    status = monitor.get_system_status()
    print(f"CPU: {status['current']['cpu']}%")
    print(f"Memory: {status['current']['memory']}%")
    
    # Dừng monitoring
    monitor.stop_monitoring()
    print("Monitoring đã dừng")
except Exception as e:
    print(f"Lỗi khi kiểm tra SystemMonitor: {str(e)}")

print("\nKết thúc kiểm tra, mọi thứ hoạt động tốt!") 