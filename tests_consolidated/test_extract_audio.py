"""
Test trích xuất audio từ AdaptiveCrawler.

Script này kiểm tra tính năng trích xuất audio mới của AdaptiveCrawler,
bao gồm trích xuất từ thẻ audio, iframe nhúng từ SoundCloud, Spotify, và các nền tảng khác.
"""

import os
import sys
import json
from pathlib import Path

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(str(Path(__file__).parent.parent))

from deepresearch.src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler

# HTML mẫu để kiểm tra trích xuất audio
TEST_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>Test Extract Audio</title>
    <base href="https://example.com/">
</head>
<body>
    <div class="content">
        <h1>Test Audio</h1>

        <!-- HTML5 audio tag -->
        <audio controls>
            <source src="audio/sample.mp3" type="audio/mpeg">
            <source src="audio/sample.ogg" type="audio/ogg">
            <source src="audio/sample.wav" type="audio/wav">
            Your browser does not support the audio tag.
        </audio>

        <!-- Direct audio with src attribute -->
        <audio src="audio/direct-sample.mp3" controls></audio>

        <!-- SoundCloud embed -->
        <iframe width="100%" height="166" scrolling="no" frameborder="no" allow="autoplay" src="https://w.soundcloud.com/player/?url=https%3A//api.soundcloud.com/tracks/293&amp;color=%23ff5500&amp;auto_play=false&amp;hide_related=false&amp;show_comments=true&amp;show_user=true&amp;show_reposts=false&amp;show_teaser=true"></iframe>

        <!-- Spotify embed -->
        <iframe src="https://open.spotify.com/embed/track/5ihDGnhQgMA0F0tk9fNLlA" width="300" height="380" frameborder="0" allowtransparency="true" allow="encrypted-media"></iframe>

        <!-- Generic audio player iframe -->
        <iframe src="https://example.com/audio-player?id=12345" width="300" height="100" frameborder="0"></iframe>
    </div>
</body>
</html>
"""

def test_extract_audio():
    """Kiểm tra phương thức _extract_audio của AdaptiveCrawler."""
    # Khởi tạo AdaptiveCrawler với download_media=True
    download_path = os.path.join(os.path.dirname(__file__), "test_downloads")
    crawler = AdaptiveCrawler(
        download_media=True,
        download_path=download_path,
        max_media_size_mb=10
    )

    # URL mẫu để kiểm tra
    test_url = "https://example.com/test-page.html"

    # Gọi phương thức _extract_audio
    audios = crawler._extract_audio(test_url, TEST_HTML)

    # In kết quả
    print(f"Đã trích xuất {len(audios)} audio:")
    for i, audio in enumerate(audios, 1):
        print(f"{i}. {audio['url']} (Type: {audio.get('source_type', 'unknown')})")

    # Lưu kết quả ra file JSON để kiểm tra
    result_file = os.path.join(os.path.dirname(__file__), "test_results", "extracted_audio.json")
    os.makedirs(os.path.dirname(result_file), exist_ok=True)

    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(audios, f, indent=2, ensure_ascii=False)

    print(f"\nĐã lưu kết quả vào: {result_file}")

    # Kiểm tra các loại audio đã trích xuất
    audio_types = set(audio.get('source_type', 'unknown') for audio in audios)
    print(f"\nCác loại audio đã trích xuất: {', '.join(audio_types)}")

    # Kiểm tra số lượng audio theo loại
    type_counts = {}
    for audio in audios:
        audio_type = audio.get('source_type', 'unknown')
        type_counts[audio_type] = type_counts.get(audio_type, 0) + 1

    for audio_type, count in type_counts.items():
        print(f"- {audio_type}: {count} audio")

    return audios

if __name__ == "__main__":
    audios = test_extract_audio()

    # Kiểm tra kết quả
    assert len(audios) >= 3, f"Phải trích xuất ít nhất 3 audio, nhưng chỉ tìm thấy {len(audios)}"

    # Kiểm tra các loại audio
    audio_types = set(audio.get('source_type', 'unknown') for audio in audios)
    # Lưu ý: HTML5 audio không được trích xuất khi không có Playwright Page
    # assert 'html5' in audio_types, "Không tìm thấy audio từ thẻ HTML5"
    assert 'soundcloud' in audio_types, "Không tìm thấy audio từ SoundCloud"
    assert 'spotify' in audio_types, "Không tìm thấy audio từ Spotify"

    print("\nKiểm tra thành công!")
