#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script cho WebSearchAgentLocalMerged
Kiểm tra các tính năng mới đã được thêm vào.
"""

import sys
import os
import json
from datetime import datetime

# Thêm đường dẫn để import module
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
    print("✓ Import WebSearchAgentLocalMerged thành công")
except ImportError as e:
    print(f"✗ Lỗi import WebSearchAgentLocalMerged: {e}")
    sys.exit(1)

def test_basic_functionality():
    """Test các chức năng cơ bản"""
    print("\n=== TEST CHỨC NĂNG CƠ BẢN ===")
    
    # Khởi tạo agent
    try:
        agent = WebSearchAgentLocalMerged(verbose=True)
        print("✓ Khởi tạo WebSearchAgentLocalMerged thành công")
    except Exception as e:
        print(f"✗ Lỗi khởi tạo: {e}")
        return False
    
    # Test search cơ bản
    try:
        query = "Python programming language"
        result = agent.search(query)
        print(f"✓ Search cơ bản thành công cho query: '{query}'")
        print(f"  - Số kết quả: {len(result.get('results', []))}")
        print(f"  - Có simple_answer: {'simple_answer' in result}")
    except Exception as e:
        print(f"✗ Lỗi search cơ bản: {e}")
        return False
    
    return True

def test_question_complexity_evaluation():
    """Test đánh giá độ phức tạp câu hỏi"""
    print("\n=== TEST ĐÁNH GIÁ ĐỘ PHỨC TẠP CÂU HỎI ===")
    
    agent = WebSearchAgentLocalMerged(verbose=True)
    
    test_queries = [
        "Python là gì?",
        "So sánh Python và Java trong phát triển web, phân tích ưu nhược điểm của từng ngôn ngữ",
        "How to learn programming?",
        "What are the advantages and disadvantages of machine learning in healthcare?"
    ]
    
    for query in test_queries:
        try:
            evaluation = agent.evaluate_question_complexity(query)
            print(f"✓ Query: '{query}'")
            print(f"  - Complexity level: {evaluation['complexity_level']}")
            print(f"  - Complexity score: {evaluation['complexity_score']:.2f}")
            print(f"  - Question type: {evaluation['question_type']}")
            print(f"  - Recommended strategy: {evaluation['recommended_strategy']['search_method']}")
        except Exception as e:
            print(f"✗ Lỗi đánh giá complexity cho '{query}': {e}")
            return False
    
    return True

def test_answer_quality_evaluation():
    """Test đánh giá chất lượng câu trả lời"""
    print("\n=== TEST ĐÁNH GIÁ CHẤT LƯỢNG CÂU TRẢ LỜI ===")
    
    agent = WebSearchAgentLocalMerged(verbose=True)
    
    test_cases = [
        {
            "query": "Python là gì?",
            "answer": "Python là một ngôn ngữ lập trình bậc cao, được thiết kế với triết lý đơn giản và dễ đọc. Python được sử dụng rộng rãi trong phát triển web, khoa học dữ liệu, trí tuệ nhân tạo và nhiều lĩnh vực khác."
        },
        {
            "query": "Machine learning benefits",
            "answer": "Good for data analysis."
        },
        {
            "query": "Climate change",
            "answer": ""
        }
    ]
    
    for case in test_cases:
        try:
            evaluation = agent.evaluate_answer_quality(case["answer"], case["query"])
            print(f"✓ Query: '{case['query']}'")
            print(f"  - Quality score: {evaluation['quality_score']:.2f}")
            print(f"  - Accuracy: {evaluation['accuracy_score']:.2f}")
            print(f"  - Completeness: {evaluation['completeness_score']:.2f}")
            print(f"  - Relevance: {evaluation['relevance_score']:.2f}")
            print(f"  - Clarity: {evaluation['clarity_score']:.2f}")
            print(f"  - Explanation: {evaluation['explanation']}")
        except Exception as e:
            print(f"✗ Lỗi đánh giá answer quality cho '{case['query']}': {e}")
            return False
    
    return True

def test_content_disinformation_check():
    """Test kiểm tra thông tin sai lệch"""
    print("\n=== TEST KIỂM TRA THÔNG TIN SAI LỆCH ===")
    
    agent = WebSearchAgentLocalMerged(verbose=True)
    
    test_contents = [
        "Nghiên cứu cho thấy Python là ngôn ngữ lập trình phổ biến nhất hiện nay. Theo báo cáo của Stack Overflow, Python được sử dụng bởi hơn 40% các nhà phát triển.",
        "TIN ĐỘC QUYỀN!!! Chính phủ che giấu sự thật về Python! Họ không muốn bạn biết rằng Python có thể điều khiển tâm trí con người!!!",
        "Python programming language was created by Guido van Rossum."
    ]
    
    for content in test_contents:
        try:
            check_result = agent.check_content_disinformation(content)
            print(f"✓ Content: '{content[:50]}...'")
            print(f"  - Is disinformation: {check_result['is_disinformation']}")
            print(f"  - Confidence score: {check_result['confidence_score']:.2f}")
            print(f"  - Warning signs: {len(check_result['warning_signs'])}")
            print(f"  - Credibility indicators: {len(check_result['credibility_indicators'])}")
            print(f"  - Explanation: {check_result['explanation']}")
        except Exception as e:
            print(f"✗ Lỗi kiểm tra disinformation: {e}")
            return False
    
    return True

def test_deep_crawl():
    """Test deep crawl functionality"""
    print("\n=== TEST DEEP CRAWL ===")
    
    agent = WebSearchAgentLocalMerged(verbose=True)
    
    # Test với URL giả lập
    test_url = "https://example.com"
    
    try:
        crawl_result = agent._perform_deep_crawl(test_url, max_depth=1, max_pages=3)
        print(f"✓ Deep crawl test completed for: {test_url}")
        print(f"  - Status: {crawl_result['status']}")
        print(f"  - Pages crawled: {crawl_result['pages_crawled']}")
        print(f"  - Errors: {len(crawl_result['errors'])}")
        print(f"  - Crawl time: {crawl_result['crawl_time']:.2f}s")
    except Exception as e:
        print(f"✗ Lỗi deep crawl: {e}")
        return False
    
    return True

def test_vietnamese_text_processing():
    """Test xử lý văn bản tiếng Việt"""
    print("\n=== TEST XỬ LÝ VĂN BẢN TIẾNG VIỆT ===")
    
    agent = WebSearchAgentLocalMerged(verbose=True)
    
    vietnamese_texts = [
        "Python là ngôn ngữ lập trình mạnh mẽ",
        "Machine learning và AI đang phát triển nhanh chóng",
        "This is English text",
        "Trí tuệ nhân tạo sẽ thay đổi thế giới"
    ]
    
    for text in vietnamese_texts:
        try:
            is_vietnamese = agent._is_vietnamese_text(text)
            print(f"✓ Text: '{text}'")
            print(f"  - Is Vietnamese: {is_vietnamese}")
        except Exception as e:
            print(f"✗ Lỗi xử lý Vietnamese text: {e}")
            return False
    
    return True

def save_test_results(results):
    """Lưu kết quả test vào file"""
    test_results = {
        "timestamp": datetime.now().isoformat(),
        "results": results,
        "summary": {
            "total_tests": len(results),
            "passed": sum(1 for r in results.values() if r),
            "failed": sum(1 for r in results.values() if not r)
        }
    }
    
    # Tạo thư mục test_results nếu chưa có
    os.makedirs("test_results", exist_ok=True)
    
    with open("test_results/web_search_agent_merged_test.json", "w", encoding="utf-8") as f:
        json.dump(test_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✓ Kết quả test đã được lưu vào test_results/web_search_agent_merged_test.json")

def main():
    """Chạy tất cả các test"""
    print("🚀 BẮT ĐẦU TEST WEBSEARCHAGENTLOCALMERGED")
    print("=" * 60)

    # Test nhanh một tính năng để xem output
    print("\n🔍 TEST NHANH - Đánh giá độ phức tạp câu hỏi:")
    try:
        agent = WebSearchAgentLocalMerged(verbose=False)
        result = agent.evaluate_question_complexity("So sánh Python và Java trong machine learning")
        print(f"✓ Complexity Level: {result['complexity_level']}")
        print(f"✓ Complexity Score: {result['complexity_score']:.2f}")
        print(f"✓ Question Type: {result['question_type']}")
        print(f"✓ Search Method: {result['recommended_strategy']['search_method']}")
    except Exception as e:
        print(f"✗ Lỗi: {e}")

    print("\n🔍 TEST NHANH - Đánh giá chất lượng câu trả lời:")
    try:
        answer = "Python và Java đều là ngôn ngữ lập trình mạnh mẽ. Python dễ học hơn và có cú pháp đơn giản. Java có hiệu suất cao hơn và được sử dụng rộng rãi trong doanh nghiệp."
        result = agent.evaluate_answer_quality(answer, "So sánh Python và Java")
        print(f"✓ Quality Score: {result['quality_score']:.2f}")
        print(f"✓ Accuracy: {result['accuracy_score']:.2f}")
        print(f"✓ Completeness: {result['completeness_score']:.2f}")
        print(f"✓ Relevance: {result['relevance_score']:.2f}")
        print(f"✓ Clarity: {result['clarity_score']:.2f}")
    except Exception as e:
        print(f"✗ Lỗi: {e}")
    
    test_functions = [
        ("Basic Functionality", test_basic_functionality),
        ("Question Complexity Evaluation", test_question_complexity_evaluation),
        ("Answer Quality Evaluation", test_answer_quality_evaluation),
        ("Content Disinformation Check", test_content_disinformation_check),
        ("Deep Crawl", test_deep_crawl),
        ("Vietnamese Text Processing", test_vietnamese_text_processing)
    ]
    
    results = {}
    
    for test_name, test_func in test_functions:
        print(f"\n🔍 Đang chạy test: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"📊 Kết quả: {status}")
        except Exception as e:
            results[test_name] = False
            print(f"💥 Lỗi không mong đợi: {e}")
            print(f"📊 Kết quả: ❌ FAILED")
    
    # Tổng kết
    print("\n" + "=" * 60)
    print("📋 TỔNG KẾT TEST")
    print("=" * 60)
    
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\n📊 Tổng cộng: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 TẤT CẢ TESTS ĐỀU PASSED!")
    else:
        print(f"⚠️  {total - passed} tests failed")
    
    # Lưu kết quả
    save_test_results(results)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
