#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script cho AdaptiveCrawlerConsolidated.
Test tất cả tính năng đã được consolidate.
"""

import sys
import os
import json
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

def test_adaptive_crawler_consolidated():
    """Test AdaptiveCrawlerConsolidated với tất cả tính năng."""
    
    print("🚀 Testing AdaptiveCrawlerConsolidated")
    print("=" * 60)
    
    try:
        # Import consolidated crawler
        from src.deep_research_core.agents.adaptive_crawler_consolidated import AdaptiveCrawlerConsolidated
        
        # Initialize crawler với tất cả tính năng
        crawler_config = {
            # Basic settings
            "use_playwright": False,  # Use requests for testing
            "max_depth": 1,
            "max_pages": 3,
            "timeout": 10,
            
            # User-Agent rotation (từ phiên bản 2)
            "rotate_user_agents": True,
            
            # Robots.txt handling (từ phiên bản 2)
            "respect_robots": True,
            "robots_cache_ttl": 3600,
            
            # Advanced features (từ phiên bản 1)
            "download_media": False,  # Tắt để test nhanh hơn
            "site_map_enabled": True,
            "extract_file_content": False,
            "enable_javascript": False,  # Tắt vì dùng requests
            "handle_infinite_scroll": False,
            "handle_ajax": False,
            "handle_pagination": False
        }
        
        print("✅ Initializing AdaptiveCrawlerConsolidated...")
        crawler = AdaptiveCrawlerConsolidated(**crawler_config)
        print(f"✅ Crawler initialized: {crawler.name} v{crawler.version}")
        
        # Test cases
        test_cases = [
            {
                "name": "Single URL Crawl",
                "method": "crawl",
                "args": ["https://httpbin.org/html"],
                "kwargs": {"max_depth": 1, "max_pages": 2}
            },
            {
                "name": "Multiple URL Crawl",
                "method": "crawl_multiple",
                "args": [["https://httpbin.org/html", "https://httpbin.org/json"]],
                "kwargs": {"max_depth": 1, "max_pages": 1}
            },
            {
                "name": "Website Crawl (Comprehensive)",
                "method": "crawl_website",
                "args": ["https://httpbin.org/html"],
                "kwargs": {"max_depth": 1, "max_pages": 2}
            }
        ]
        
        results = {}
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 Test {i}: {test_case['name']}")
            print("-" * 40)
            
            start_time = time.time()
            
            try:
                method = getattr(crawler, test_case["method"])
                result = method(*test_case["args"], **test_case["kwargs"])
                end_time = time.time()
                
                print(f"✅ {test_case['method']} completed in {end_time - start_time:.2f}s")
                
                # Display key information
                if result.get("success"):
                    print(f"🔍 Method: {test_case['method']}")
                    
                    if "total_pages" in result:
                        print(f"📊 Total pages: {result.get('total_pages', 0)}")
                        print(f"🔗 Total links: {result.get('total_links', 0)}")
                        print(f"🖼️  Total media files: {result.get('total_media_files', 0)}")
                    
                    if "statistics" in result:
                        stats = result["statistics"]
                        print(f"📊 Statistics:")
                        print(f"   - Total pages: {stats.get('total_pages', 0)}")
                        print(f"   - Successful: {stats.get('successful_pages', 0)}")
                        print(f"   - Failed: {stats.get('failed_pages', 0)}")
                        print(f"   - Links: {stats.get('total_links', 0)}")
                        print(f"   - Media files: {stats.get('total_media_files', 0)}")
                    
                    if "successful_crawls" in result:
                        print(f"✅ Successful crawls: {result.get('successful_crawls', 0)}")
                        print(f"❌ Failed crawls: {result.get('failed_crawls', 0)}")
                    
                    # Show sample pages
                    pages = result.get("pages", [])
                    if pages:
                        print("📋 Sample pages:")
                        for j, page in enumerate(pages[:2], 1):
                            print(f"  {j}. {page.get('url', 'No URL')}")
                            print(f"     Title: {page.get('content', {}).get('title', 'No title')}")
                            print(f"     Success: {'✅' if page.get('success') else '❌'}")
                            if not page.get('success'):
                                print(f"     Error: {page.get('error', 'Unknown error')}")
                
                else:
                    print(f"❌ {test_case['method']} failed: {result.get('error', 'Unknown error')}")
                
                results[test_case["name"]] = {
                    "success": result.get("success", False),
                    "execution_time": end_time - start_time,
                    "method": test_case["method"],
                    "total_pages": result.get("total_pages", 0),
                    "total_links": result.get("total_links", 0),
                    "total_media_files": result.get("total_media_files", 0),
                    "has_statistics": "statistics" in result,
                    "has_site_map": "site_map" in result or bool(crawler.get_site_map())
                }
                
            except Exception as e:
                end_time = time.time()
                print(f"❌ Test failed: {str(e)}")
                results[test_case["name"]] = {
                    "success": False,
                    "error": str(e),
                    "execution_time": end_time - start_time,
                    "method": test_case["method"]
                }
        
        # Test additional features
        print(f"\n🔧 Testing Additional Features")
        print("-" * 40)
        
        # Test User-Agent rotation
        try:
            ua1 = crawler._get_user_agent()
            ua2 = crawler._get_user_agent()
            print(f"✅ User-Agent rotation: {'Working' if ua1 != ua2 or len(crawler.user_agents) == 1 else 'Not working'}")
        except Exception as e:
            print(f"⚠️  User-Agent rotation test failed: {str(e)}")
        
        # Test robots.txt checking
        try:
            can_fetch = crawler._can_fetch("https://httpbin.org/robots.txt")
            print(f"✅ Robots.txt check: {'Allowed' if can_fetch else 'Blocked'}")
        except Exception as e:
            print(f"⚠️  Robots.txt check failed: {str(e)}")
        
        # Test site map
        try:
            site_map = crawler.get_site_map()
            print(f"✅ Site map: {len(site_map)} entries")
            if site_map:
                sample_url = list(site_map.keys())[0]
                print(f"    Sample: {sample_url}")
        except Exception as e:
            print(f"⚠️  Site map test failed: {str(e)}")
        
        # Test cache clearing
        try:
            crawler.clear_cache()
            print(f"✅ Cache clearing: Success")
        except Exception as e:
            print(f"⚠️  Cache clearing failed: {str(e)}")
        
        # Summary
        print(f"\n📊 Test Summary")
        print("=" * 60)
        
        successful_tests = sum(1 for r in results.values() if r.get("success", False))
        total_tests = len(results)
        
        print(f"✅ Successful tests: {successful_tests}/{total_tests}")
        print(f"⏱️  Average execution time: {sum(r.get('execution_time', 0) for r in results.values()) / len(results):.2f}s")
        
        # Feature coverage
        features_tested = {
            "Single URL crawling": any(r.get("method") == "crawl" for r in results.values()),
            "Multiple URL crawling": any(r.get("method") == "crawl_multiple" for r in results.values()),
            "Website crawling": any(r.get("method") == "crawl_website" for r in results.values()),
            "Site map generation": any(r.get("has_site_map", False) for r in results.values()),
            "Statistics collection": any(r.get("has_statistics", False) for r in results.values())
        }
        
        print(f"\n🎯 Feature Coverage:")
        for feature, tested in features_tested.items():
            status = "✅" if tested else "❌"
            print(f"  {status} {feature}")
        
        # Performance analysis
        print(f"\n📈 Performance Analysis:")
        total_pages_crawled = sum(r.get("total_pages", 0) for r in results.values())
        total_links_found = sum(r.get("total_links", 0) for r in results.values())
        total_media_found = sum(r.get("total_media_files", 0) for r in results.values())
        
        print(f"  📊 Total pages crawled: {total_pages_crawled}")
        print(f"  🔗 Total links found: {total_links_found}")
        print(f"  🖼️  Total media files found: {total_media_found}")
        
        if total_pages_crawled > 0:
            avg_links_per_page = total_links_found / total_pages_crawled
            avg_media_per_page = total_media_found / total_pages_crawled
            print(f"  📈 Average links per page: {avg_links_per_page:.1f}")
            print(f"  📈 Average media files per page: {avg_media_per_page:.1f}")
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"test_results/adaptive_crawler_consolidated_test_{timestamp}.json"
        
        os.makedirs("test_results", exist_ok=True)
        
        with open(results_file, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": timestamp,
                "crawler_info": {
                    "name": crawler.name,
                    "version": crawler.version
                },
                "test_results": results,
                "feature_coverage": features_tested,
                "performance": {
                    "total_pages_crawled": total_pages_crawled,
                    "total_links_found": total_links_found,
                    "total_media_found": total_media_found
                },
                "summary": {
                    "successful_tests": successful_tests,
                    "total_tests": total_tests,
                    "success_rate": successful_tests / total_tests if total_tests > 0 else 0
                }
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        return successful_tests == total_tests
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 AdaptiveCrawlerConsolidated Test Suite")
    print("=" * 60)
    
    success = test_adaptive_crawler_consolidated()
    
    if success:
        print("\n🎉 All tests passed! Crawler consolidation successful.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Check the output above.")
        sys.exit(1)
