#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON><PERSON> tra cơ bản cho WebSearchAgentLocalMerged.
"""

import sys
import os
import unittest

# Thê<PERSON> thư mục gốc vào đường dẫn để có thể import module
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
except ImportError as e:
    print(f"Không thể import WebSearchAgentLocalMerged: {e}")
    sys.exit(1)

class TestWebSearchAgentLocalMerged(unittest.TestCase):
    """
    Kiểm tra cơ bản cho WebSearchAgentLocalMerged.
    """
    
    def setUp(self):
        """Khởi tạo agent trước mỗi test."""
        self.agent = WebSearchAgentLocalMerged()
    
    def test_initialization(self):
        """<PERSON><PERSON><PERSON> tra khởi tạo thành công."""
        self.assertIsNotNone(self.agent)
        self.assertTrue(hasattr(self.agent, 'data_dir'))
        self.assertTrue(hasattr(self.agent, 'enable_credibility_evaluation'))
        self.assertTrue(hasattr(self.agent, 'cache_dir'))
    
    def test_search_method_exists(self):
        """Kiểm tra phương thức search tồn tại."""
        self.assertTrue(hasattr(self.agent, 'search'))
        self.assertTrue(callable(getattr(self.agent, 'search')))
    
    def test_vietnamese_methods_exist(self):
        """Kiểm tra các phương thức xử lý tiếng Việt tồn tại."""
        self.assertTrue(hasattr(self.agent, '_fix_vietnamese_encoding'))
        self.assertTrue(hasattr(self.agent, '_is_vietnamese_text'))
        self.assertTrue(callable(getattr(self.agent, '_fix_vietnamese_encoding')))
        self.assertTrue(callable(getattr(self.agent, '_is_vietnamese_text')))
    
    def test_fix_vietnamese_encoding(self):
        """Kiểm tra phương thức sửa lỗi encoding tiếng Việt."""
        # Kiểm tra với chuỗi rỗng
        self.assertEqual(self.agent._fix_vietnamese_encoding(""), "")
        
        # Kiểm tra với chuỗi không lỗi
        text = "Xin chào Việt Nam"
        self.assertEqual(self.agent._fix_vietnamese_encoding(text), text)
        
        # Kiểm tra với HTML entities
        text_with_entities = "Xin ch&agrave;o Vi&ecirc;t Nam"
        self.assertEqual(self.agent._fix_vietnamese_encoding(text_with_entities).replace("&agrave;", "à").replace("&ecirc;", "ê"), 
                         text_with_entities.replace("&agrave;", "à").replace("&ecirc;", "ê"))
    
    def test_is_vietnamese_text(self):
        """Kiểm tra phương thức nhận diện văn bản tiếng Việt."""
        # Kiểm tra với chuỗi rỗng
        self.assertFalse(self.agent._is_vietnamese_text(""))
        
        # Kiểm tra với chuỗi tiếng Việt
        self.assertTrue(self.agent._is_vietnamese_text("Xin chào Việt Nam, đây là một đoạn văn bản tiếng Việt với các dấu như: á, à, ả, ã, ạ"))
        
        # Kiểm tra với chuỗi tiếng Anh
        self.assertFalse(self.agent._is_vietnamese_text("Hello, this is an English text without any Vietnamese characters"))
        
        # Kiểm tra với chuỗi ngắn
        self.assertFalse(self.agent._is_vietnamese_text("Xin chào"))

if __name__ == '__main__':
    unittest.main() 