#!/usr/bin/env python3
"""
Comprehensive test suite cho WebSearchAgentLocalMerged.
Tests tất cả 22 methods mới và 12 advanced features.
"""

import sys
import os
import json
import time
import unittest
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
    print("✅ Import WebSearchAgentLocalMerged thành công")
except ImportError as e:
    print(f"❌ Lỗi import: {e}")
    sys.exit(1)

class TestWebSearchAgentLocalMergedComprehensive(unittest.TestCase):
    """Comprehensive test suite cho WebSearchAgentLocalMerged."""
    
    @classmethod
    def setUpClass(cls):
        """Setup cho toàn bộ test class."""
        cls.test_results = {
            'timestamp': datetime.now().isoformat(),
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': []
        }
        
    def setUp(self):
        """Setup trước mỗi test."""
        self.agent = WebSearchAgentLocalMerged(verbose=False)
        self.test_query = "Python programming tutorial"
        self.test_url = "https://docs.python.org/3/"
        
    def tearDown(self):
        """Cleanup sau mỗi test."""
        if hasattr(self.agent, 'cleanup_resources'):
            self.agent.cleanup_resources()
    
    def record_test_result(self, test_name, passed, details=""):
        """Ghi lại kết quả test."""
        self.__class__.test_results['total_tests'] += 1
        if passed:
            self.__class__.test_results['passed_tests'] += 1
        else:
            self.__class__.test_results['failed_tests'] += 1
        
        self.__class__.test_results['test_details'].append({
            'test_name': test_name,
            'passed': passed,
            'details': details
        })

    # ========== BASIC FUNCTIONALITY TESTS ==========
    
    def test_01_initialization(self):
        """Test khởi tạo agent."""
        try:
            agent = WebSearchAgentLocalMerged()
            self.assertIsNotNone(agent)
            self.assertTrue(hasattr(agent, 'search'))
            self.record_test_result('initialization', True, 'Agent khởi tạo thành công')
        except Exception as e:
            self.record_test_result('initialization', False, str(e))
            self.fail(f"Khởi tạo thất bại: {e}")
    
    def test_02_basic_search(self):
        """Test tìm kiếm cơ bản."""
        try:
            result = self.agent.search(
                query=self.test_query,
                num_results=3,
                get_content=False,
                evaluate_question=False,
                evaluate_answer=False
            )
            
            self.assertIsInstance(result, dict)
            self.assertIn('results', result)
            self.assertIn('success', result)
            self.assertTrue(result['success'])
            
            # Kiểm tra không phải mock data
            results = result['results']
            if results:
                first_result = results[0]
                self.assertNotIn('example.com', first_result.get('url', ''))
                
            self.record_test_result('basic_search', True, f"Tìm thấy {len(results)} kết quả")
        except Exception as e:
            self.record_test_result('basic_search', False, str(e))
            self.fail(f"Basic search thất bại: {e}")

    # ========== REAL SEARCH FUNCTIONALITY TESTS ==========
    
    def test_03_searxng_priority(self):
        """Test SearXNG local priority."""
        try:
            # Test health check method
            if hasattr(self.agent, '_check_searxng_health'):
                local_healthy = self.agent._check_searxng_health("http://localhost:8080")
                self.record_test_result('searxng_health_check', True, f"Local SearXNG: {local_healthy}")
            
            # Test SearXNG search method
            if hasattr(self.agent, '_search_with_searxng'):
                searxng_results = self.agent._search_with_searxng(self.test_query, 3)
                self.assertIsInstance(searxng_results, list)
                self.record_test_result('searxng_search', True, f"SearXNG: {len(searxng_results)} kết quả")
            
        except Exception as e:
            self.record_test_result('searxng_priority', False, str(e))
    
    def test_04_multi_engine_search(self):
        """Test multi-engine search functionality."""
        try:
            # Test DuckDuckGo
            if hasattr(self.agent, '_search_with_duckduckgo'):
                ddg_results = self.agent._search_with_duckduckgo(self.test_query, 3)
                self.assertIsInstance(ddg_results, list)
                self.record_test_result('duckduckgo_search', True, f"DuckDuckGo: {len(ddg_results)} kết quả")
            
            # Test Bing (nếu có API key)
            if hasattr(self.agent, '_search_with_bing'):
                bing_results = self.agent._search_with_bing(self.test_query, 3)
                self.assertIsInstance(bing_results, list)
                self.record_test_result('bing_search', True, f"Bing: {len(bing_results)} kết quả")
            
            # Test Google (nếu có API key)
            if hasattr(self.agent, '_search_with_google'):
                google_results = self.agent._search_with_google(self.test_query, 3)
                self.assertIsInstance(google_results, list)
                self.record_test_result('google_search', True, f"Google: {len(google_results)} kết quả")
                
        except Exception as e:
            self.record_test_result('multi_engine_search', False, str(e))

    # ========== ADVANCED FEATURES TESTS ==========
    
    def test_05_smart_caching(self):
        """Test smart caching functionality."""
        try:
            # Test cache methods
            cache_methods = [
                '_get_cache_key', '_get_from_cache', '_save_to_cache',
                '_determine_cache_ttl', 'get_cache_stats', 'clear_cache'
            ]
            
            for method in cache_methods:
                if hasattr(self.agent, method):
                    self.record_test_result(f'cache_method_{method}', True, f"Method {method} tồn tại")
                else:
                    self.record_test_result(f'cache_method_{method}', False, f"Method {method} không tồn tại")
            
            # Test cache functionality
            if hasattr(self.agent, 'get_cache_stats'):
                cache_stats = self.agent.get_cache_stats()
                self.assertIsInstance(cache_stats, dict)
                self.record_test_result('cache_stats', True, f"Cache stats: {cache_stats}")
                
        except Exception as e:
            self.record_test_result('smart_caching', False, str(e))
    
    def test_06_query_optimization(self):
        """Test query optimization features."""
        try:
            optimization_methods = [
                'optimize_query', 'generate_alternative_queries', 'detect_query_intent'
            ]
            
            for method in optimization_methods:
                if hasattr(self.agent, method):
                    try:
                        if method == 'optimize_query':
                            result = getattr(self.agent, method)(self.test_query)
                        elif method == 'generate_alternative_queries':
                            result = getattr(self.agent, method)(self.test_query, 3)
                        elif method == 'detect_query_intent':
                            result = getattr(self.agent, method)(self.test_query)
                        
                        self.record_test_result(f'query_opt_{method}', True, f"Method {method} hoạt động")
                    except Exception as e:
                        self.record_test_result(f'query_opt_{method}', False, str(e))
                else:
                    self.record_test_result(f'query_opt_{method}', False, f"Method {method} không tồn tại")
                    
        except Exception as e:
            self.record_test_result('query_optimization', False, str(e))
    
    def test_07_multilanguage_processing(self):
        """Test multi-language processing."""
        try:
            ml_methods = [
                'detect_content_language', 'normalize_content_language', 'extract_keywords_multilingual'
            ]
            
            test_content = "Python là một ngôn ngữ lập trình mạnh mẽ và dễ học."
            
            for method in ml_methods:
                if hasattr(self.agent, method):
                    try:
                        if method == 'detect_content_language':
                            result = getattr(self.agent, method)(test_content)
                        elif method == 'normalize_content_language':
                            result = getattr(self.agent, method)(test_content, 'vi')
                        elif method == 'extract_keywords_multilingual':
                            result = getattr(self.agent, method)(test_content, 'vi')
                        
                        self.record_test_result(f'ml_{method}', True, f"Method {method} hoạt động")
                    except Exception as e:
                        self.record_test_result(f'ml_{method}', False, str(e))
                else:
                    self.record_test_result(f'ml_{method}', False, f"Method {method} không tồn tại")
                    
        except Exception as e:
            self.record_test_result('multilanguage_processing', False, str(e))

    def test_08_advanced_crawling(self):
        """Test advanced crawling features."""
        try:
            crawling_methods = [
                'crawl_with_javascript_support', 'crawl_spa_website', 
                'crawl_with_infinite_scroll', 'crawl_with_form_interaction',
                'crawl_with_pagination'
            ]
            
            for method in crawling_methods:
                if hasattr(self.agent, method):
                    self.record_test_result(f'crawl_{method}', True, f"Method {method} tồn tại")
                else:
                    self.record_test_result(f'crawl_{method}', False, f"Method {method} không tồn tại")
                    
        except Exception as e:
            self.record_test_result('advanced_crawling', False, str(e))

    def test_09_performance_optimization(self):
        """Test performance optimization features."""
        try:
            perf_methods = [
                'get_performance_stats', 'optimize_performance', 
                'batch_process_urls', 'adaptive_timeout'
            ]
            
            for method in perf_methods:
                if hasattr(self.agent, method):
                    try:
                        if method == 'get_performance_stats':
                            result = getattr(self.agent, method)()
                            self.assertIsInstance(result, dict)
                        elif method == 'optimize_performance':
                            getattr(self.agent, method)()
                        elif method == 'batch_process_urls':
                            result = getattr(self.agent, method)([self.test_url], batch_size=1)
                            self.assertIsInstance(result, list)
                        elif method == 'adaptive_timeout':
                            result = getattr(self.agent, method)(self.test_url)
                            self.assertIsInstance(result, int)
                        
                        self.record_test_result(f'perf_{method}', True, f"Method {method} hoạt động")
                    except Exception as e:
                        self.record_test_result(f'perf_{method}', False, str(e))
                else:
                    self.record_test_result(f'perf_{method}', False, f"Method {method} không tồn tại")
                    
        except Exception as e:
            self.record_test_result('performance_optimization', False, str(e))

    def test_10_resource_management(self):
        """Test resource management."""
        try:
            if hasattr(self.agent, 'cleanup_resources'):
                self.agent.cleanup_resources()
                self.record_test_result('resource_cleanup', True, "Cleanup resources thành công")
            else:
                self.record_test_result('resource_cleanup', False, "Method cleanup_resources không tồn tại")
                
        except Exception as e:
            self.record_test_result('resource_management', False, str(e))

def run_comprehensive_tests():
    """Chạy comprehensive test suite."""
    print("🚀 COMPREHENSIVE TEST SUITE FOR WebSearchAgentLocalMerged")
    print("=" * 70)
    
    # Tạo test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestWebSearchAgentLocalMergedComprehensive)
    
    # Chạy tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Lưu kết quả chi tiết
    test_results = TestWebSearchAgentLocalMergedComprehensive.test_results
    
    # Tính toán thống kê
    total = test_results['total_tests']
    passed = test_results['passed_tests']
    failed = test_results['failed_tests']
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"Total tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success rate: {success_rate:.1f}%")
    
    # Lưu kết quả vào file
    output_file = f"test_results/comprehensive_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    os.makedirs('test_results', exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(test_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Kết quả chi tiết đã lưu: {output_file}")
    
    # Kết luận
    if success_rate >= 80:
        print("\n🎉 COMPREHENSIVE TESTS PASSED!")
        print("✅ WebSearchAgentLocalMerged sẵn sàng cho production!")
        return True
    else:
        print("\n❌ SOME TESTS FAILED")
        print("⚠️  Cần sửa các vấn đề trước khi production")
        return False

if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
