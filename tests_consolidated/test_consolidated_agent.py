#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script cho WebSearchAgentLocalConsolidated.
<PERSON><PERSON>m tra tất cả tính năng đã được merge.
"""

import sys
import os
import json
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

def test_consolidated_agent():
    """Test WebSearchAgentLocalConsolidated với tất cả tính năng."""
    
    print("🚀 Testing WebSearchAgentLocalConsolidated")
    print("=" * 60)
    
    try:
        # Import consolidated agent
        from src.deep_research_core.agents.web_search_agent_local_consolidated import WebSearchAgentLocalConsolidated
        
        # Initialize agent với tất cả tính năng
        agent_config = {
            "verbose": True,
            "cache_ttl": 3600,
            
            # Credibility evaluation flags (từ phiên bản 1)
            "enable_credibility_evaluation": True,
            "filter_unreliable_sources": False,  # Tắ<PERSON> để test tất cả results
            "rerank_by_credibility": True,
            "min_credibility_score": 0.3,
            "enable_query_enhancement": True,
            "data_dir": "data",
            
            # LLM integration (từ phiên bản 2)
            "use_default_components": True,
            
            # Advanced features (từ phiên bản 3)
            "captcha_handler_config": {
                "auto_solve": False,
                "use_selenium": False,
                "max_retries": 3,
                "retry_delay": 5
            },
            "question_evaluator_config": {
                "complexity_threshold_high": 0.7,
                "complexity_threshold_medium": 0.4,
                "use_domain_knowledge": True
            },
            "answer_evaluator_config": {
                "quality_threshold_high": 0.7,
                "quality_threshold_medium": 0.4,
                "use_model_evaluation": False
            },
            "query_decomposer_config": {
                "provider": "openrouter",
                "temperature": 0.3,
                "max_tokens": 1000,
                "language": "auto",
                "use_cache": True,
                "cache_size": 100,
                "use_mock_for_testing": True
            }
        }
        
        print("✅ Initializing WebSearchAgentLocalConsolidated...")
        agent = WebSearchAgentLocalConsolidated(**agent_config)
        print(f"✅ Agent initialized: {agent.name} v{agent.version}")
        
        # Test cases
        test_cases = [
            {
                "name": "Simple Search",
                "query": "Python programming",
                "params": {
                    "num_results": 5,
                    "get_content": False,
                    "evaluate_question": False,
                    "evaluate_answer": False
                }
            },
            {
                "name": "Complex Search with Question Evaluation",
                "query": "What are the implications of artificial intelligence on society and economy?",
                "params": {
                    "num_results": 5,
                    "get_content": True,
                    "evaluate_question": True,
                    "evaluate_answer": False,
                    "deep_crawl": False,
                    "decompose_query": False
                }
            },
            {
                "name": "Full Feature Search",
                "query": "Climate change effects on global economy",
                "params": {
                    "num_results": 5,
                    "get_content": True,
                    "evaluate_question": True,
                    "evaluate_answer": True,
                    "deep_crawl": True,
                    "decompose_query": True,
                    "optimize_query": True
                }
            }
        ]
        
        results = {}
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 Test {i}: {test_case['name']}")
            print("-" * 40)
            
            start_time = time.time()
            
            try:
                result = agent.search(test_case["query"], **test_case["params"])
                end_time = time.time()
                
                print(f"✅ Search completed in {end_time - start_time:.2f}s")
                print(f"📊 Results: {len(result.get('results', []))} items")
                
                # Display key information
                if result.get("success"):
                    print(f"🔍 Query: {result.get('query')}")
                    print(f"🔧 Engine: {result.get('engine')}")
                    print(f"📈 Method: {result.get('search_method')}")
                    
                    # Show credibility summary if available
                    if "credibility_summary" in result:
                        cred = result["credibility_summary"]
                        print(f"🛡️  Credibility: {cred.get('credibility_level', 'unknown')} "
                              f"(avg: {cred.get('average_score', 0):.2f})")
                    
                    # Show question evaluation if available
                    if "question_evaluation" in result:
                        qeval = result["question_evaluation"]
                        print(f"❓ Question complexity: {qeval.get('complexity_level', 'unknown')}")
                    
                    # Show answer evaluation if available
                    if "answer_evaluation" in result:
                        aeval = result["answer_evaluation"]
                        print(f"💬 Answer quality: {aeval.get('quality_level', 'unknown')}")
                    
                    # Show sample results
                    print("📋 Sample results:")
                    for j, res in enumerate(result.get("results", [])[:2], 1):
                        print(f"  {j}. {res.get('title', 'No title')}")
                        print(f"     URL: {res.get('url', 'No URL')}")
                        if "credibility" in res:
                            cred_score = res["credibility"].get("score", 0)
                            cred_level = res["credibility"].get("level", "unknown")
                            print(f"     Credibility: {cred_level} ({cred_score:.2f})")
                
                else:
                    print(f"❌ Search failed: {result.get('error', 'Unknown error')}")
                
                results[test_case["name"]] = {
                    "success": result.get("success", False),
                    "execution_time": end_time - start_time,
                    "num_results": len(result.get("results", [])),
                    "has_credibility": "credibility_summary" in result,
                    "has_question_eval": "question_evaluation" in result,
                    "has_answer_eval": "answer_evaluation" in result
                }
                
            except Exception as e:
                end_time = time.time()
                print(f"❌ Test failed: {str(e)}")
                results[test_case["name"]] = {
                    "success": False,
                    "error": str(e),
                    "execution_time": end_time - start_time
                }
        
        # Test additional methods
        print(f"\n🔧 Testing Additional Methods")
        print("-" * 40)
        
        # Test credibility report
        try:
            cred_report = agent.get_credibility_report("https://example.com", "Sample content", "Sample title")
            print(f"✅ Credibility report: {type(cred_report).__name__}")
        except Exception as e:
            print(f"⚠️  Credibility report failed: {str(e)}")
        
        # Test alternative sources
        try:
            alt_sources = agent.get_alternative_sources("https://example.com")
            print(f"✅ Alternative sources: {len(alt_sources)} found")
        except Exception as e:
            print(f"⚠️  Alternative sources failed: {str(e)}")
        
        # Test disinformation check
        try:
            disinfo_result = agent.check_content_disinformation("This is sample content", "en", "Sample Title")
            print(f"✅ Disinformation check: {type(disinfo_result).__name__}")
        except Exception as e:
            print(f"⚠️  Disinformation check failed: {str(e)}")
        
        # Summary
        print(f"\n📊 Test Summary")
        print("=" * 60)
        
        successful_tests = sum(1 for r in results.values() if r.get("success", False))
        total_tests = len(results)
        
        print(f"✅ Successful tests: {successful_tests}/{total_tests}")
        print(f"⏱️  Average execution time: {sum(r.get('execution_time', 0) for r in results.values()) / len(results):.2f}s")
        
        # Feature coverage
        features_tested = {
            "Credibility evaluation": any(r.get("has_credibility", False) for r in results.values()),
            "Question evaluation": any(r.get("has_question_eval", False) for r in results.values()),
            "Answer evaluation": any(r.get("has_answer_eval", False) for r in results.values())
        }
        
        print(f"\n🎯 Feature Coverage:")
        for feature, tested in features_tested.items():
            status = "✅" if tested else "❌"
            print(f"  {status} {feature}")
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"test_results/consolidated_agent_test_{timestamp}.json"
        
        os.makedirs("test_results", exist_ok=True)
        
        with open(results_file, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": timestamp,
                "agent_info": {
                    "name": agent.name,
                    "version": agent.version
                },
                "test_results": results,
                "feature_coverage": features_tested,
                "summary": {
                    "successful_tests": successful_tests,
                    "total_tests": total_tests,
                    "success_rate": successful_tests / total_tests if total_tests > 0 else 0
                }
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        return successful_tests == total_tests
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        print("⚠️  Make sure all dependencies are installed")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 WebSearchAgentLocalConsolidated Test Suite")
    print("=" * 60)
    
    success = test_consolidated_agent()
    
    if success:
        print("\n🎉 All tests passed! Consolidation successful.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Check the output above.")
        sys.exit(1)
