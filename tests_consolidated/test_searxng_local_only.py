#!/usr/bin/env python3
"""
Ví dụ sử dụng WebSearchAgentLocalOnly chỉ với SearXNG local.
"""

import sys
from deepresearch.src.deep_research_core.agents.searxng_search import search_searxng, search_with_local_only
from deepresearch.src.deep_research_core.agents.web_search_agent_local_only import WebSearchAgentLocalOnly

def test_searxng_local_only():
    """Kiểm tra tìm kiếm chỉ với SearXNG local."""
    print("=== Kiểm tra tìm kiếm chỉ với SearXNG local ===")

    # Tìm kiếm trực tiếp với SearXNG local
    results = search_with_local_only(
        query="Python programming",
        num_results=5,
        language="en"
    )

    print(f"Thành công: {results.get('success')}")
    print(f"Số kết quả: {len(results.get('results', []))}")

    # In kết quả đầu tiên
    if results.get('results'):
        first_result = results['results'][0]
        print(f"\nKết quả đầu tiên:")
        print(f"Tiêu đề: {first_result.get('title')}")
        print(f"URL: {first_result.get('url')}")
        print(f"Nội dung: {first_result.get('content')[:100]}...")

def test_web_search_agent_local_only():
    """Kiểm tra WebSearchAgentLocalOnly."""
    print("\n=== Kiểm tra WebSearchAgentLocalOnly ===")

    agent = WebSearchAgentLocalOnly(
        search_method="searxng",
        api_search_config={
            "engine": "searx",
            "searx_url": "http://localhost:8080",
            "language": "auto"
        },
        verbose=True
    )

    results = agent.search("Python programming", num_results=5)

    print(f"Thành công: {results.get('success')}")
    print(f"Số kết quả: {len(results.get('results', []))}")

    # In kết quả đầu tiên
    if results.get('results'):
        first_result = results['results'][0]
        print(f"\nKết quả đầu tiên:")
        print(f"Tiêu đề: {first_result.get('title')}")
        print(f"URL: {first_result.get('url')}")
        print(f"Nội dung: {first_result.get('content')[:100]}...")

def test_crawlee_local_only():
    """Kiểm tra tìm kiếm với Crawlee chỉ sử dụng SearXNG local."""
    print("\n=== Kiểm tra tìm kiếm với Crawlee chỉ sử dụng SearXNG local ===")

    agent = WebSearchAgentLocalOnly(
        search_method="crawlee",
        api_search_config={
            "engine": "searx",
            "searx_url": "http://localhost:8080",
            "language": "auto"
        },
        verbose=True
    )

    results = agent.search("Python programming", num_results=5)

    print(f"Thành công: {results.get('success')}")
    print(f"Số kết quả: {len(results.get('results', []))}")

    # In kết quả đầu tiên
    if results.get('results'):
        first_result = results['results'][0]
        print(f"\nKết quả đầu tiên:")
        print(f"Tiêu đề: {first_result.get('title')}")
        print(f"URL: {first_result.get('url')}")
        print(f"Nội dung: {first_result.get('content')[:100]}...")

def main():
    """Hàm chính."""
    print("Kiểm tra tìm kiếm chỉ với SearXNG local\n")

    try:
        test_searxng_local_only()
        test_web_search_agent_local_only()
        test_crawlee_local_only()
    except Exception as e:
        print(f"Lỗi: {str(e)}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
