#!/usr/bin/env python3
"""
Performance benchmark test cho WebSearchAgentLocalMerged.
"""

import time
import json
import requests
from datetime import datetime

def test_search_engines_performance():
    """Test hiệu su<PERSON>t của các search engines."""
    print("⚡ PERFORMANCE BENCHMARK TEST")
    print("=" * 50)
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'search_engines': {},
        'summary': {}
    }
    
    test_query = "Python programming tutorial"
    
    # Test SearXNG Local
    print("🔍 Testing SearXNG Local...")
    searxng_time, searxng_success = test_searxng_performance(test_query)
    results['search_engines']['searxng_local'] = {
        'response_time': searxng_time,
        'success': searxng_success,
        'url': 'http://localhost:8080'
    }
    
    # Test DuckDuckGo
    print("🦆 Testing DuckDuckGo...")
    ddg_time, ddg_success = test_duckduckgo_performance(test_query)
    results['search_engines']['duckduckgo'] = {
        'response_time': ddg_time,
        'success': ddg_success,
        'url': 'https://api.duckduckgo.com'
    }
    
    # Test SearXNG Public
    print("🌐 Testing SearXNG Public...")
    public_time, public_success = test_searxng_public_performance(test_query)
    results['search_engines']['searxng_public'] = {
        'response_time': public_time,
        'success': public_success,
        'url': 'https://searx.be'
    }
    
    # Tính toán summary
    successful_engines = [k for k, v in results['search_engines'].items() if v['success']]
    response_times = [v['response_time'] for v in results['search_engines'].values() if v['success']]
    
    results['summary'] = {
        'total_engines_tested': len(results['search_engines']),
        'successful_engines': len(successful_engines),
        'success_rate': len(successful_engines) / len(results['search_engines']) * 100,
        'average_response_time': sum(response_times) / len(response_times) if response_times else 0,
        'fastest_engine': min(results['search_engines'].items(), key=lambda x: x[1]['response_time'] if x[1]['success'] else float('inf'))[0] if successful_engines else None
    }
    
    # Hiển thị kết quả
    print("\n📊 PERFORMANCE RESULTS")
    print("=" * 30)
    
    for engine, data in results['search_engines'].items():
        status = "✅" if data['success'] else "❌"
        time_str = f"{data['response_time']:.3f}s" if data['success'] else "FAILED"
        print(f"{status} {engine}: {time_str}")
    
    print(f"\n🎯 SUMMARY:")
    print(f"Success rate: {results['summary']['success_rate']:.1f}%")
    print(f"Average response time: {results['summary']['average_response_time']:.3f}s")
    if results['summary']['fastest_engine']:
        print(f"Fastest engine: {results['summary']['fastest_engine']}")
    
    # Lưu kết quả
    import os
    os.makedirs('test_results', exist_ok=True)
    report_file = f"test_results/performance_benchmark_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Performance report saved: {report_file}")
    
    return results['summary']['success_rate'] >= 50  # Ít nhất 50% engines hoạt động

def test_searxng_performance(query):
    """Test hiệu suất SearXNG local."""
    try:
        start_time = time.time()
        
        params = {
            'q': query,
            'format': 'json',
            'engines': 'google,bing,duckduckgo',
            'safesearch': '1'
        }
        
        response = requests.get(
            "http://localhost:8080/search",
            params=params,
            timeout=10,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            return response_time, len(results) > 0
        else:
            return response_time, False
            
    except Exception as e:
        print(f"  ❌ SearXNG local error: {e}")
        return 0, False

def test_duckduckgo_performance(query):
    """Test hiệu suất DuckDuckGo."""
    try:
        start_time = time.time()
        
        params = {
            'q': query,
            'format': 'json',
            'no_html': '1',
            'skip_disambig': '1'
        }
        
        response = requests.get(
            "https://api.duckduckgo.com/",
            params=params,
            timeout=10
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            has_results = bool(data.get('Abstract') or data.get('RelatedTopics'))
            return response_time, has_results
        else:
            return response_time, False
            
    except Exception as e:
        print(f"  ❌ DuckDuckGo error: {e}")
        return 0, False

def test_searxng_public_performance(query):
    """Test hiệu suất SearXNG public."""
    try:
        start_time = time.time()
        
        params = {
            'q': query,
            'format': 'json',
            'engines': 'google,bing,duckduckgo',
            'safesearch': '1'
        }
        
        response = requests.get(
            "https://searx.be/search",
            params=params,
            timeout=10,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            return response_time, len(results) > 0
        else:
            return response_time, False
            
    except Exception as e:
        print(f"  ❌ SearXNG public error: {e}")
        return 0, False

def test_cache_performance():
    """Test hiệu suất cache."""
    print("\n💾 CACHE PERFORMANCE TEST")
    print("=" * 30)
    
    # Simulate cache operations
    cache_data = {}
    
    # Test cache write performance
    start_time = time.time()
    for i in range(1000):
        cache_data[f"key_{i}"] = f"value_{i}" * 100  # 500 chars per value
    write_time = time.time() - start_time
    
    # Test cache read performance
    start_time = time.time()
    for i in range(1000):
        _ = cache_data.get(f"key_{i}")
    read_time = time.time() - start_time
    
    print(f"✅ Cache write (1000 items): {write_time:.3f}s")
    print(f"✅ Cache read (1000 items): {read_time:.3f}s")
    print(f"✅ Cache size: {len(cache_data)} items")
    
    return {
        'write_time': write_time,
        'read_time': read_time,
        'items_count': len(cache_data)
    }

def test_network_latency():
    """Test network latency."""
    print("\n🌐 NETWORK LATENCY TEST")
    print("=" * 25)
    
    test_urls = [
        "https://httpbin.org/get",
        "https://www.google.com",
        "https://searx.be",
        "http://localhost:8080"
    ]
    
    latencies = {}
    
    for url in test_urls:
        try:
            start_time = time.time()
            response = requests.get(url, timeout=5)
            latency = time.time() - start_time
            
            if response.status_code == 200:
                latencies[url] = latency
                print(f"✅ {url}: {latency:.3f}s")
            else:
                print(f"❌ {url}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {url}: {str(e)[:50]}")
    
    if latencies:
        avg_latency = sum(latencies.values()) / len(latencies)
        print(f"\n📊 Average latency: {avg_latency:.3f}s")
        return avg_latency
    else:
        return 0

def main():
    """Main performance test."""
    print("🚀 COMPREHENSIVE PERFORMANCE BENCHMARK")
    print("=" * 60)
    
    # Test search engines
    search_success = test_search_engines_performance()
    
    # Test cache performance
    cache_results = test_cache_performance()
    
    # Test network latency
    avg_latency = test_network_latency()
    
    # Overall assessment
    print("\n" + "=" * 60)
    print("🎯 OVERALL PERFORMANCE ASSESSMENT")
    print("=" * 60)
    
    if search_success:
        print("✅ Search engines: GOOD")
    else:
        print("❌ Search engines: POOR")
    
    if cache_results['write_time'] < 0.1 and cache_results['read_time'] < 0.1:
        print("✅ Cache performance: EXCELLENT")
    elif cache_results['write_time'] < 0.5 and cache_results['read_time'] < 0.5:
        print("✅ Cache performance: GOOD")
    else:
        print("⚠️  Cache performance: ACCEPTABLE")
    
    if avg_latency < 0.5:
        print("✅ Network latency: EXCELLENT")
    elif avg_latency < 2.0:
        print("✅ Network latency: GOOD")
    else:
        print("⚠️  Network latency: HIGH")
    
    overall_score = (
        (1 if search_success else 0) +
        (1 if cache_results['write_time'] < 0.5 else 0) +
        (1 if avg_latency < 2.0 else 0)
    ) / 3 * 100
    
    print(f"\n🏆 Overall Performance Score: {overall_score:.1f}%")
    
    if overall_score >= 80:
        print("🎉 EXCELLENT PERFORMANCE!")
        return True
    elif overall_score >= 60:
        print("✅ GOOD PERFORMANCE")
        return True
    else:
        print("⚠️  PERFORMANCE NEEDS IMPROVEMENT")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
