"""
Test module for vietnamese_utils.py
"""

import unittest
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from deepresearch.src.deep_research_core.utils.vietnamese_utils import (
    normalize_vietnamese_diacritics,
    simple_vietnamese_tokenize,
    advanced_vietnamese_tokenize
)


class TestVietnameseUtils(unittest.TestCase):
    """Test cases for vietnamese_utils.py"""

    def test_simple_vietnamese_tokenize(self):
        """Test simple_vietnamese_tokenize function"""
        # Test with empty string
        self.assertEqual(simple_vietnamese_tokenize(""), [])

        # Test with normal text
        tokens = simple_vietnamese_tokenize("Xin chào thế giới")
        self.assertIsInstance(tokens, list)
        self.assertTrue(all(isinstance(token, str) for token in tokens))

        # Test with Vietnamese text containing compound words
        tokens = simple_vietnamese_tokenize("Hòa bình và phát triển")
        self.assertIn("và", tokens)

        # Test with text containing punctuation
        tokens = simple_vietnamese_tokenize("Việt Nam, một đất nước tuyệt vời!")
        self.assertIn(",", tokens)
        self.assertIn("!", tokens)

    def test_advanced_vietnamese_tokenize(self):
        """Test advanced_vietnamese_tokenize function"""
        # Test with empty string
        self.assertEqual(advanced_vietnamese_tokenize(""), [])

        # Test with normal text
        tokens = advanced_vietnamese_tokenize("Xin chào thế giới")
        self.assertIsInstance(tokens, list)
        self.assertTrue(all(isinstance(token, str) for token in tokens))

        # Test with Vietnamese text containing compound words
        tokens = advanced_vietnamese_tokenize("Hòa bình và phát triển")
        self.assertIn("và", tokens)

        # Test with complex Vietnamese text
        tokens = advanced_vietnamese_tokenize("Việt Nam là một quốc gia có nền văn hóa lâu đời")
        self.assertIn("một", tokens)
        self.assertIn("quốc", tokens)


if __name__ == "__main__":
    unittest.main()
