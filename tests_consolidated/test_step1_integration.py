#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test STEP 1, 2 & 3: AdvancedCrawlee + Error Utils + Playwright Handler Integration
"""

import sys
import os

# Add paths
sys.path.append('.')
sys.path.append('src')
sys.path.append('deepresearch/src')

def test_advanced_crawlee_import():
    """Test importing AdvancedCrawlee modules."""
    try:
        print("Testing AdvancedCrawlee import...")
        from deepresearch.src.deep_research_core.agents.advanced_crawlee import ResourceManager, MemoryOptimizedCrawler
        print("✅ AdvancedCrawlee modules imported successfully")
        return True, ResourceManager, MemoryOptimizedCrawler
    except Exception as e:
        print(f"❌ Failed to import AdvancedCrawlee: {e}")
        return False, None, None

def test_resource_manager():
    """Test ResourceManager functionality."""
    try:
        success, ResourceManager, _ = test_advanced_crawlee_import()
        if not success:
            return False
        
        print("Creating ResourceManager instance...")
        rm = ResourceManager(memory_limit_mb=512, max_concurrent_processes=2)
        print("✅ ResourceManager created")
        
        print("Testing ResourceManager methods...")
        status = rm.get_resource_usage()
        print(f"Resource usage: {status}")
        
        active = rm.get_active_processes()
        print(f"Active processes: {active}")
        
        print("✅ ResourceManager tests passed")
        return True
    except Exception as e:
        print(f"❌ ResourceManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_optimized_crawler():
    """Test MemoryOptimizedCrawler functionality."""
    try:
        success, ResourceManager, MemoryOptimizedCrawler = test_advanced_crawlee_import()
        if not success:
            return False
        
        print("Creating ResourceManager for MemoryOptimizedCrawler...")
        rm = ResourceManager(memory_limit_mb=512, max_concurrent_processes=2)
        
        print("Creating MemoryOptimizedCrawler instance...")
        crawler = MemoryOptimizedCrawler(resource_manager=rm, batch_size=5)
        print("✅ MemoryOptimizedCrawler created")
        
        print("✅ MemoryOptimizedCrawler tests passed")
        return True
    except Exception as e:
        print(f"❌ MemoryOptimizedCrawler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adaptive_crawler_integration():
    """Test AdaptiveCrawler integration with AdvancedCrawlee."""
    try:
        print("Testing AdaptiveCrawler integration...")
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

        print("Creating AdaptiveCrawler with memory optimization...")
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_memory_optimization=True,
            memory_limit_mb=512,
            max_concurrent_processes=2,
            batch_size=5
        )
        print("✅ AdaptiveCrawler created")

        print("Testing resource status...")
        status = crawler.get_resource_status()
        print(f"Resource status: {status}")

        print("Testing batch size optimization...")
        batch_size = crawler.optimize_batch_size()
        print(f"Optimized batch size: {batch_size}")

        print("Testing memory cleanup...")
        crawler.cleanup_memory()
        print("✅ Memory cleanup completed")

        print("✅ AdaptiveCrawler integration tests passed")
        return True
    except Exception as e:
        print(f"❌ AdaptiveCrawler integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling_integration():
    """Test Error Utils integration with AdaptiveCrawler."""
    try:
        print("Testing Error Utils integration...")
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

        print("Creating AdaptiveCrawler with error handling...")
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_error_handling=True,
            max_retries=2,
            retry_delay=0.5,
            retry_backoff=1.5
        )
        print("✅ AdaptiveCrawler with error handling created")

        print("Testing input validation...")
        validation_result = crawler.validate_crawl_input(
            url="https://example.com",
            max_depth=2,
            max_pages=5,
            use_playwright=True
        )
        print(f"Validation result: {validation_result}")

        print("Testing invalid input validation...")
        invalid_validation = crawler.validate_crawl_input(
            url="invalid-url",
            max_depth=-1,
            max_pages="not-a-number"
        )
        print(f"Invalid validation result: {invalid_validation}")

        print("Testing safe crawl multiple with empty list...")
        safe_result = crawler.safe_crawl_multiple([])
        print(f"Safe crawl empty result: {safe_result}")

        print("✅ Error handling integration tests passed")
        return True
    except Exception as e:
        print(f"❌ Error handling integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_playwright_handler_integration():
    """Test Playwright Handler integration with AdaptiveCrawler."""
    try:
        print("Testing Playwright Handler integration...")
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

        print("Creating AdaptiveCrawler with Playwright Handler...")
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_playwright_handler=True,
            playwright_headless=True,
            playwright_browser_type="chromium",
            playwright_timeout=30000
        )
        print("✅ AdaptiveCrawler with Playwright Handler created")

        print("Testing Playwright Handler status...")
        status = crawler.get_playwright_handler_status()
        print(f"Playwright Handler status: {status}")

        print("Testing Playwright context creation...")
        context = crawler.create_playwright_context()
        print(f"Playwright context created: {context is not None}")

        print("✅ Playwright Handler integration tests passed")
        return True
    except Exception as e:
        print(f"❌ Playwright Handler integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_processor_integration():
    """Test File Processor integration with AdaptiveCrawler."""
    try:
        print("Testing File Processor integration...")
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

        print("Creating AdaptiveCrawler with File Processor...")
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_file_processor=True,
            file_processor_encoding="utf-8",
            file_processor_extract_metadata=True,
            file_processor_ocr_enabled=False
        )
        print("✅ AdaptiveCrawler with File Processor created")

        print("Testing File Processor status...")
        status = crawler.get_file_processor_status()
        print(f"File Processor status: {status}")

        print("✅ File Processor integration tests passed")
        return True
    except Exception as e:
        print(f"❌ File Processor integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_content_extraction_integration():
    """Test Content Extraction Utils integration with AdaptiveCrawler."""
    try:
        print("Testing Content Extraction Utils integration...")
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

        print("Creating AdaptiveCrawler with Content Extraction Utils...")
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_content_extraction=True,
            content_min_length=50,
            content_max_length=10000,
            content_extract_metadata=True,
            content_site_detection=True,
            content_summary_enabled=True,
            content_summary_max_length=200
        )
        print("✅ AdaptiveCrawler with Content Extraction Utils created")

        print("Testing Content Extraction Utils status...")
        status = crawler.get_content_extraction_status()
        print(f"Content Extraction Utils status: {status}")

        print("Testing advanced content extraction...")
        sample_html = """
        <html>
        <head><title>Test Page</title></head>
        <body>
        <h1>Main Title</h1>
        <p>This is a test paragraph with some content for extraction testing.</p>
        <div class="content">More content here for testing purposes.</div>
        </body>
        </html>
        """

        extraction_result = crawler.extract_content_advanced("https://example.com", sample_html)
        print(f"Content extraction result: {extraction_result.get('success', False)}")
        print(f"Extracted text length: {len(extraction_result.get('text', ''))}")

        print("✅ Content Extraction Utils integration tests passed")
        return True
    except Exception as e:
        print(f"❌ Content Extraction Utils integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_advanced_monitoring_integration():
    """Test Advanced Monitoring integration with AdaptiveCrawler."""
    try:
        print("Testing Advanced Monitoring integration...")
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

        print("Creating AdaptiveCrawler with Advanced Monitoring...")
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_advanced_monitoring=True,
            monitoring_enabled=True,
            system_monitoring_enabled=False,  # Don't auto-start for test
            performance_tracking_enabled=True,
            memory_monitoring_enabled=True,
            cpu_monitoring_enabled=True,
            network_monitoring_enabled=True,
            error_tracking_enabled=True,
            execution_time_tracking=True
        )
        print("✅ AdaptiveCrawler with Advanced Monitoring created")

        print("Testing Advanced Monitoring status...")
        status = crawler.get_monitoring_status()
        print(f"Advanced Monitoring status: {status}")

        print("Testing performance metrics...")
        metrics = crawler.get_performance_metrics()
        print(f"Performance metrics available: {metrics.get('error') is None}")

        print("Testing system status...")
        system_status = crawler.get_system_status()
        print(f"System status available: {system_status.get('error') is None}")

        print("Testing crawl metrics recording...")
        import time
        start_time = time.time()
        time.sleep(0.1)  # Simulate crawl duration
        duration = time.time() - start_time
        crawler.record_crawl_metrics("https://example.com", True, duration, 1024)
        print("✅ Crawl metrics recorded")

        print("✅ Advanced Monitoring integration tests passed")
        return True
    except Exception as e:
        print(f"❌ Advanced Monitoring integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_agent_manager_integration():
    """Test User Agent Manager integration with AdaptiveCrawler."""
    try:
        print("Testing User Agent Manager integration...")
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

        print("Creating AdaptiveCrawler with User Agent Manager...")
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_user_agent_manager=True,
            user_agent_rotation_enabled=True,
            user_agent_rotation_interval=5,
            user_agent_device_type="desktop",
            user_agent_custom="Custom User Agent Test"
        )
        print("✅ AdaptiveCrawler with User Agent Manager created")

        print("Testing User Agent Manager status...")
        status = crawler.get_user_agent_status()
        print(f"User Agent Manager status: {status}")

        print("Testing user agent methods...")
        random_ua = crawler.get_random_user_agent()
        print(f"Random user agent: {random_ua[:50]}...")

        next_ua = crawler.get_next_user_agent()
        print(f"Next user agent: {next_ua[:50]}...")

        count = crawler.get_user_agents_count()
        print(f"User agents count: {count}")

        print("Testing user agent management...")
        test_ua = "Mozilla/5.0 (Test Browser) Test/1.0"
        add_result = crawler.add_user_agent(test_ua, "desktop")
        print(f"Add user agent result: {add_result}")

        remove_result = crawler.remove_user_agent(test_ua, "desktop")
        print(f"Remove user agent result: {remove_result}")

        print("Testing user agent rotation...")
        enable_result = crawler.enable_user_agent_rotation(10)
        print(f"Enable rotation result: {enable_result}")

        disable_result = crawler.disable_user_agent_rotation()
        print(f"Disable rotation result: {disable_result}")

        print("✅ User Agent Manager integration tests passed")
        return True
    except Exception as e:
        print(f"❌ User Agent Manager integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("STEP 1 & 2 INTEGRATION TEST: AdvancedCrawlee + Error Utils")
    print("=" * 60)
    
    tests = [
        ("AdvancedCrawlee Import", test_advanced_crawlee_import),
        ("ResourceManager", test_resource_manager),
        ("MemoryOptimizedCrawler", test_memory_optimized_crawler),
        ("AdaptiveCrawler Integration", test_adaptive_crawler_integration),
        ("Error Handling Integration", test_error_handling_integration),
        ("Playwright Handler Integration", test_playwright_handler_integration),
        ("File Processor Integration", test_file_processor_integration),
        ("Content Extraction Utils Integration", test_content_extraction_integration),
        ("Advanced Monitoring Integration", test_advanced_monitoring_integration),
        ("User Agent Manager Integration", test_user_agent_manager_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔧 Testing {test_name}...")
        print("-" * 40)
        
        if test_name == "AdvancedCrawlee Import":
            success, _, _ = test_func()
        else:
            success = test_func()
        
        results.append((test_name, success))
        
        if success:
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! STEP 1 & 2 INTEGRATION SUCCESSFUL!")
        return True
    else:
        print("⚠️  SOME TESTS FAILED. STEP 1 & 2 INTEGRATION INCOMPLETE.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
