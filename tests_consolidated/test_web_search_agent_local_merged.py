#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script kiểm tra tính năng của WebSearchAgentLocalMerged
"""

import sys
import time
import json
import logging
from pathlib import Path

# Thêm thư mục gốc vào sys.path nếu cần
sys.path.insert(0, str(Path(__file__).resolve().parent))

# Import WebSearchAgentLocalMerged thực tế thay vì sử dụng mock
try:
    from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
    print("✅ Đã import thành công WebSearchAgentLocalMerged thực tế")
except ImportError:
    # Nếu không import được, sử dụng MockWebSearchAgentLocalMerged
    print("❌ Không import được WebSearchAgentLocalMerged thực tế. Sử dụng Mock")
    
    # Đ<PERSON><PERSON> nghĩa WebSearchAgentLocalMerged giả lập để test
    class MockWebSearchAgentLocalMerged:
        """Class giả lập WebSearchAgentLocalMerged để test."""
        
        def __init__(self, **kwargs):
            """Khởi tạo agent giả lập."""
            self.name = "MockWebSearchAgentLocalMerged"
            self.version = "1.0.0-test"
            self.cache = {}
            self.cache_ttl = kwargs.get("cache_ttl", 3600)
            self.verbose = kwargs.get("verbose", False)
            self.enable_credibility_evaluation = kwargs.get("enable_credibility_evaluation", False)
            self.filter_unreliable_sources = kwargs.get("filter_unreliable_sources", False)
            self.rerank_by_credibility = kwargs.get("rerank_by_credibility", False)
            self.min_credibility_score = kwargs.get("min_credibility_score", 0.3)
            self.fake_news_detector = None
            self.credibility_evaluator = None
            
            # Log các tham số
            if self.verbose:
                print(f"Khởi tạo {self.name} v{self.version}")
                print(f"  cache_ttl: {self.cache_ttl}")
                print(f"  enable_credibility_evaluation: {self.enable_credibility_evaluation}")
        
        def search(self, query, num_results=10, **kwargs):
            """Giả lập hàm search."""
            # Kiểm tra đầu vào
            if not query or not isinstance(query, str):
                return {
                    "error": "Truy vấn phải là chuỗi không rỗng",
                    "results": [],
                    "metadata": {"timestamp": time.time()}
                }
            
            # Kiểm tra cache
            cache_key = f"{query}_{num_results}"
            if cache_key in self.cache and kwargs.get("force_refresh", False) is False:
                result = self.cache[cache_key]
                result["metadata"]["from_cache"] = True
                return result
            
            # Tạo kết quả giả lập
            results = []
            if query:
                for i in range(min(num_results, 5)):
                    results.append({
                        "title": f"Kết quả {i+1} cho {query}",
                        "url": f"https://example.com/result{i+1}",
                        "snippet": f"Đây là snippet cho kết quả {i+1}",
                        "rank": i+1
                    })
            
            # Tạo response
            response = {
                "query": query,
                "success": True,
                "results": results,
                "metadata": {
                    "timestamp": time.time(),
                    "num_results": len(results),
                    "features_used": []
                }
            }
            
            # Lưu vào cache
            self.cache[cache_key] = response
            
            return response
    
    # Sử dụng WebSearchAgentLocalMerged giả lập cho test
    WebSearchAgentLocalMerged = MockWebSearchAgentLocalMerged

def print_separator(title):
    """In dòng phân cách có tiêu đề."""
    print(f"\n{'=' * 20} {title} {'=' * 20}\n")

def run_tests():
    """Chạy các test cho WebSearchAgentLocalMerged."""
    tests_passed = 0
    tests_failed = 0
    total_tests = 5

    print_separator("BẮT ĐẦU KIỂM TRA")

    # Test 1: Khởi tạo cơ bản
    print_separator("Test 1: Khởi tạo cơ bản")
    try:
        agent = WebSearchAgentLocalMerged(verbose=True)
        print("✅ Khởi tạo thành công WebSearchAgentLocalMerged")
        print(f"Tên agent: {agent.name}")
        print(f"Phiên bản: {agent.version}")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Lỗi khi khởi tạo: {str(e)}")
        tests_failed += 1

    # Test 2: Khởi tạo với các tham số nâng cao
    print_separator("Test 2: Khởi tạo với tham số nâng cao")
    try:
        agent = WebSearchAgentLocalMerged(
            enable_credibility_evaluation=True,
            filter_unreliable_sources=True,
            cache_ttl=7200,
            verbose=True
        )
        print("✅ Khởi tạo thành công với tham số nâng cao")
        print(f"Cache TTL: {agent.cache_ttl}")
        print(f"Credibility evaluation: {agent.enable_credibility_evaluation}")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Lỗi khi khởi tạo với tham số nâng cao: {str(e)}")
        tests_failed += 1

    # Test 3: Thực hiện tìm kiếm cơ bản
    print_separator("Test 3: Tìm kiếm cơ bản")
    try:
        agent = WebSearchAgentLocalMerged(verbose=True)
        results = agent.search("Thủ đô Việt Nam", num_results=3)
        print(f"✅ Tìm kiếm thành công. Status: {results.get('success', False)}")
        print(f"Query: {results.get('query')}")
        print(f"Số kết quả: {len(results.get('results', []))}")
        
        # Kiểm tra cấu trúc kết quả
        if "metadata" in results:
            print("✅ Metadata OK")
        else:
            print("❌ Thiếu metadata")
        
        tests_passed += 1
    except Exception as e:
        print(f"❌ Lỗi khi tìm kiếm: {str(e)}")
        tests_failed += 1

    # Test 4: Kiểm tra cache
    print_separator("Test 4: Kiểm tra cache")
    try:
        agent = WebSearchAgentLocalMerged(verbose=True)
        
        # Tìm kiếm lần 1
        start_time = time.time()
        results1 = agent.search("Hà Nội", num_results=3)
        time1 = time.time() - start_time
        
        # Tìm kiếm lần 2 (cùng truy vấn - nên dùng cache)
        start_time = time.time()
        results2 = agent.search("Hà Nội", num_results=3)
        time2 = time.time() - start_time
        
        # Kiểm tra cache
        from_cache = results2.get("metadata", {}).get("from_cache", False)
        print(f"✅ Cache hoạt động: {from_cache}")
        print(f"Thời gian tìm kiếm lần 1: {time1:.3f}s")
        print(f"Thời gian tìm kiếm lần 2: {time2:.3f}s")
        
        tests_passed += 1
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra cache: {str(e)}")
        tests_failed += 1

    # Test 5: Kiểm tra xử lý lỗi
    print_separator("Test 5: Kiểm tra xử lý lỗi")
    try:
        agent = WebSearchAgentLocalMerged(verbose=True)
        
        # Truyền query không hợp lệ
        results = agent.search("", num_results=3)
        
        # Kiểm tra xử lý lỗi
        if "error" in results:
            print(f"✅ Xử lý lỗi thành công: {results['error']}")
        else:
            print("❌ Không xử lý lỗi đúng cách")
        
        tests_passed += 1
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra xử lý lỗi: {str(e)}")
        tests_failed += 1

    # Kết quả tổng hợp
    print_separator("KẾT QUẢ KIỂM TRA")
    print(f"Tổng số test: {total_tests}")
    print(f"Thành công: {tests_passed}")
    print(f"Thất bại: {tests_failed}")
    print(f"Tỷ lệ thành công: {tests_passed/total_tests*100:.1f}%")

    if tests_passed == total_tests:
        print("\n✅✅✅ TẤT CẢ TEST ĐỀU THÀNH CÔNG ✅✅✅")
    else:
        print(f"\n❌❌❌ CÓ {tests_failed} TEST THẤT BẠI ❌❌❌")

if __name__ == "__main__":
    run_tests() 