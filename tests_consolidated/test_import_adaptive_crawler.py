#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script đơn giản để kiểm tra import AdaptiveCrawler.
"""

import os
import sys
import logging

# Thê<PERSON> thư mục gốc vào sys.path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# <PERSON>hi<PERSON><PERSON> lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Thử import AdaptiveCrawler từ các đường dẫn khác nhau
paths_to_try = [
    "deep_research_core.crawlers.adaptive_crawler",
    "src.deep_research_core.crawlers.adaptive_crawler",
    "deepresearch.src.deep_research_core.crawlers.adaptive_crawler",
    "deepresearch.src.deep_research_core.agents.adaptive_crawler",
    "deepresearch.adaptive_crawler"
]

success = False

for path in paths_to_try:
    try:
        logger.info(f"Đang thử import AdaptiveCrawler từ {path}")
        module_path, class_name = path.rsplit(".", 1)
        module = __import__(module_path, fromlist=[class_name])
        AdaptiveCrawler = getattr(module, class_name)
        logger.info(f"Đã import thành công AdaptiveCrawler từ {path}")
        logger.info(f"Các tham số của AdaptiveCrawler: {dir(AdaptiveCrawler)}")
        success = True
        break
    except (ImportError, AttributeError) as e:
        logger.error(f"Không thể import từ {path}: {str(e)}")

if not success:
    logger.error("Không thể import AdaptiveCrawler từ bất kỳ đường dẫn nào.")
    sys.exit(1)

# Nếu import thành công, hiển thị thông tin về lớp
logger.info("Thông tin về lớp AdaptiveCrawler:")
logger.info(f"Module: {AdaptiveCrawler.__module__}")
logger.info(f"Docstring: {AdaptiveCrawler.__doc__}")

# Thử tạo một instance của AdaptiveCrawler
try:
    crawler = AdaptiveCrawler()
    logger.info("Đã tạo thành công instance của AdaptiveCrawler")
    logger.info(f"Các thuộc tính và phương thức: {dir(crawler)}")
except Exception as e:
    logger.error(f"Không thể tạo instance của AdaptiveCrawler: {str(e)}")
