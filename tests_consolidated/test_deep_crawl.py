#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test case cho chức năng tìm kiếm sâu (deep crawl) của WebSearchAgentLocal.
"""

import os
import sys
import unittest
import time
import json
import logging
from datetime import datetime

# Thêm thư mục hiện tại vào đường dẫn Python
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# Import các module cần thiết
import sys
from deepresearch.question_complexity_evaluator import QuestionComplexityEvaluator
from deepresearch.answer_quality_evaluator import AnswerQualityEvaluator
from deepresearch.captcha_handler import CaptchaHandler
from deepresearch.web_search_agent_local import WebSearchAgentLocal
from deepresearch.adaptive_crawler import AdaptiveCrawler

# Đảm bảo các module có thể được import
sys.modules['captcha_handler'] = sys.modules['deepresearch.captcha_handler']
sys.modules['question_complexity_evaluator'] = sys.modules['deepresearch.question_complexity_evaluator']
sys.modules['answer_quality_evaluator'] = sys.modules['deepresearch.answer_quality_evaluator']
sys.modules['adaptive_crawler'] = sys.modules['deepresearch.adaptive_crawler']

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Tạo file kết quả
RESULTS_FILE = "deep_crawl_results.md"

def write_to_results(text):
    """Ghi kết quả vào file."""
    with open(RESULTS_FILE, "a", encoding="utf-8") as f:
        f.write(text + "\n")

def write_section_header(title):
    """Ghi tiêu đề phần vào file kết quả."""
    write_to_results(f"\n## {title}\n")

def write_test_result(test_name, result):
    """Ghi kết quả test vào file."""
    write_to_results(f"### {test_name}\n")
    write_to_results("```\n" + result + "\n```\n")

class DeepCrawlTest(unittest.TestCase):
    """
    Test cho chức năng tìm kiếm sâu (deep crawl) của WebSearchAgentLocal.
    """

    @classmethod
    def setUpClass(cls):
        """Thiết lập trước khi chạy tất cả các test."""
        # Tạo file kết quả mới
        with open(RESULTS_FILE, "w", encoding="utf-8") as f:
            f.write(f"# Kết quả kiểm tra tìm kiếm sâu (Deep Crawl)\n")
            f.write(f"Thời gian: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

    def setUp(self):
        """Thiết lập trước mỗi test."""
        self.agent = WebSearchAgentLocal(
            verbose=True,
            captcha_handler_config={
                "auto_solve": False,
                "use_selenium": False,
                "max_retries": 3,
                "retry_delay": 5
            },
            question_evaluator_config={
                "complexity_threshold_high": 0.7,
                "complexity_threshold_medium": 0.4,
                "use_domain_knowledge": True
            },
            answer_evaluator_config={
                "quality_threshold_high": 0.7,
                "quality_threshold_medium": 0.4,
                "use_model_evaluation": False
            }
        )

        self.crawler = AdaptiveCrawler(
            max_depth=2,
            max_pages=5,
            timeout=30,
            user_agent_rotation=True,
            respect_robots_txt=False,  # Tắt tùy chọn respect_robots_txt để test
            verbose=True
        )

    def test_01_direct_deep_crawl(self):
        """Test trực tiếp chức năng deep crawl với URL cụ thể."""
        test_name = "Deep Crawl trực tiếp với URL cụ thể"

        # URL an toàn để test
        url = "https://www.python.org/"

        # Thực hiện deep crawl
        start_time = time.time()
        result = self.crawler.crawl(url)
        end_time = time.time()

        # Phân tích kết quả
        output = f"URL: {url}\n"
        output += f"Thời gian crawl: {end_time - start_time:.2f} giây\n"
        output += f"Số trang đã crawl: {len(result)}\n\n"

        # Hiển thị thông tin về các trang đã crawl
        output += "Các trang đã crawl:\n"
        for i, page in enumerate(result[:3]):  # Chỉ hiển thị 3 trang đầu tiên
            output += f"Trang {i+1}:\n"
            output += f"  URL: {page.get('url', '')}\n"
            output += f"  Tiêu đề: {page.get('title', '')}\n"
            output += f"  Độ sâu: {page.get('depth', 0)}\n"
            output += f"  Số ký tự nội dung: {len(page.get('content', ''))}\n"
            output += f"  Số liên kết: {len(page.get('links', []))}\n\n"

        if len(result) > 3:
            output += f"... và {len(result) - 3} trang khác\n"

        write_test_result(test_name, output)
        self.assertTrue(len(result) > 0)

    def test_02_deep_crawl_with_search(self):
        """Test tìm kiếm sâu kết hợp với tìm kiếm."""
        test_name = "Deep Crawl kết hợp với tìm kiếm"

        # Câu hỏi phức tạp cần deep crawl
        query = "What are the key features of Python 3.9 and how do they improve developer productivity?"

        # Thực hiện tìm kiếm với deep crawl
        start_time = time.time()
        result = self.agent.search(
            query=query,
            num_results=5,
            deep_crawl=True,
            get_content=True
        )
        end_time = time.time()

        # Phân tích kết quả
        output = f"Câu hỏi: {query}\n"
        output += f"Thời gian tìm kiếm: {end_time - start_time:.2f} giây\n"
        output += f"Thành công: {result.get('success', False)}\n"
        output += f"Số kết quả: {len(result.get('results', []))}\n\n"

        # Hiển thị kết quả tìm kiếm
        output += "Kết quả tìm kiếm:\n"
        for i, res in enumerate(result.get('results', [])[:3]):  # Chỉ hiển thị 3 kết quả đầu tiên
            output += f"Kết quả {i+1}:\n"
            output += f"  Tiêu đề: {res.get('title', '')}\n"
            output += f"  URL: {res.get('url', '')}\n"
            output += f"  Snippet: {res.get('snippet', '')[:100]}...\n"
            output += f"  Source: {res.get('source', '')}\n"

            # Hiển thị thông tin về nội dung đã crawl
            if 'content' in res and res['content']:
                output += f"  Độ dài nội dung: {len(res['content'])} ký tự\n"
                output += f"  Nội dung (trích đoạn): {res['content'][:150]}...\n"

            output += "\n"

        if len(result.get('results', [])) > 3:
            output += f"... và {len(result.get('results', [])) - 3} kết quả khác\n"

        write_test_result(test_name, output)
        self.assertTrue(result["success"])

    def test_03_deep_crawl_with_complex_query(self):
        """Test tìm kiếm sâu với câu hỏi phức tạp."""
        test_name = "Deep Crawl với câu hỏi phức tạp"

        # Câu hỏi rất phức tạp
        query = "Compare and contrast the performance characteristics of Python's asyncio, threading, and multiprocessing modules for different types of I/O-bound and CPU-bound tasks, including specific code examples and benchmarks."

        # Đánh giá độ phức tạp câu hỏi
        complexity_result = self.agent.evaluate_question_complexity(query)

        # Thực hiện tìm kiếm với deep crawl
        start_time = time.time()
        result = self.agent.search(
            query=query,
            num_results=5,
            deep_crawl=True,
            get_content=True,
            evaluate_question=True
        )
        end_time = time.time()

        # Phân tích kết quả
        output = f"Câu hỏi: {query}\n"
        output += f"Thời gian tìm kiếm: {end_time - start_time:.2f} giây\n"
        output += f"Thành công: {result.get('success', False)}\n"
        output += f"Số kết quả: {len(result.get('results', []))}\n\n"

        # Hiển thị đánh giá câu hỏi
        output += "Đánh giá câu hỏi:\n"
        output += f"Độ phức tạp: {complexity_result.get('complexity_level', 'unknown')}\n"
        output += f"Điểm phức tạp: {complexity_result.get('complexity_score', 0)}\n\n"

        # Hiển thị chiến lược đề xuất
        strategy = complexity_result.get('recommended_strategy', {})
        output += f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}\n"
        output += f"Số kết quả đề xuất: {strategy.get('num_results', 5)}\n"
        output += f"Trích xuất nội dung: {strategy.get('get_content', False)}\n"
        output += f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}\n\n"

        # Hiển thị kết quả tìm kiếm
        output += "Kết quả tìm kiếm:\n"
        for i, res in enumerate(result.get('results', [])[:3]):  # Chỉ hiển thị 3 kết quả đầu tiên
            output += f"Kết quả {i+1}:\n"
            output += f"  Tiêu đề: {res.get('title', '')}\n"
            output += f"  URL: {res.get('url', '')}\n"
            output += f"  Snippet: {res.get('snippet', '')[:100]}...\n"
            output += f"  Source: {res.get('source', '')}\n"

            # Hiển thị thông tin về nội dung đã crawl
            if 'content' in res and res['content']:
                output += f"  Độ dài nội dung: {len(res['content'])} ký tự\n"
                output += f"  Nội dung (trích đoạn): {res['content'][:150]}...\n"

            output += "\n"

        if len(result.get('results', [])) > 3:
            output += f"... và {len(result.get('results', [])) - 3} kết quả khác\n"

        write_test_result(test_name, output)
        self.assertTrue(result["success"])
        self.assertIn(complexity_result["complexity_level"], ["medium", "high"])

    def test_04_deep_crawl_with_vietnamese_query(self):
        """Test tìm kiếm sâu với câu hỏi tiếng Việt."""
        test_name = "Deep Crawl với câu hỏi tiếng Việt"

        # Câu hỏi tiếng Việt phức tạp
        query = "So sánh và phân tích chi tiết các đặc điểm của Python và JavaScript trong phát triển ứng dụng web, bao gồm hiệu suất, bảo mật và khả năng mở rộng."

        # Đánh giá độ phức tạp câu hỏi
        complexity_result = self.agent.evaluate_question_complexity(query)

        # Thực hiện tìm kiếm với deep crawl
        start_time = time.time()
        result = self.agent.search(
            query=query,
            num_results=5,
            deep_crawl=True,
            get_content=True,
            evaluate_question=True
        )
        end_time = time.time()

        # Phân tích kết quả
        output = f"Câu hỏi: {query}\n"
        output += f"Thời gian tìm kiếm: {end_time - start_time:.2f} giây\n"
        output += f"Thành công: {result.get('success', False)}\n"
        output += f"Số kết quả: {len(result.get('results', []))}\n\n"

        # Hiển thị đánh giá câu hỏi
        output += "Đánh giá câu hỏi:\n"
        output += f"Độ phức tạp: {complexity_result.get('complexity_level', 'unknown')}\n"
        output += f"Điểm phức tạp: {complexity_result.get('complexity_score', 0)}\n\n"

        # Hiển thị chiến lược đề xuất
        strategy = complexity_result.get('recommended_strategy', {})
        output += f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}\n"
        output += f"Số kết quả đề xuất: {strategy.get('num_results', 5)}\n"
        output += f"Trích xuất nội dung: {strategy.get('get_content', False)}\n"
        output += f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}\n\n"

        # Hiển thị kết quả tìm kiếm
        output += "Kết quả tìm kiếm:\n"
        for i, res in enumerate(result.get('results', [])[:3]):  # Chỉ hiển thị 3 kết quả đầu tiên
            output += f"Kết quả {i+1}:\n"
            output += f"  Tiêu đề: {res.get('title', '')}\n"
            output += f"  URL: {res.get('url', '')}\n"
            output += f"  Snippet: {res.get('snippet', '')[:100]}...\n"
            output += f"  Source: {res.get('source', '')}\n"

            # Hiển thị thông tin về nội dung đã crawl
            if 'content' in res and res['content']:
                output += f"  Độ dài nội dung: {len(res['content'])} ký tự\n"
                output += f"  Nội dung (trích đoạn): {res['content'][:150]}...\n"

            output += "\n"

        if len(result.get('results', [])) > 3:
            output += f"... và {len(result.get('results', [])) - 3} kết quả khác\n"

        write_test_result(test_name, output)
        self.assertTrue(result["success"])

    def test_05_deep_crawl_with_poor_answer(self):
        """Test tìm kiếm sâu khi câu trả lời ban đầu kém."""
        test_name = "Deep Crawl khi câu trả lời ban đầu kém"

        # Câu hỏi
        query = "What are the advanced features of Python's asyncio module?"

        # Câu trả lời kém
        poor_answer = "Asyncio is a module in Python."

        # Kết quả tìm kiếm ban đầu
        initial_results = [
            {
                "title": "Python asyncio - Wikipedia",
                "url": "https://en.wikipedia.org/wiki/Python_asyncio",
                "snippet": "Asyncio is a module in Python.",
                "content": "Asyncio is a module in Python."
            }
        ]

        # Đánh giá chất lượng câu trả lời
        quality_result = self.agent.evaluate_answer_quality(query, poor_answer, initial_results)

        # Thực hiện tìm kiếm sâu nếu câu trả lời kém
        start_time = time.time()
        if quality_result.get('need_more_search', False):
            result = self.agent.search(
                query=query,
                num_results=5,
                deep_crawl=True,
                get_content=True
            )
        else:
            result = {"success": False, "message": "Không cần tìm kiếm thêm", "results": []}
        end_time = time.time()

        # Phân tích kết quả
        output = f"Câu hỏi: {query}\n"
        output += f"Câu trả lời ban đầu: {poor_answer}\n\n"

        # Hiển thị đánh giá chất lượng
        output += "Đánh giá chất lượng câu trả lời:\n"
        output += f"Chất lượng: {quality_result.get('quality_level', 'unknown')}\n"
        output += f"Điểm chất lượng: {quality_result.get('overall_score', 0)}\n"
        output += f"Cần tìm kiếm thêm: {quality_result.get('need_more_search', False)}\n\n"

        # Hiển thị các chỉ số đánh giá
        metrics = quality_result.get('metrics', {})
        for metric_name, metric_data in metrics.items():
            output += f"Chỉ số {metric_name}:\n"
            output += f"  Điểm: {metric_data.get('score', 0)}\n"
            output += f"  Giải thích: {metric_data.get('explanation', '')}\n\n"

        # Hiển thị kết quả tìm kiếm sâu
        output += f"Thời gian tìm kiếm sâu: {end_time - start_time:.2f} giây\n"
        output += f"Thành công: {result.get('success', False)}\n"
        output += f"Số kết quả: {len(result.get('results', []))}\n\n"

        # Hiển thị kết quả tìm kiếm
        output += "Kết quả tìm kiếm sâu:\n"
        for i, res in enumerate(result.get('results', [])[:3]):  # Chỉ hiển thị 3 kết quả đầu tiên
            output += f"Kết quả {i+1}:\n"
            output += f"  Tiêu đề: {res.get('title', '')}\n"
            output += f"  URL: {res.get('url', '')}\n"
            output += f"  Snippet: {res.get('snippet', '')[:100]}...\n"
            output += f"  Source: {res.get('source', '')}\n"

            # Hiển thị thông tin về nội dung đã crawl
            if 'content' in res and res['content']:
                output += f"  Độ dài nội dung: {len(res['content'])} ký tự\n"
                output += f"  Nội dung (trích đoạn): {res['content'][:150]}...\n"

            output += "\n"

        write_test_result(test_name, output)
        self.assertTrue(quality_result["need_more_search"])

if __name__ == "__main__":
    unittest.main()
