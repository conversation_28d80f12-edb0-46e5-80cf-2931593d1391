#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test đơn giản cho các tính năng mới của WebSearchAgentLocal.
"""

import sys
import os
import time
import unittest

# Thêm thư mục hiện tại vào đường dẫn Python
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# Import các module c<PERSON>n thiết
from deepresearch.question_complexity_evaluator import QuestionComplexityEvaluator
from deepresearch.answer_quality_evaluator import AnswerQualityEvaluator
from deepresearch.captcha_handler import CaptchaHandler

class TestQuestionComplexityEvaluator(unittest.TestCase):
    """
    Test cho QuestionComplexityEvaluator.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.evaluator = QuestionComplexityEvaluator(
            complexity_threshold_high=0.7,
            complexity_threshold_medium=0.4,
            use_domain_knowledge=True,
            verbose=True
        )

    def test_simple_question(self):
        """
        Test đánh giá câu hỏi đơn giản.
        """
        query = "What is Python?"
        result = self.evaluator.evaluate_complexity(query)

        print(f"\nĐánh giá câu hỏi đơn giản: {query}")
        print(f"Độ phức tạp: {result.get('complexity_level', 'unknown')}")
        print(f"Điểm phức tạp: {result.get('complexity_score', 0)}")

        # Hiển thị chiến lược đề xuất
        strategy = result.get('recommended_strategy', {})
        print(f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}")
        print(f"Số kết quả đề xuất: {strategy.get('num_results', 5)}")
        print(f"Trích xuất nội dung: {strategy.get('get_content', False)}")
        print(f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}")

        self.assertIn(result["complexity_level"], ["low", "medium"])

    def test_complex_question(self):
        """
        Test đánh giá câu hỏi phức tạp.
        """
        query = "Explain the differences between Python's asyncio, threading, and multiprocessing modules, including their performance characteristics, use cases, and limitations in various scenarios."
        result = self.evaluator.evaluate_complexity(query)

        print(f"\nĐánh giá câu hỏi phức tạp: {query}")
        print(f"Độ phức tạp: {result.get('complexity_level', 'unknown')}")
        print(f"Điểm phức tạp: {result.get('complexity_score', 0)}")

        # Hiển thị chiến lược đề xuất
        strategy = result.get('recommended_strategy', {})
        print(f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}")
        print(f"Số kết quả đề xuất: {strategy.get('num_results', 5)}")
        print(f"Trích xuất nội dung: {strategy.get('get_content', False)}")
        print(f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}")

        self.assertIn(result["complexity_level"], ["medium", "high"])

class TestAnswerQualityEvaluator(unittest.TestCase):
    """
    Test cho AnswerQualityEvaluator.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.evaluator = AnswerQualityEvaluator(
            quality_threshold_high=0.7,
            quality_threshold_medium=0.4,
            use_model_evaluation=False,
            verbose=True
        )

    def test_good_answer(self):
        """
        Test đánh giá câu trả lời tốt.
        """
        question = "What is Python?"
        answer = """
        Python is a high-level, interpreted programming language known for its readability and versatility.
        It was created by Guido van Rossum and first released in 1991. Python supports multiple programming
        paradigms, including procedural, object-oriented, and functional programming. It has a comprehensive
        standard library and a large ecosystem of third-party packages, making it suitable for a wide range
        of applications, from web development to data science and artificial intelligence.
        """
        documents = [
            {
                "title": "Python Programming - Wikipedia",
                "url": "https://en.wikipedia.org/wiki/Python_(programming_language)",
                "snippet": "Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentation.",
                "content": "Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentation. Python is dynamically-typed and garbage-collected. It supports multiple programming paradigms, including structured, object-oriented, and functional programming."
            }
        ]

        result = self.evaluator.evaluate_quality(question, answer, documents)

        print(f"\nĐánh giá câu trả lời tốt:")
        print(f"Chất lượng: {result.get('quality_level', 'unknown')}")
        print(f"Điểm chất lượng: {result.get('overall_score', 0)}")
        print(f"Cần tìm kiếm thêm: {result.get('need_more_search', False)}")

        # Hiển thị các chỉ số đánh giá
        metrics = result.get('metrics', {})
        for metric_name, metric_data in metrics.items():
            print(f"\nChỉ số {metric_name}:")
            print(f"  Điểm: {metric_data.get('score', 0)}")
            print(f"  Giải thích: {metric_data.get('explanation', '')}")

        self.assertIn(result["quality_level"], ["medium", "high"])

    def test_poor_answer(self):
        """
        Test đánh giá câu trả lời kém.
        """
        question = "What is Python?"
        answer = "It's a programming language."
        documents = [
            {
                "title": "Python Programming - Wikipedia",
                "url": "https://en.wikipedia.org/wiki/Python_(programming_language)",
                "snippet": "Python is a high-level, general-purpose programming language.",
                "content": "Python is a high-level, general-purpose programming language."
            }
        ]

        result = self.evaluator.evaluate_quality(question, answer, documents)

        print(f"\nĐánh giá câu trả lời kém:")
        print(f"Chất lượng: {result.get('quality_level', 'unknown')}")
        print(f"Điểm chất lượng: {result.get('overall_score', 0)}")
        print(f"Cần tìm kiếm thêm: {result.get('need_more_search', False)}")

        # Hiển thị các chỉ số đánh giá
        metrics = result.get('metrics', {})
        for metric_name, metric_data in metrics.items():
            print(f"\nChỉ số {metric_name}:")
            print(f"  Điểm: {metric_data.get('score', 0)}")
            print(f"  Giải thích: {metric_data.get('explanation', '')}")

        self.assertIn(result["quality_level"], ["low", "medium"])

class TestCaptchaHandler(unittest.TestCase):
    """
    Test cho CaptchaHandler.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.handler = CaptchaHandler(
            auto_solve=False,
            use_selenium=False,
            max_retries=3,
            retry_delay=5,
            verbose=True
        )

    def test_detect_recaptcha(self):
        """
        Test phát hiện reCAPTCHA.
        """
        html_content = """
        <html>
        <head><title>CAPTCHA Test</title></head>
        <body>
            <div class="g-recaptcha" data-sitekey="6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"></div>
            <script src="https://www.google.com/recaptcha/api.js"></script>
        </body>
        </html>
        """

        has_captcha, captcha_type, captcha_data = self.handler.detect_captcha(html_content)

        print(f"\nPhát hiện reCAPTCHA:")
        print(f"Có CAPTCHA: {has_captcha}")
        print(f"Loại CAPTCHA: {captcha_type}")
        print(f"Dữ liệu CAPTCHA: {captcha_data}")

        self.assertTrue(has_captcha)
        self.assertTrue("recaptcha" in str(captcha_type).lower())

    def test_detect_hcaptcha(self):
        """
        Test phát hiện hCaptcha.
        """
        html_content = """
        <html>
        <head><title>CAPTCHA Test</title></head>
        <body>
            <div class="h-captcha" data-sitekey="10000000-ffff-ffff-ffff-000000000001"></div>
            <script src="https://hcaptcha.com/1/api.js"></script>
        </body>
        </html>
        """

        has_captcha, captcha_type, captcha_data = self.handler.detect_captcha(html_content)

        print(f"\nPhát hiện hCaptcha:")
        print(f"Có CAPTCHA: {has_captcha}")
        print(f"Loại CAPTCHA: {captcha_type}")
        print(f"Dữ liệu CAPTCHA: {captcha_data}")

        self.assertTrue(has_captcha)
        self.assertTrue("hcaptcha" in str(captcha_type).lower())

if __name__ == "__main__":
    unittest.main()
