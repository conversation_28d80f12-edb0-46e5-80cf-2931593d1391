#!/usr/bin/env python3
"""
Test script để kiểm tra real search functionality của WebSearchAgentLocalMerged.
"""

import sys
import os
import json
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_real_search():
    """Test real search functionality."""
    print("🔍 TESTING REAL SEARCH FUNCTIONALITY")
    print("=" * 50)
    
    try:
        # Import agent
        from deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
        
        # Khởi tạo agent với verbose mode
        agent = WebSearchAgentLocalMerged(
            verbose=True,
            use_cache=False,  # Tắt cache để test real search
            timeout=15
        )
        
        print(f"✅ Agent khởi tạo thành công")
        
        # Test queries
        test_queries = [
            "Python programming tutorial",
            "artificial intelligence news 2024", 
            "climate change effects",
            "machine learning algorithms",
            "web scraping best practices"
        ]
        
        results_summary = []
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Test {i}/5: '{query}'")
            print("-" * 40)
            
            try:
                # Thực hiện tìm kiếm
                result = agent.search(
                    query=query,
                    num_results=5,
                    get_content=False,  # Không lấy content để test nhanh hơn
                    evaluate_question=False,
                    evaluate_answer=False
                )
                
                if result and 'results' in result:
                    search_results = result['results']
                    
                    print(f"✅ Tìm thấy {len(search_results)} kết quả")
                    
                    # Hiển thị kết quả
                    for j, res in enumerate(search_results[:3], 1):
                        title = res.get('title', 'No title')[:80]
                        url = res.get('url', 'No URL')
                        source = res.get('source', 'unknown')
                        
                        print(f"  {j}. [{source}] {title}")
                        print(f"     URL: {url}")
                    
                    # Kiểm tra xem có phải mock data không
                    is_real_data = True
                    for res in search_results:
                        if 'example.com' in res.get('url', '') or res.get('source') == 'fallback':
                            is_real_data = False
                            break
                    
                    status = "✅ REAL DATA" if is_real_data else "❌ MOCK/FALLBACK DATA"
                    print(f"     Status: {status}")
                    
                    results_summary.append({
                        'query': query,
                        'num_results': len(search_results),
                        'is_real_data': is_real_data,
                        'sources': [res.get('source', 'unknown') for res in search_results],
                        'first_url': search_results[0].get('url', '') if search_results else ''
                    })
                    
                else:
                    print("❌ Không có kết quả")
                    results_summary.append({
                        'query': query,
                        'num_results': 0,
                        'is_real_data': False,
                        'error': 'No results'
                    })
                    
            except Exception as e:
                print(f"❌ Lỗi: {e}")
                results_summary.append({
                    'query': query,
                    'num_results': 0,
                    'is_real_data': False,
                    'error': str(e)
                })
        
        # Tóm tắt kết quả
        print("\n" + "=" * 50)
        print("📊 SUMMARY")
        print("=" * 50)
        
        total_tests = len(test_queries)
        successful_tests = sum(1 for r in results_summary if r.get('num_results', 0) > 0)
        real_data_tests = sum(1 for r in results_summary if r.get('is_real_data', False))
        
        print(f"Total tests: {total_tests}")
        print(f"Successful searches: {successful_tests}/{total_tests}")
        print(f"Real data (not mock): {real_data_tests}/{total_tests}")
        
        # Thống kê sources
        all_sources = []
        for r in results_summary:
            if 'sources' in r:
                all_sources.extend(r['sources'])
        
        if all_sources:
            from collections import Counter
            source_counts = Counter(all_sources)
            print(f"\nSources used:")
            for source, count in source_counts.most_common():
                print(f"  - {source}: {count} results")
        
        # Lưu kết quả chi tiết
        output_file = f"test_results/real_search_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs('test_results', exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_tests': total_tests,
                    'successful_tests': successful_tests,
                    'real_data_tests': real_data_tests,
                    'success_rate': successful_tests / total_tests * 100,
                    'real_data_rate': real_data_tests / total_tests * 100
                },
                'detailed_results': results_summary
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Kết quả chi tiết đã lưu: {output_file}")
        
        # Kết luận
        if real_data_tests >= total_tests * 0.8:  # 80% success rate
            print("\n🎉 PASS: Real search functionality hoạt động tốt!")
            return True
        else:
            print("\n❌ FAIL: Real search functionality cần cải thiện")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi nghiêm trọng: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_engines():
    """Test từng search engine riêng biệt."""
    print("\n🔧 TESTING INDIVIDUAL SEARCH ENGINES")
    print("=" * 50)
    
    try:
        from deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
        
        agent = WebSearchAgentLocalMerged(verbose=True)
        test_query = "Python programming"
        
        # Test SearXNG
        print("Testing SearXNG...")
        searxng_results = agent._search_with_searxng(test_query, 3)
        print(f"SearXNG: {len(searxng_results)} results")
        
        # Test DuckDuckGo
        print("Testing DuckDuckGo...")
        ddg_results = agent._search_with_duckduckgo(test_query, 3)
        print(f"DuckDuckGo: {len(ddg_results)} results")
        
        # Test Bing (nếu có API key)
        print("Testing Bing...")
        bing_results = agent._search_with_bing(test_query, 3)
        print(f"Bing: {len(bing_results)} results")
        
        # Test Google (nếu có API key)
        print("Testing Google...")
        google_results = agent._search_with_google(test_query, 3)
        print(f"Google: {len(google_results)} results")
        
        total_results = len(searxng_results) + len(ddg_results) + len(bing_results) + len(google_results)
        print(f"\nTotal results from all engines: {total_results}")
        
        return total_results > 0
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

if __name__ == "__main__":
    print("🚀 REAL SEARCH FUNCTIONALITY TEST")
    print("=" * 60)
    
    # Test main search functionality
    main_test_passed = test_real_search()
    
    # Test individual engines
    engines_test_passed = test_search_engines()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS")
    print("=" * 60)
    print(f"Main search test: {'✅ PASS' if main_test_passed else '❌ FAIL'}")
    print(f"Engines test: {'✅ PASS' if engines_test_passed else '❌ FAIL'}")
    
    if main_test_passed and engines_test_passed:
        print("\n🎉 ALL TESTS PASSED! Real search functionality is working!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED. Check the logs above.")
        sys.exit(1)
