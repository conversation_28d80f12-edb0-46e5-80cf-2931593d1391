#!/usr/bin/env python3
"""
Test WebSearchAgentLocalMerged với real search functionality để xác minh TASK 1 assessment.
"""

import sys
import os

# Thêm path để import modules
sys.path.append('/home/<USER>/Desktop/automation-tool/deep_research_core_1')
sys.path.append('/home/<USER>/Desktop/automation-tool/deep_research_core_1/src')

from src.deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged


def test_real_search_capability():
    """Kiểm tra khả năng tìm kiếm thực của WebSearchAgentLocalMerged."""
    
    print("=" * 80)
    print("KIỂM TRA THỰC TẾ WEBSEARCHAGENTLOCALMERGED")
    print("=" * 80)
    
    # Khởi tạo agent
    agent = WebSearchAgentLocalMerged(verbose=True)
    
    # Test 1: Tìm kiếm đơn giản
    print("\n🔍 TEST 1: Tìm kiếm đơn giản")
    print("-" * 50)
    
    query = "Python programming tutorial"
    try:
        result = agent.search(query, num_results=3)
        
        print(f"Query: {query}")
        print(f"Số kết quả: {len(result.get('results', []))}")
        
        for i, res in enumerate(result.get('results', [])[:3], 1):
            print(f"\nKết quả {i}:")
            print(f"  Title: {res.get('title', 'N/A')}")
            print(f"  URL: {res.get('url', 'N/A')}")
            print(f"  Source: {res.get('source', 'N/A')}")
            
            # Kiểm tra xem có phải mock data không
            if "example" in res.get('url', '').lower():
                print(f"  ⚠️  MOCK DATA DETECTED!")
            else:
                print(f"  ✅ REAL URL")
                
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    # Test 2: Kiểm tra _perform_real_search trực tiếp
    print("\n🔍 TEST 2: _perform_real_search trực tiếp")
    print("-" * 50)
    
    try:
        real_results = agent._perform_real_search("artificial intelligence", 3)
        
        print(f"Số kết quả thực: {len(real_results)}")
        
        for i, res in enumerate(real_results[:3], 1):
            print(f"\nKết quả thực {i}:")
            print(f"  Title: {res.get('title', 'N/A')}")
            print(f"  URL: {res.get('url', 'N/A')}")
            print(f"  Source: {res.get('source', 'N/A')}")
            
            # Kiểm tra xem có phải mock data không
            if "example" in res.get('url', '').lower():
                print(f"  ⚠️  MOCK DATA DETECTED!")
            else:
                print(f"  ✅ REAL URL")
                
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    # Test 3: Kiểm tra SearXNG functionality
    print("\n🔍 TEST 3: SearXNG search trực tiếp")
    print("-" * 50)
    
    try:
        searxng_results = agent._search_with_searxng("machine learning", 3)
        
        print(f"Số kết quả SearXNG: {len(searxng_results)}")
        
        for i, res in enumerate(searxng_results[:3], 1):
            print(f"\nKết quả SearXNG {i}:")
            print(f"  Title: {res.get('title', 'N/A')}")
            print(f"  URL: {res.get('url', 'N/A')}")
            print(f"  Source: {res.get('source', 'N/A')}")
            
            # Kiểm tra xem có phải mock data không
            if "example" in res.get('url', '').lower():
                print(f"  ⚠️  MOCK DATA DETECTED!")
            else:
                print(f"  ✅ REAL URL")
                
    except Exception as e:
        print(f"❌ Lỗi SearXNG: {e}")
    
    # Test 4: Kiểm tra DuckDuckGo functionality
    print("\n🔍 TEST 4: DuckDuckGo search trực tiếp")
    print("-" * 50)
    
    try:
        ddg_results = agent._search_with_duckduckgo("deep learning", 3)
        
        print(f"Số kết quả DuckDuckGo: {len(ddg_results)}")
        
        for i, res in enumerate(ddg_results[:3], 1):
            print(f"\nKết quả DuckDuckGo {i}:")
            print(f"  Title: {res.get('title', 'N/A')}")
            print(f"  URL: {res.get('url', 'N/A')}")
            print(f"  Source: {res.get('source', 'N/A')}")
            
            # Kiểm tra xem có phải mock data không
            if "example" in res.get('url', '').lower():
                print(f"  ⚠️  MOCK DATA DETECTED!")
            else:
                print(f"  ✅ REAL URL")
                
    except Exception as e:
        print(f"❌ Lỗi DuckDuckGo: {e}")
    
    # Test 5: Kiểm tra fallback functionality
    print("\n🔍 TEST 5: Fallback search")
    print("-" * 50)
    
    try:
        fallback_results = agent._fallback_search("test query", 3)
        
        print(f"Số kết quả fallback: {len(fallback_results)}")
        
        for i, res in enumerate(fallback_results[:3], 1):
            print(f"\nKết quả fallback {i}:")
            print(f"  Title: {res.get('title', 'N/A')}")
            print(f"  URL: {res.get('url', 'N/A')}")
            print(f"  Source: {res.get('source', 'N/A')}")
            
            # Fallback luôn là mock data
            print(f"  ℹ️  EXPECTED MOCK/FALLBACK DATA")
                
    except Exception as e:
        print(f"❌ Lỗi fallback: {e}")
    
    print("\n" + "=" * 80)
    print("KẾT LUẬN KIỂM TRA")
    print("=" * 80)
    print("1. Hàm search() chính đã được sửa để gọi _perform_real_search()")
    print("2. _perform_real_search() có implementation thật với SearXNG, DuckDuckGo, Bing, Google")
    print("3. Chỉ fallback mới sử dụng mock data")
    print("4. Các search engines thật có thể thất bại do network/API issues")


if __name__ == "__main__":
    test_real_search_capability()
