#!/usr/bin/env python3
"""
Test script để kiểm tra SearXNG local priority trong WebSearchAgentLocalMerged.
"""

import sys
import os
import requests
import time

def check_searxng_local():
    """Kiểm tra SearXNG local có hoạt động không."""
    print("🔍 Kiểm tra SearXNG local...")
    
    local_urls = [
        "http://localhost:8080",
        "http://127.0.0.1:8080", 
        "http://localhost:4000",
        "http://127.0.0.1:4000",
        "http://localhost:8888",
        "http://127.0.0.1:8888"
    ]
    
    working_locals = []
    
    for url in local_urls:
        try:
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                content = response.text.lower()
                if any(indicator in content for indicator in ['searx', 'search', 'opensearch']):
                    print(f"✅ SearXNG local hoạt động: {url}")
                    working_locals.append(url)
                else:
                    print(f"❌ {url}: Không phải SearXNG")
            else:
                print(f"❌ {url}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {url}: {e}")
    
    return working_locals

def test_searxng_search_direct(searxng_url):
    """Test tìm kiếm trực tiếp với SearXNG."""
    print(f"\n🔍 Test tìm kiếm trực tiếp với {searxng_url}...")
    
    try:
        params = {
            'q': 'Python programming',
            'format': 'json',
            'engines': 'google,bing,duckduckgo',
            'safesearch': '1'
        }
        
        response = requests.get(
            f"{searxng_url}/search",
            params=params,
            timeout=10,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            
            print(f"✅ Tìm kiếm thành công: {len(results)} kết quả")
            
            # Hiển thị 2 kết quả đầu
            for i, result in enumerate(results[:2], 1):
                title = result.get('title', 'No title')[:60]
                url = result.get('url', 'No URL')
                engine = result.get('engine', 'unknown')
                print(f"  {i}. [{engine}] {title}")
                print(f"     {url}")
            
            return True
        else:
            print(f"❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def test_agent_searxng_priority():
    """Test SearXNG priority trong WebSearchAgentLocalMerged."""
    print("\n🤖 Test SearXNG priority trong WebSearchAgentLocalMerged...")
    
    try:
        # Add src to path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        # Import agent
        from deep_research_core.agents.web_search_agent_local_merged import WebSearchAgentLocalMerged
        
        # Tạo agent với verbose mode
        agent = WebSearchAgentLocalMerged(verbose=True)
        
        print("✅ Agent khởi tạo thành công")
        
        # Test method _check_searxng_health
        print("\n📋 Test _check_searxng_health method:")
        local_urls = [
            "http://localhost:8080",
            "http://localhost:4000", 
            "http://localhost:8888"
        ]
        
        for url in local_urls:
            is_healthy = agent._check_searxng_health(url)
            status = "✅ Healthy" if is_healthy else "❌ Not available"
            print(f"  {url}: {status}")
        
        # Test method _search_with_searxng
        print("\n🔍 Test _search_with_searxng method:")
        searxng_results = agent._search_with_searxng("Python programming", 3)
        
        if searxng_results:
            print(f"✅ SearXNG search thành công: {len(searxng_results)} kết quả")
            
            # Kiểm tra source của kết quả đầu tiên
            first_result = searxng_results[0]
            source = first_result.get('source', 'unknown')
            url = first_result.get('url', '')
            
            print(f"  Kết quả đầu tiên từ: {source}")
            print(f"  URL: {url}")
            
            # Kiểm tra xem có sử dụng local SearXNG không
            if 'localhost' in str(searxng_results) or '127.0.0.1' in str(searxng_results):
                print("🎉 SearXNG LOCAL được ưu tiên!")
            else:
                print("⚠️  Sử dụng SearXNG public instances")
        else:
            print("❌ SearXNG search thất bại")
        
        # Test full search method
        print("\n🚀 Test full search method:")
        search_result = agent.search(
            query="Python programming tutorial",
            num_results=3,
            get_content=False,
            evaluate_question=False,
            evaluate_answer=False
        )
        
        if search_result and 'results' in search_result:
            results = search_result['results']
            print(f"✅ Full search thành công: {len(results)} kết quả")
            
            # Kiểm tra sources
            sources = [r.get('source', 'unknown') for r in results]
            print(f"  Sources: {', '.join(set(sources))}")
            
            # Kiểm tra có sử dụng SearXNG không
            if 'searxng' in sources:
                print("🎉 SearXNG được sử dụng trong full search!")
            else:
                print("⚠️  SearXNG không được sử dụng trong full search")
        else:
            print("❌ Full search thất bại")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("🚀 SEARXNG LOCAL PRIORITY TEST")
    print("=" * 50)
    
    # 1. Kiểm tra SearXNG local
    working_locals = check_searxng_local()
    
    # 2. Test tìm kiếm trực tiếp nếu có SearXNG local
    if working_locals:
        test_searxng_search_direct(working_locals[0])
    else:
        print("\n⚠️  Không có SearXNG local hoạt động")
    
    # 3. Test agent priority
    agent_test_passed = test_agent_searxng_priority()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    print(f"SearXNG local instances found: {len(working_locals)}")
    if working_locals:
        print(f"Working local URLs: {', '.join(working_locals)}")
    print(f"Agent test: {'✅ PASS' if agent_test_passed else '❌ FAIL'}")
    
    if working_locals and agent_test_passed:
        print("\n🎉 SearXNG LOCAL PRIORITY is working correctly!")
    elif not working_locals:
        print("\n⚠️  No local SearXNG found. Agent will use public instances.")
    else:
        print("\n❌ Agent test failed. Check the implementation.")

if __name__ == "__main__":
    main()
