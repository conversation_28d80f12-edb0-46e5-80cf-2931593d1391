#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test Site Structure Handler Integration
"""

import sys
import os

# Add paths
sys.path.append('.')
sys.path.append('src')

def test_site_structure_handler():
    """Test Site Structure Handler integration."""
    try:
        print("Testing Site Structure Handler integration...", flush=True)

        # Import the crawler
        print("Importing crawler...", flush=True)
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged
        print("✅ Import successful", flush=True)
        
        # Create crawler with Site Structure Handler
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_site_structure_handler=True,
            site_structure_extract_navigation=True,
            site_structure_extract_breadcrumbs=True,
            site_structure_extract_pagination=True,
            site_structure_detect_page_type=True,
            site_structure_detect_site_type=True
        )
        print("✅ Crawler created with Site Structure Handler")
        
        # Test Site Structure Handler status
        status = crawler.get_site_structure_status()
        print(f"Site Structure Handler status: {status}")
        
        # Test page structure analysis
        test_url = "https://example.com"
        test_html = """
        <html>
        <head><title>Test Page</title></head>
        <body>
            <nav>
                <ul>
                    <li><a href="/home">Home</a></li>
                    <li><a href="/about">About</a></li>
                </ul>
            </nav>
            <div class="breadcrumbs">
                <a href="/">Home</a> > <a href="/category">Category</a> > Current Page
            </div>
            <div class="pagination">
                <a href="?page=1">1</a>
                <a href="?page=2">2</a>
                <a href="?page=3">3</a>
            </div>
        </body>
        </html>
        """
        
        # Test page analysis
        page_analysis = crawler.analyze_page_structure(test_url, test_html)
        print(f"Page analysis: {page_analysis}")
        
        # Test navigation extraction
        navigation = crawler.extract_navigation_structure(test_url, test_html)
        print(f"Navigation structure: {navigation}")
        
        # Test breadcrumbs extraction
        breadcrumbs = crawler.extract_breadcrumbs_structure(test_url, test_html)
        print(f"Breadcrumbs structure: {breadcrumbs}")
        
        # Test pagination extraction
        pagination = crawler.extract_pagination_structure(test_url, test_html)
        print(f"Pagination structure: {pagination}")
        
        # Test page type detection
        page_type = crawler.detect_page_type_advanced(test_url, test_html)
        print(f"Page type: {page_type}")
        
        # Test site type detection
        site_type = crawler.detect_site_type_advanced(test_url, test_html)
        print(f"Site type: {site_type}")
        
        # Test site structure mapping
        structure_map = crawler.build_site_structure_map(test_url, max_depth=1, max_urls=5)
        print(f"Site structure map: {structure_map}")
        
        # Test site structure analysis
        structure_analysis = crawler.analyze_site_structure()
        print(f"Site structure analysis: {structure_analysis}")
        
        # Test site patterns
        patterns = crawler.find_site_patterns()
        print(f"Site patterns: {patterns}")
        
        print("✅ All Site Structure Handler tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Site Structure Handler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("SITE STRUCTURE HANDLER INTEGRATION TEST")
    print("=" * 60)
    
    success = test_site_structure_handler()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SITE STRUCTURE HANDLER INTEGRATION SUCCESSFUL!")
        sys.exit(0)
    else:
        print("❌ SITE STRUCTURE HANDLER INTEGRATION FAILED!")
        sys.exit(1)
