"""
Test trích xuất video từ AdaptiveCrawler.

Script này kiểm tra tính năng trích xuất video mới của AdaptiveCrawler,
bao gồm trích xuất từ thẻ video, ifram<PERSON> nhúng từ YouTube, Vimeo, và các nền tảng khác.
"""

import os
import sys
import json
from pathlib import Path

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(str(Path(__file__).parent.parent))

from src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler

# HTML mẫu để kiểm tra trích xuất video
TEST_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>Test Extract Videos</title>
    <base href="https://example.com/">
</head>
<body>
    <div class="content">
        <h1>Test Videos</h1>

        <!-- HTML5 video tag -->
        <video width="640" height="360" controls poster="images/video-poster.jpg">
            <source src="videos/sample.mp4" type="video/mp4">
            <source src="videos/sample.webm" type="video/webm">
            <source src="videos/sample.ogv" type="video/ogg">
            Your browser does not support the video tag.
        </video>

        <!-- Direct video with src attribute -->
        <video src="videos/direct-sample.mp4" width="320" height="240" controls></video>

        <!-- YouTube embed -->
        <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

        <!-- YouTube embed (nocookie) -->
        <iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/dQw4w9WgXcQ" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

        <!-- Vimeo embed -->
        <iframe src="https://player.vimeo.com/video/76979871" width="640" height="360" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>

        <!-- Dailymotion embed -->
        <iframe frameborder="0" width="480" height="270" src="https://www.dailymotion.com/embed/video/x2z8jjy" allowfullscreen allow="autoplay"></iframe>

        <!-- Facebook embed -->
        <iframe src="https://www.facebook.com/plugins/video.php?height=314&href=https%3A%2F%2Fwww.facebook.com%2Ffacebook%2Fvideos%2F10153231379946729%2F&show_text=false&width=560&t=0" width="560" height="314" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowfullscreen="true" allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share" allowFullScreen="true"></iframe>

        <!-- Generic video player iframe -->
        <iframe src="https://example.com/video-player?id=12345" width="480" height="270" frameborder="0"></iframe>
    </div>
</body>
</html>
"""

def test_extract_videos():
    """Kiểm tra phương thức _extract_videos của AdaptiveCrawler."""
    # Khởi tạo AdaptiveCrawler với download_media=True
    download_path = os.path.join(os.path.dirname(__file__), "test_downloads")
    crawler = AdaptiveCrawler(
        download_media=True,
        download_path=download_path,
        max_media_size_mb=10
    )

    # URL mẫu để kiểm tra
    test_url = "https://example.com/test-page.html"

    # Gọi phương thức _extract_videos
    videos = crawler._extract_videos(test_url, TEST_HTML)

    # In kết quả
    print(f"Đã trích xuất {len(videos)} video:")
    for i, video in enumerate(videos, 1):
        print(f"{i}. {video['url']} (Type: {video.get('source_type', 'unknown')})")

    # Lưu kết quả ra file JSON để kiểm tra
    result_file = os.path.join(os.path.dirname(__file__), "test_results", "extracted_videos.json")
    os.makedirs(os.path.dirname(result_file), exist_ok=True)

    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(videos, f, indent=2, ensure_ascii=False)

    print(f"\nĐã lưu kết quả vào: {result_file}")

    # Kiểm tra các loại video đã trích xuất
    video_types = set(video.get('source_type', 'unknown') for video in videos)
    print(f"\nCác loại video đã trích xuất: {', '.join(video_types)}")

    # Kiểm tra số lượng video theo loại
    type_counts = {}
    for video in videos:
        video_type = video.get('source_type', 'unknown')
        type_counts[video_type] = type_counts.get(video_type, 0) + 1

    for video_type, count in type_counts.items():
        print(f"- {video_type}: {count} video")

    return videos

if __name__ == "__main__":
    videos = test_extract_videos()

    # Kiểm tra kết quả
    assert len(videos) >= 5, f"Phải trích xuất ít nhất 5 video, nhưng chỉ tìm thấy {len(videos)}"

    # Kiểm tra các loại video
    video_types = set(video.get('source_type', 'unknown') for video in videos)
    # Lưu ý: HTML5 video không được trích xuất khi không có Playwright Page
    # assert 'html5' in video_types, "Không tìm thấy video từ thẻ HTML5"
    assert 'youtube' in video_types, "Không tìm thấy video từ YouTube"
    assert 'vimeo' in video_types, "Không tìm thấy video từ Vimeo"
    assert 'dailymotion' in video_types, "Không tìm thấy video từ Dailymotion"
    assert 'facebook' in video_types, "Không tìm thấy video từ Facebook"

    print("\nKiểm tra thành công!")
