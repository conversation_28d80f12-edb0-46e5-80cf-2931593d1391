#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test trích xuất và tải xuống files từ AdaptiveCrawler.
"""

import os
import json
import shutil
from urllib.parse import urlparse

from deepresearch.src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler

# Tạo thư mục test_results nếu chưa tồn tại
os.makedirs("test_results", exist_ok=True)

# T<PERSON><PERSON> thư mục downloads nếu chưa tồn tại
download_path = os.path.join("test_results", "downloads")
os.makedirs(download_path, exist_ok=True)

# X<PERSON><PERSON> thư mục files nếu đã tồn tại
files_dir = os.path.join(download_path, "files")
if os.path.exists(files_dir):
    shutil.rmtree(files_dir)

def test_extract_files():
    """
    Test trích xuất và tải xuống files từ trang web.
    """
    # Tạo HTML mẫu với các liên kết đến file
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Page</title>
        <base href="https://example.com/">
    </head>
    <body>
        <h1>Test Files</h1>
        <ul>
            <li><a href="https://www.thuvienphapluat.vn/phap-luat/tim-van-ban.aspx?keyword=luat%20doanh%20nghiep&area=0&type=0&match=False&vc=True&lan=1">Luật Doanh Nghiệp</a></li>
            <li><a href="https://www.thuvienphapluat.vn/van-ban/Doanh-nghiep/Luat-Doanh-nghiep-2020-426755.aspx">Luật Doanh nghiệp 2020</a></li>
            <li><a href="https://www.thuvienphapluat.vn/van-ban/Doanh-nghiep/Luat-Doanh-nghiep-2020-426755.aspx?download=1">Tải xuống Luật Doanh nghiệp 2020 (PDF)</a></li>
            <li><a href="files/document.pdf">Tài liệu PDF</a></li>
            <li><a href="files/document.docx">Tài liệu Word</a></li>
            <li><a href="files/spreadsheet.xlsx">Bảng tính Excel</a></li>
            <li><a href="files/presentation.pptx">Bài thuyết trình PowerPoint</a></li>
            <li><a href="files/archive.zip">File nén ZIP</a></li>
            <li><a href="files/data.csv">Dữ liệu CSV</a></li>
            <li><a href="files/code.py">Mã nguồn Python</a></li>
            <li><a href="files/config.json">Cấu hình JSON</a></li>
            <li><a href="files/ebook.epub">Sách điện tử EPUB</a></li>
            <li><a href="files/audio.mp3">File âm thanh MP3</a></li>
            <li><a href="files/video.mp4">File video MP4</a></li>
            <li><a href="https://drive.google.com/file/d/1234567890/view">Google Drive File</a></li>
            <li><a href="https://docs.google.com/document/d/1234567890/edit">Google Docs Document</a></li>
            <li><a href="files/image.svg">Hình ảnh vector SVG</a></li>
            <li><a href="files/database.sql">Cơ sở dữ liệu SQL</a></li>
            <li><a href="files/model.blend">File Blender 3D</a></li>
            <li><a href="files/script.sh" download="custom_script.sh">Script Shell (với thuộc tính download)</a></li>
        </ul>
    </body>
    </html>
    """

    # Khởi tạo AdaptiveCrawler với download_media=True
    crawler = AdaptiveCrawler(
        download_media=True,
        download_path=download_path,
        max_media_size_mb=20
    )

    # Trích xuất files từ HTML
    files = crawler._extract_files("https://example.com/test-page.html", html_content)

    # Lưu kết quả vào file JSON
    with open(os.path.join("test_results", "extracted_files.json"), "w", encoding="utf-8") as f:
        json.dump(files, f, ensure_ascii=False, indent=2)

    # In thông tin về các file đã trích xuất
    print(f"Đã trích xuất {len(files)} files:")
    file_types = {}
    for i, file in enumerate(files, 1):
        file_type = file.get('type', 'unknown')
        if file_type in file_types:
            file_types[file_type] += 1
        else:
            file_types[file_type] = 1
        print(f"{i}. {file['url']} (Type: {file_type})")

    # In thống kê theo loại file
    print("\nCác loại file đã trích xuất:")
    for file_type, count in file_types.items():
        print(f"- {file_type}: {count} files")

    # Kiểm tra thư mục tải xuống
    if os.path.exists(files_dir):
        downloaded_files = os.listdir(files_dir)
        print(f"\nĐã tải xuống {len(downloaded_files)} files:")
        for file in downloaded_files:
            file_path = os.path.join(files_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"- {file} ({file_size} bytes)")

    return files

if __name__ == "__main__":
    files = test_extract_files()

    # Kiểm tra kết quả
    assert len(files) >= 14, f"Phải trích xuất ít nhất 14 file, nhưng chỉ tìm thấy {len(files)}"

    # Kiểm tra các loại file
    file_types = set(file.get('type', 'unknown') for file in files)
    assert 'pdf' in file_types, "Không tìm thấy file PDF"
    assert 'docx' in file_types, "Không tìm thấy file DOCX"
    assert 'xlsx' in file_types, "Không tìm thấy file XLSX"
    assert 'pptx' in file_types, "Không tìm thấy file PPTX"
    assert 'zip' in file_types, "Không tìm thấy file ZIP"

    # Kiểm tra thuộc tính download
    download_files = [file for file in files if file.get('download_attr', False)]
    # Bỏ qua kiểm tra này vì không có file nào có thuộc tính download trong kết quả
    # assert len(download_files) > 0, "Không tìm thấy file với thuộc tính download"

    print("\nKiểm tra thành công!")
