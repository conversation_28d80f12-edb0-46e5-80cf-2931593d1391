#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kiểm tra đơn giản đ<PERSON> xác nhận vi<PERSON><PERSON> merge thành công.
"""

import os
import sys
import json
from datetime import datetime

# Thông tin về các phiên bản đã merge
web_search_files = [
    "src/deep_research_core/agents/web_search_agent_local.py",
    "src/deep_research_core/agents/web_search_agent_local_minimal.py",
    "src/deep_research_core/agents/web_search_agent_local_consolidated.py",
    "src/deep_research_core/agents/web_search_agent_local_merged.py"
]

def check_file_exists(file_path):
    """Kiểm tra xem file có tồn tại không."""
    if os.path.exists(file_path):
        size = os.path.getsize(file_path)
        last_modified = datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
        return {
            "exists": True,
            "size": f"{size / 1024:.2f} KB",
            "last_modified": last_modified
        }
    else:
        return {"exists": False}

def get_file_summary(file_path):
    """Lấy thông tin tóm tắt về file."""
    if not os.path.exists(file_path):
        return {"exists": False}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        total_lines = content.count('\n') + 1
        empty_lines = content.count('\n\n') + content.count('\r\n\r\n')
        import_lines = len([line for line in content.split('\n') if line.strip().startswith('import ') or line.strip().startswith('from ')])
        
        # Tìm tên lớp trong file
        class_matches = [line for line in content.split('\n') if 'class ' in line]
        classes = [line.split('class ')[1].split('(')[0].strip() for line in class_matches if 'class ' in line]
        
        # Kiểm tra có lỗi cú pháp không
        try:
            compile(content, file_path, 'exec')
            has_syntax_error = False
        except SyntaxError:
            has_syntax_error = True
        
        return {
            "exists": True,
            "total_lines": total_lines,
            "empty_lines": empty_lines,
            "import_lines": import_lines,
            "classes": classes,
            "has_syntax_error": has_syntax_error,
            "file_size": f"{os.path.getsize(file_path) / 1024:.2f} KB"
        }
    except Exception as e:
        return {
            "exists": True,
            "error": str(e)
        }

def main():
    """Hàm chính để kiểm tra các file."""
    print("=== KIỂM TRA VIỆC MERGE CÁC PHIÊN BẢN WEBSEARCHAGENT ===\n")
    
    results = {}
    for file_path in web_search_files:
        print(f"Kiểm tra: {file_path}")
        file_info = get_file_summary(file_path)
        
        if file_info["exists"]:
            if "error" in file_info:
                print(f"  - LỖI: {file_info['error']}")
            else:
                print(f"  - Số dòng: {file_info['total_lines']}")
                print(f"  - Kích thước: {file_info['file_size']}")
                print(f"  - Lớp: {', '.join(file_info['classes'])}")
                print(f"  - Lỗi cú pháp: {'Có' if file_info['has_syntax_error'] else 'Không'}")
        else:
            print(f"  - Không tồn tại")
        
        print("")
        results[file_path] = file_info
    
    # Lưu kết quả ra file
    with open('merge_check_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"Đã lưu kết quả vào file merge_check_results.json")
    
    # Kết luận
    merged_file = "src/deep_research_core/agents/web_search_agent_local_merged.py"
    if results[merged_file]["exists"] and not results[merged_file].get("has_syntax_error", False):
        print("\n✅ MERGE THÀNH CÔNG: File hợp nhất đã được tạo và không có lỗi cú pháp.")
    else:
        print("\n❌ MERGE THẤT BẠI: File hợp nhất không tồn tại hoặc có lỗi cú pháp.")

if __name__ == "__main__":
    main() 