"""
Test trích xuất hình ảnh từ AdaptiveCrawler.

Script này kiểm tra tính năng trích xuất hình ảnh mới của AdaptiveCrawler,
bao gồm trích xuất từ thẻ img, CSS background-image, và thẻ picture/source.
"""

import os
import sys
import json
from pathlib import Path

# Thêm thư mục gốc vào sys.path để import các module
sys.path.append(str(Path(__file__).parent.parent))

from src.deep_research_core.agents.adaptive_crawler import AdaptiveCrawler

# HTML mẫu để kiểm tra trích xuất hình ảnh
TEST_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>Test Extract Images</title>
    <base href="https://example.com/">
    <style>
        .header {
            background-image: url('images/header-bg.jpg');
        }
        .footer {
            background-image: url('/images/footer-bg.png');
        }
        .sidebar {
            background: url("https://example.com/images/sidebar-bg.gif") no-repeat;
        }
    </style>
</head>
<body>
    <div class="header">Header with background image</div>

    <div class="content">
        <h1>Test Images</h1>

        <!-- Regular img tags -->
        <img src="images/image1.jpg" alt="Image 1" width="300" height="200">
        <img src="/images/image2.png" alt="Image 2" title="Second Image" width="400" height="300">
        <img src="https://example.com/images/image3.webp" alt="Image 3">

        <!-- Small icon to be filtered -->
        <img src="images/icon.png" alt="Small Icon" width="16" height="16">

        <!-- Picture tag with source -->
        <picture>
            <source srcset="images/large.jpg 1200w, images/medium.jpg 800w" media="(min-width: 800px)">
            <source srcset="images/small.jpg" media="(min-width: 480px)">
            <img src="images/fallback.jpg" alt="Responsive Image">
        </picture>
    </div>

    <div class="footer">Footer with background image</div>
</body>
</html>
"""

def test_extract_images():
    """Kiểm tra phương thức _extract_images của AdaptiveCrawler."""
    # Khởi tạo AdaptiveCrawler với download_media=True
    download_path = os.path.join(os.path.dirname(__file__), "test_downloads")
    crawler = AdaptiveCrawler(
        download_media=True,
        download_path=download_path,
        max_media_size_mb=10
    )

    # URL mẫu để kiểm tra
    test_url = "https://example.com/test-page.html"

    # Gọi phương thức _extract_images
    images = crawler._extract_images(test_url, TEST_HTML)

    # In kết quả
    print(f"Đã trích xuất {len(images)} hình ảnh:")
    for i, img in enumerate(images, 1):
        print(f"{i}. {img['url']} (Type: {img.get('source_type', 'unknown')})")

    # Lưu kết quả ra file JSON để kiểm tra
    result_file = os.path.join(os.path.dirname(__file__), "test_results", "extracted_images.json")
    os.makedirs(os.path.dirname(result_file), exist_ok=True)

    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(images, f, indent=2, ensure_ascii=False)

    print(f"\nĐã lưu kết quả vào: {result_file}")

    # Kiểm tra các loại hình ảnh đã trích xuất
    img_types = set(img.get('source_type', 'unknown') for img in images)
    print(f"\nCác loại hình ảnh đã trích xuất: {', '.join(img_types)}")

    # Kiểm tra số lượng hình ảnh theo loại
    type_counts = {}
    for img in images:
        img_type = img.get('source_type', 'unknown')
        type_counts[img_type] = type_counts.get(img_type, 0) + 1

    for img_type, count in type_counts.items():
        print(f"- {img_type}: {count} hình ảnh")

    return images

if __name__ == "__main__":
    images = test_extract_images()

    # Kiểm tra kết quả
    assert len(images) >= 8, f"Phải trích xuất ít nhất 8 hình ảnh, nhưng chỉ tìm thấy {len(images)}"

    # Kiểm tra các loại hình ảnh
    img_types = set(img.get('source_type', 'unknown') for img in images)
    assert 'html' in img_types, "Không tìm thấy hình ảnh từ thẻ HTML"
    assert 'css' in img_types, "Không tìm thấy hình ảnh từ CSS"
    assert 'picture' in img_types, "Không tìm thấy hình ảnh từ thẻ picture"

    print("\nKiểm tra thành công!")
