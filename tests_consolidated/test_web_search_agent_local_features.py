#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test tổng hợp cho các t<PERSON>h năng mới của WebSearchAgentLocal.
"""

import os
import sys
import unittest
import time
import json
import logging
from unittest.mock import patch, MagicMock

# Thêm thư mục hiện tại vào đường dẫn Python
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# Import các module cần thiết
import sys
from deepresearch.question_complexity_evaluator import QuestionComplexityEvaluator
from deepresearch.answer_quality_evaluator import AnswerQualityEvaluator
from deepresearch.captcha_handler import CaptchaHandler
from deepresearch.web_search_agent_local import WebSearchAgentLocal

# Đảm bảo các module có thể được import
sys.modules['captcha_handler'] = sys.modules['deepresearch.captcha_handler']
sys.modules['question_complexity_evaluator'] = sys.modules['deepresearch.question_complexity_evaluator']
sys.modules['answer_quality_evaluator'] = sys.modules['deepresearch.answer_quality_evaluator']

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestQuestionComplexityEvaluator(unittest.TestCase):
    """
    Test cho QuestionComplexityEvaluator.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.evaluator = QuestionComplexityEvaluator(
            complexity_threshold_high=0.7,
            complexity_threshold_medium=0.4,
            use_domain_knowledge=True,
            verbose=True
        )

    def test_simple_question(self):
        """
        Test đánh giá câu hỏi đơn giản.
        """
        query = "What is Python?"
        result = self.evaluator.evaluate_complexity(query)

        print(f"\nĐánh giá câu hỏi đơn giản: {query}")
        print(f"Độ phức tạp: {result.get('complexity_level', 'unknown')}")
        print(f"Điểm phức tạp: {result.get('complexity_score', 0)}")

        # Hiển thị chiến lược đề xuất
        strategy = result.get('recommended_strategy', {})
        print(f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}")
        print(f"Số kết quả đề xuất: {strategy.get('num_results', 5)}")
        print(f"Trích xuất nội dung: {strategy.get('get_content', False)}")
        print(f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}")

        self.assertIn(result["complexity_level"], ["low", "medium"])

    def test_complex_question(self):
        """
        Test đánh giá câu hỏi phức tạp.
        """
        query = "Explain the differences between Python's asyncio, threading, and multiprocessing modules, including their performance characteristics, use cases, and limitations in various scenarios."
        result = self.evaluator.evaluate_complexity(query)

        print(f"\nĐánh giá câu hỏi phức tạp: {query}")
        print(f"Độ phức tạp: {result.get('complexity_level', 'unknown')}")
        print(f"Điểm phức tạp: {result.get('complexity_score', 0)}")

        # Hiển thị chiến lược đề xuất
        strategy = result.get('recommended_strategy', {})
        print(f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}")
        print(f"Số kết quả đề xuất: {strategy.get('num_results', 5)}")
        print(f"Trích xuất nội dung: {strategy.get('get_content', False)}")
        print(f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}")

        self.assertIn(result["complexity_level"], ["medium", "high"])

    def test_vietnamese_question(self):
        """
        Test đánh giá câu hỏi tiếng Việt.
        """
        query = "Python là gì và tại sao nó được sử dụng rộng rãi trong lĩnh vực khoa học dữ liệu?"
        result = self.evaluator.evaluate_complexity(query)

        print(f"\nĐánh giá câu hỏi tiếng Việt: {query}")
        print(f"Độ phức tạp: {result.get('complexity_level', 'unknown')}")
        print(f"Điểm phức tạp: {result.get('complexity_score', 0)}")

        # Hiển thị chiến lược đề xuất
        strategy = result.get('recommended_strategy', {})
        print(f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}")
        print(f"Số kết quả đề xuất: {strategy.get('num_results', 5)}")
        print(f"Trích xuất nội dung: {strategy.get('get_content', False)}")
        print(f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}")

        self.assertIn(result["complexity_level"], ["low", "medium", "high"])

class TestAnswerQualityEvaluator(unittest.TestCase):
    """
    Test cho AnswerQualityEvaluator.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.evaluator = AnswerQualityEvaluator(
            quality_threshold_high=0.7,
            quality_threshold_medium=0.4,
            use_model_evaluation=False,
            verbose=True
        )

    def test_good_answer(self):
        """
        Test đánh giá câu trả lời tốt.
        """
        question = "What is Python?"
        answer = """
        Python is a high-level, interpreted programming language known for its readability and versatility.
        It was created by Guido van Rossum and first released in 1991. Python supports multiple programming
        paradigms, including procedural, object-oriented, and functional programming. It has a comprehensive
        standard library and a large ecosystem of third-party packages, making it suitable for a wide range
        of applications, from web development to data science and artificial intelligence.
        """
        documents = [
            {
                "title": "Python Programming - Wikipedia",
                "url": "https://en.wikipedia.org/wiki/Python_(programming_language)",
                "snippet": "Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentation.",
                "content": "Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentation. Python is dynamically-typed and garbage-collected. It supports multiple programming paradigms, including structured, object-oriented, and functional programming."
            }
        ]

        result = self.evaluator.evaluate_quality(question, answer, documents)

        print(f"\nĐánh giá câu trả lời tốt:")
        print(f"Chất lượng: {result.get('quality_level', 'unknown')}")
        print(f"Điểm chất lượng: {result.get('overall_score', 0)}")
        print(f"Cần tìm kiếm thêm: {result.get('need_more_search', False)}")

        # Hiển thị các chỉ số đánh giá
        metrics = result.get('metrics', {})
        for metric_name, metric_data in metrics.items():
            print(f"\nChỉ số {metric_name}:")
            print(f"  Điểm: {metric_data.get('score', 0)}")
            print(f"  Giải thích: {metric_data.get('explanation', '')}")

        self.assertIn(result["quality_level"], ["medium", "high"])
        self.assertFalse(result["need_more_search"])

    def test_poor_answer(self):
        """
        Test đánh giá câu trả lời kém.
        """
        question = "What is Python?"
        answer = "It's a programming language."
        documents = [
            {
                "title": "Python Programming - Wikipedia",
                "url": "https://en.wikipedia.org/wiki/Python_(programming_language)",
                "snippet": "Python is a high-level, general-purpose programming language.",
                "content": "Python is a high-level, general-purpose programming language."
            }
        ]

        result = self.evaluator.evaluate_quality(question, answer, documents)

        print(f"\nĐánh giá câu trả lời kém:")
        print(f"Chất lượng: {result.get('quality_level', 'unknown')}")
        print(f"Điểm chất lượng: {result.get('overall_score', 0)}")
        print(f"Cần tìm kiếm thêm: {result.get('need_more_search', False)}")

        # Hiển thị các chỉ số đánh giá
        metrics = result.get('metrics', {})
        for metric_name, metric_data in metrics.items():
            print(f"\nChỉ số {metric_name}:")
            print(f"  Điểm: {metric_data.get('score', 0)}")
            print(f"  Giải thích: {metric_data.get('explanation', '')}")

        self.assertIn(result["quality_level"], ["low", "medium"])
        self.assertTrue(result["need_more_search"])

    def test_vietnamese_answer(self):
        """
        Test đánh giá câu trả lời tiếng Việt.
        """
        question = "Python là gì?"
        answer = """
        Python là một ngôn ngữ lập trình bậc cao, được thiết kế với triết lý nhấn mạnh vào khả năng đọc code.
        Python hỗ trợ nhiều mô hình lập trình, bao gồm lập trình hướng đối tượng, lập trình hàm và lập trình
        thủ tục. Nó có một thư viện chuẩn toàn diện và một hệ sinh thái lớn các gói bên thứ ba, làm cho nó
        phù hợp với nhiều ứng dụng, từ phát triển web đến khoa học dữ liệu và trí tuệ nhân tạo.
        """
        documents = [
            {
                "title": "Python - Wikipedia tiếng Việt",
                "url": "https://vi.wikipedia.org/wiki/Python_(ngôn_ngữ_lập_trình)",
                "snippet": "Python là một ngôn ngữ lập trình bậc cao, được thiết kế với triết lý nhấn mạnh vào khả năng đọc code.",
                "content": "Python là một ngôn ngữ lập trình bậc cao, được thiết kế với triết lý nhấn mạnh vào khả năng đọc code. Python hỗ trợ nhiều mô hình lập trình, bao gồm lập trình hướng đối tượng, lập trình hàm và lập trình thủ tục."
            }
        ]

        result = self.evaluator.evaluate_quality(question, answer, documents)

        print(f"\nĐánh giá câu trả lời tiếng Việt:")
        print(f"Chất lượng: {result.get('quality_level', 'unknown')}")
        print(f"Điểm chất lượng: {result.get('overall_score', 0)}")
        print(f"Cần tìm kiếm thêm: {result.get('need_more_search', False)}")

        # Hiển thị các chỉ số đánh giá
        metrics = result.get('metrics', {})
        for metric_name, metric_data in metrics.items():
            print(f"\nChỉ số {metric_name}:")
            print(f"  Điểm: {metric_data.get('score', 0)}")
            print(f"  Giải thích: {metric_data.get('explanation', '')}")

        self.assertIn(result["quality_level"], ["medium", "high"])

class TestCaptchaHandler(unittest.TestCase):
    """
    Test cho CaptchaHandler.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.handler = CaptchaHandler(
            auto_solve=False,
            use_selenium=False,
            max_retries=3,
            retry_delay=5,
            verbose=True
        )

    def test_detect_recaptcha(self):
        """
        Test phát hiện reCAPTCHA.
        """
        html_content = """
        <html>
        <head><title>CAPTCHA Test</title></head>
        <body>
            <div class="g-recaptcha" data-sitekey="6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"></div>
            <script src="https://www.google.com/recaptcha/api.js"></script>
        </body>
        </html>
        """

        has_captcha, captcha_type, captcha_data = self.handler.detect_captcha(html_content)

        print(f"\nPhát hiện reCAPTCHA:")
        print(f"Có CAPTCHA: {has_captcha}")
        print(f"Loại CAPTCHA: {captcha_type}")
        print(f"Dữ liệu CAPTCHA: {captcha_data}")

        self.assertTrue(has_captcha)
        self.assertTrue("recaptcha" in str(captcha_type).lower())

    def test_detect_hcaptcha(self):
        """
        Test phát hiện hCaptcha.
        """
        html_content = """
        <html>
        <head><title>CAPTCHA Test</title></head>
        <body>
            <div class="h-captcha" data-sitekey="10000000-ffff-ffff-ffff-000000000001"></div>
            <script src="https://hcaptcha.com/1/api.js"></script>
        </body>
        </html>
        """

        has_captcha, captcha_type, captcha_data = self.handler.detect_captcha(html_content)

        print(f"\nPhát hiện hCaptcha:")
        print(f"Có CAPTCHA: {has_captcha}")
        print(f"Loại CAPTCHA: {captcha_type}")
        print(f"Dữ liệu CAPTCHA: {captcha_data}")

        self.assertTrue(has_captcha)
        self.assertTrue("hcaptcha" in str(captcha_type).lower())

    def test_detect_image_captcha(self):
        """
        Test phát hiện CAPTCHA hình ảnh.
        """
        html_content = """
        <html>
        <head><title>CAPTCHA Test</title></head>
        <body>
            <form action="/verify" method="post">
                <img src="/captcha.jpg" alt="CAPTCHA" id="captcha-image">
                <input type="text" name="captcha" placeholder="Enter CAPTCHA">
                <button type="submit">Submit</button>
            </form>
        </body>
        </html>
        """

        has_captcha, captcha_type, captcha_data = self.handler.detect_captcha(html_content)

        print(f"\nPhát hiện CAPTCHA hình ảnh:")
        print(f"Có CAPTCHA: {has_captcha}")
        print(f"Loại CAPTCHA: {captcha_type}")
        print(f"Dữ liệu CAPTCHA: {captcha_data}")

        self.assertTrue(has_captcha)
        self.assertTrue("image" in str(captcha_type).lower())

class TestWebSearchAgentLocal(unittest.TestCase):
    """
    Test cho WebSearchAgentLocal.
    """

    def setUp(self):
        """
        Thiết lập trước mỗi test.
        """
        self.agent = WebSearchAgentLocal(
            verbose=True,
            captcha_handler_config={
                "auto_solve": False,
                "use_selenium": False,
                "max_retries": 3,
                "retry_delay": 5
            },
            question_evaluator_config={
                "complexity_threshold_high": 0.7,
                "complexity_threshold_medium": 0.4,
                "use_domain_knowledge": True
            },
            answer_evaluator_config={
                "quality_threshold_high": 0.7,
                "quality_threshold_medium": 0.4,
                "use_model_evaluation": False
            }
        )

    def test_evaluate_question_complexity(self):
        """
        Test phương thức evaluate_question_complexity.
        """
        # Thực hiện đánh giá câu hỏi
        query = "What is Python?"
        result = self.agent.evaluate_question_complexity(query)

        # Kiểm tra kết quả
        print(f"\nĐánh giá câu hỏi: {query}")
        if isinstance(result, dict) and "complexity_level" in result:
            print(f"Độ phức tạp: {result.get('complexity_level', 'unknown')}")
            print(f"Điểm phức tạp: {result.get('complexity_score', 0)}")

            # Hiển thị chiến lược đề xuất
            strategy = result.get('recommended_strategy', {})
            print(f"Chiến lược đề xuất: {strategy.get('search_method', 'auto')}")
            print(f"Số kết quả đề xuất: {strategy.get('num_results', 5)}")
            print(f"Trích xuất nội dung: {strategy.get('get_content', False)}")
            print(f"Tìm kiếm sâu: {strategy.get('deep_crawl', False)}")
        else:
            print(f"Kết quả: {result}")

        # Kiểm tra kết quả cơ bản
        self.assertIsInstance(result, dict)

    def test_evaluate_answer_quality(self):
        """
        Test phương thức evaluate_answer_quality.
        """
        # Chuẩn bị dữ liệu
        question = "What is Python?"
        answer = """
        Python is a high-level, interpreted programming language known for its readability and versatility.
        It was created by Guido van Rossum and first released in 1991. Python supports multiple programming
        paradigms, including procedural, object-oriented, and functional programming. It has a comprehensive
        standard library and a large ecosystem of third-party packages, making it suitable for a wide range
        of applications, from web development to data science and artificial intelligence.
        """
        documents = [
            {
                "title": "Python Programming - Wikipedia",
                "url": "https://en.wikipedia.org/wiki/Python_(programming_language)",
                "snippet": "Python is a high-level, general-purpose programming language.",
                "content": "Python is a high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentation. Python is dynamically-typed and garbage-collected. It supports multiple programming paradigms, including structured, object-oriented, and functional programming."
            }
        ]

        # Thực hiện đánh giá câu trả lời
        result = self.agent.evaluate_answer_quality(question, answer, documents)

        # Kiểm tra kết quả
        print(f"\nĐánh giá câu trả lời cho: {question}")
        if isinstance(result, dict):
            if "quality_level" in result:
                print(f"Chất lượng: {result.get('quality_level', 'unknown')}")
                print(f"Điểm chất lượng: {result.get('overall_score', 0)}")
                print(f"Cần tìm kiếm thêm: {result.get('need_more_search', False)}")

                # Hiển thị các chỉ số đánh giá
                metrics = result.get('metrics', {})
                for metric_name, metric_data in metrics.items():
                    print(f"\nChỉ số {metric_name}:")
                    print(f"  Điểm: {metric_data.get('score', 0)}")
                    print(f"  Giải thích: {metric_data.get('explanation', '')}")
            else:
                print(f"Kết quả: {result}")
        else:
            print(f"Kết quả: {result}")

        # Kiểm tra kết quả cơ bản
        self.assertIsInstance(result, dict)

    def test_handle_captcha(self):
        """
        Test phương thức handle_captcha.
        """
        # Chuẩn bị dữ liệu
        url = "https://example.com/captcha"
        html_content = """
        <html>
        <head><title>CAPTCHA Test</title></head>
        <body>
            <div class="g-recaptcha" data-sitekey="6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"></div>
            <script src="https://www.google.com/recaptcha/api.js"></script>
        </body>
        </html>
        """

        # Thực hiện xử lý CAPTCHA
        result = self.agent.handle_captcha(url, html_content)

        # Kiểm tra kết quả
        print(f"\nXử lý CAPTCHA cho URL: {url}")
        if isinstance(result, dict):
            print(f"Thành công: {result.get('success', False)}")
            print(f"Thông báo: {result.get('message', '')}")
            print(f"Loại CAPTCHA: {result.get('captcha_type', 'unknown')}")
        else:
            print(f"Kết quả: {result}")

        # Kiểm tra kết quả cơ bản
        self.assertIsInstance(result, dict)

if __name__ == "__main__":
    unittest.main()
